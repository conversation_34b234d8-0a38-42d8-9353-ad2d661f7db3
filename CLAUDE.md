# CLAUDE.md

此文件为 Claude Code (claude.ai/code) 在此代码库中工作时提供指导。

## 经验教训：
- 永远把：添加数据流追踪日志作为修改代码和生产代码的第一原则！
- 代码文件中不要添加 emoji 等特殊字符，避免造成编码错误。
- 你觉得重复的地方，一定要进行多角度核实、评估。
- 完善新架构的功能实现，彻底移除旧架构的内容，完全不要考虑所谓的新旧架构兼容与冲突问题！
- 永远不要让文件名和目录名相同。
- 遇到导入错误要先检查模块结构，而不是盲目修改导入语句。
- 设计项目结构时要考虑Python的模块解析规则。
- 代码文件代码行数不应该太多。

## 项目概览

这是一个**月度工资异动处理系统** - 使用 Python 3.12、PyQt5、SQLite 和 pandas 构建的Windows桌面应用程序。该系统处理工资数据导入、检测变化并为用户生成报告。

## 构建和开发命令

### 激活powershell虚拟环境
在运行测试脚本时，要确保激活虚拟环境：
../.venv/Scripts/Activate.ps1
或者
E:\project\case\salary_changes\.venv\Scripts\Activate.ps1

如果无法激活，就采用下面形式直接运行脚本
"E:\project\case\salary_changes\.venv\Scripts\python.exe" main.py




### 环境设置
```bash
# 创建并激活虚拟环境
python -m venv salary_system_env
salary_system_env\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt

# 验证安装
python -c "import PyQt5; import pandas; import openpyxl; print('所有依赖已成功安装!')"
```

### 运行应用程序
```bash
# 正常启动
python main.py

# 运行测试
python main.py --test
# 或者
pytest -v test/

# 检查环境和依赖
python main.py --check

# 显示版本
python main.py --version
```

### 测试
```bash
# 运行所有测试并生成覆盖率报告
pytest -v test/ --cov=src --cov-report=html

# 运行特定测试模块
pytest test/test_core_modules_coverage.py
pytest test/test_gui_main_window_comprehensive.py
pytest test/test_data_import.py

# 性能测试
python test/performance_test.py
```

### 代码质量
```bash
# 格式化代码
black src/

# 代码检查
flake8 src/
```

### 构建
```bash
# Windows 快速构建
build.bat

# 手动 PyInstaller 构建
python build_config.py
```

## 高层架构

### 系统架构（4层设计）
系统采用**面向服务的MVC**架构，职责分离清晰：

**1. 表现层（GUI）**
- `src/gui/` - PyQt5 UI 组件
- 主入口：`src/gui/prototype/prototype_main_window.py`（重构后的现代UI）
- 遗留版本：`src/gui/main_window.py`（原始实现）

**2. 应用层**
- `src/core/application_service.py` - 中央编排服务
- `src/core/application_state_service.py` - 集中状态管理
- `src/core/config_manager.py` - 配置管理

**3. 业务层**
- `src/modules/data_import/` - Excel/CSV 导入和字段映射
- `src/modules/change_detection/` - 工资变动检测算法
- `src/modules/report_generation/` - 模板驱动的报告生成
- `src/modules/system_config/` - 系统配置和用户偏好

**4. 数据层**
- `src/modules/data_storage/` - 数据库管理和动态表创建
- SQLite 数据库，针对不同工资表类型的动态模式

### 关键设计模式
- **MVC 模式**：模型-视图-控制器分离
- **服务层**：ApplicationService 协调所有业务操作
- **策略模式**：多种工资计算和匹配策略
- **工厂模式**：报告生成器和数据导入器
- **观察者模式**：通过 PyQt 信号实现事件驱动状态更新

### 数据流
1. **数据导入**：Excel 文件 → 字段映射 → 数据库存储
2. **变动检测**：基准比较 → 算法分析 → 变动识别
3. **报告生成**：模板选择 → 数据合并 → 输出生成

### 模块依赖关系
```
GUI 层 → 应用服务 → 业务模块 → 数据存储
             ↓
         配置管理器
             ↓
        日志和工具
```

## 重要实现细节

### 数据库模式
- **动态表**：系统根据导入的 Excel 结构创建表
- **字段映射**：可配置的中英文字段名映射
- **专用模板**：4种不同结构的工资表类型

### 性能优化
- **虚拟化表格**：高效处理 1000+ 行
- **分页**：可配置的行限制和缓存
- **异步处理**：大数据集的非阻塞操作
- **内存管理**：针对大型 Excel 文件处理的优化

### 配置系统
- `config.json` - 主系统配置
- `user_preferences.json` - 用户特定设置
- `state/` 目录 - 会话和导航状态持久化
- 字段映射存储在 `state/data/field_mappings.json`

### 测试策略
- **GUI 测试**：PyQt 测试自动化框架
- **单元测试**：核心业务逻辑覆盖（总体 81%）
- **集成测试**：端到端工作流测试
- **性能测试**：大数据集处理基准

### 日志
- 使用 `loguru` 的结构化日志
- 日志文件在 `logs/` 目录
- 通过 `psutil` 进行性能监控

## 当前开发阶段
系统处于 **ARCHIVE** 阶段 - 生产就绪，具有完整文档。最近的重大重构包括从 PyQt5 遗留版本迁移到现代响应式 UI 模式，以及 Python 3.9→3.12 升级以改善 Windows 兼容性。最近还进行了架构迁移，移除了老架构，全部迁移到新架构。

## 文档位置约定
- 如无特别说明，统一以当前项目根目录作为起始目录。
- 项目需求文档，如无特别说明，以独立markdown格式文档统一放置在项目根目录下的 docs/prd 目录中，以简体中文命名文件。
- 项目说明文档、功能文档、架构图、依赖关系等文档，如无特别说明，以独立markdown格式文档统一放置在项目根目录下的 docs/draft 目录中，以简体中文命名文件。
- 项目问题记录等文档，如无特别说明，以独立markdown格式文档统一放置在项目根目录下的 docs/problems 目录中，以简体中文命名文件。
- 项目高保真原型图相关文档，如无特别说明，统一放置在项目根目录下的 docs/design 目录中。
- 测试文件，统一放置在项目根目录下的 test 目录中。
- 临时文件，统一放置在项目根目录下的 temp 目录中。
- 示例文档，统一放置在项目根目录下的 docs/examples 目录中。
- 输入文件（数据文件），统一放置在项目根目录下的 data 目录中。
- 输出文件，统一放置在项目根目录下的 output 目录中。
- 日志文件，统一放置在项目根目录下的 logs 目录中。
- 部署文档，统一放置在项目根目录下的 deploy 目录中。


## 🚨 重要提醒：日志分析最佳实践

**永远记住：2025-07-14 的严重失误教训**

### 日志分析必须遵循的标准流程

1. **第一步：系统性错误扫描** ⚠️
   ```bash
   # 必须先完整扫描所有ERROR级别日志
   grep -n "ERROR" logs/salary_system.log
   grep -n "🔧.*失败" logs/salary_system.log  
   grep -n "Exception" logs/salary_system.log
   ```

2. **第二步：错误分类和优先级排序** 📋
   - **P0 - 系统启动失败**：阻止系统正常启动的错误
   - **P1 - 核心功能异常**：影响主要业务流程的错误（如数据导入、排序失效）
   - **P2 - 信号连接失败**：PyQt信号槽连接问题
   - **P3 - 方法缺失**：'object has no attribute' 类错误
   - **P4 - 配置加载问题**：配置文件或路径相关错误

3. **第三步：按优先级逐一解决** ✅
   - 绝不能跳过ERROR级别去直接分析功能问题
   - 每个ERROR都必须有明确的修复方案
   - 修复一个ERROR后重新测试确认

4. **第四步：WARNING级别审查** ⚠️
   - 检查是否有潜在的业务逻辑问题
   - 确认是否会影响用户体验

### 反面教训：绝不能犯的错误

❌ **错误做法**：
- 看到排序功能问题就直接跳到业务逻辑分析
- 忽视ERROR级别的信号连接失败
- 选择性地只看"相关"的日志条目

✅ **正确做法**：
- 首先扫描全部ERROR，建立完整问题清单
- 按严重程度排序，优先解决阻塞性问题
- 系统性地逐一解决，确保没有遗漏

### 专业标准

**作为专业的系统分析和修复，必须做到：**
- 🔍 **全面性**：不遗漏任何ERROR级别问题
- 📊 **系统性**：按优先级有序处理
- 🎯 **精准性**：每个问题都有明确的根因分析
- ✅ **完整性**：修复后验证解决效果

**这个教训提醒我：技术分析必须严谨、系统、专业。任何"选择性忽视"都是不可接受的低级错误。**