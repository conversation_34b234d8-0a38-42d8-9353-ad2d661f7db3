#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复异动表元数据缺失问题
为现有的异动表补充元数据记录
"""

import sqlite3
import os
from datetime import datetime

def fix_change_metadata():
    """修复异动表元数据的table_type字段"""
    db_path = "data/db/salary_system.db"

    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return False

    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        print("🔍 检查现有异动表元数据...")

        # 检查table_metadata表结构
        cursor.execute("PRAGMA table_info(table_metadata);")
        columns = cursor.fetchall()
        print("📋 table_metadata表结构:")
        for col in columns:
            print(f"  - {col[1]} ({col[2]})")

        # 查找table_type为unknown的异动表记录
        cursor.execute("SELECT id, table_name, table_type FROM table_metadata WHERE table_name LIKE 'change_data_%';")
        change_metadata = cursor.fetchall()

        if not change_metadata:
            print("❌ 未找到任何异动表元数据记录")
            return False

        print(f"📊 找到 {len(change_metadata)} 个异动表元数据记录:")
        for row in change_metadata:
            print(f"  - ID:{row[0]} {row[1]} (类型:{row[2]})")

        # 修复table_type字段
        fixed_count = 0
        for row in change_metadata:
            record_id, table_name, table_type = row

            if table_type != 'change_data':
                # 更新table_type为change_data
                cursor.execute(
                    "UPDATE table_metadata SET table_type='change_data', updated_at=? WHERE id=?",
                    (datetime.now().isoformat(), record_id)
                )
                print(f"✅ 修复元数据: {table_name} 类型从 '{table_type}' 改为 'change_data'")
                fixed_count += 1
            else:
                print(f"⏭️  跳过已正确的元数据: {table_name}")

        # 提交更改
        conn.commit()
        print(f"\n🎉 成功修复 {fixed_count} 个元数据记录")

        # 验证结果
        cursor.execute("SELECT table_name, table_type FROM table_metadata WHERE table_name LIKE 'change_data_%';")
        updated_metadata = cursor.fetchall()
        print(f"📊 验证：现在有 {len(updated_metadata)} 个异动表元数据:")
        for row in updated_metadata:
            print(f"  - {row[0]} (类型:{row[1]})")

        # 检查change_data类型的记录数
        cursor.execute("SELECT COUNT(*) FROM table_metadata WHERE table_type='change_data';")
        change_data_count = cursor.fetchone()[0]
        print(f"🎯 table_type='change_data'的记录总数: {change_data_count}")

        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 修复过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 开始修复异动表元数据...")
    success = fix_change_metadata()
    if success:
        print("✅ 修复完成！现在导航面板应该能显示异动表了。")
    else:
        print("❌ 修复失败，请检查错误信息。")
