#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化的表格渲染器
遵循CLAUDE.md要求：数据流追踪日志，高性能渲染策略
"""

import time
from typing import List, Dict, Any
from PyQt5.QtWidgets import QTableWidgetItem, QApplication
from PyQt5.QtCore import Qt
from src.utils.thread_safe_timer import safe_single_shot

from src.utils.log_config import setup_logger


class OptimizedTableRenderer:
    """
    优化的表格渲染器
    
    根据数据集大小采用不同的渲染策略，大幅提升表格渲染性能
    遵循CLAUDE.md原则：数据流追踪日志 + 性能优化
    """
    
    def __init__(self):
        """初始化优化渲染器"""
        self.logger = setup_logger(self.__class__.__name__)
        self.logger.info("优化表格渲染器初始化完成")
    
    @staticmethod
    def render_small_dataset(table_widget, data: List[Dict], headers: List[str]) -> float:
        """
        小数据集专用渲染器（≤75条）
        
        采用批量渲染策略：
        1. 禁用UI自动更新
        2. 预分配空间
        3. 批量创建items
        4. 一次性启用更新
        
        Args:
            table_widget: QTableWidget实例
            data: 行数据列表
            headers: 表头列表
            
        Returns:
            渲染耗时(毫秒)
        """
        start_time = time.time()
        logger = setup_logger('OptimizedTableRenderer')
        
        logger.info(f"[数据流追踪] 开始小数据集渲染: {len(data)}行 x {len(headers)}列")
        
        # 🔧 [P1-数据流优化] 空数据快速返回
        if len(data) == 0:
            logger.info("[数据流追踪] 小数据集渲染：检测到空数据，快速返回")
            table_widget.setRowCount(0)
            table_widget.setColumnCount(len(headers) if headers else 0)
            # 🔧 [P0-修复] 设置表头，避免显示数字
            if headers:
                table_widget.setHorizontalHeaderLabels(headers)
            return 0.0  # 0ms耗时
        
        # 关键优化1: 禁用UI自动更新和信号
        table_widget.setUpdatesEnabled(False)
        table_widget.blockSignals(True)
        
        try:
            # 关键优化2: 预分配空间，减少动态调整开销
            table_widget.setRowCount(len(data))
            table_widget.setColumnCount(len(headers))
            
            # 🔧 [P0-修复] 设置表头
            if headers:
                table_widget.setHorizontalHeaderLabels(headers)
            
            logger.debug(f"[数据流追踪] 表格空间预分配完成: {len(data)}行 x {len(headers)}列")
            
            # 关键优化3: 批量创建items，减少逐个setItem的开销
            items_created = 0
            
            for row_idx, row_data in enumerate(data):
                for col_idx, header in enumerate(headers):
                    # 获取单元格值
                    value = row_data.get(header, "")
                    
                    # 创建表格项
                    item = QTableWidgetItem(str(value) if value is not None else "")
                    
                    # 🔧 [P1-2修复] 增强的数字类型判断和右对齐
                    if OptimizedTableRenderer._is_numeric_value(value, value):
                        item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                    else:
                        item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
                    
                    # 设置到表格
                    table_widget.setItem(row_idx, col_idx, item)
                    items_created += 1
            
            logger.debug(f"[数据流追踪] 批量创建表格项完成: {items_created}个")
                
        except Exception as e:
            logger.error(f"小数据集渲染过程出错: {e}")
            raise
        finally:
            # 关键优化4: 一次性启用更新，触发单次重绘
            table_widget.blockSignals(False)
            table_widget.setUpdatesEnabled(True)
            
        elapsed_ms = (time.time() - start_time) * 1000
        avg_per_row = elapsed_ms/len(data) if len(data) > 0 else 0
        logger.info(f"[数据流追踪] 小数据集渲染完成: 耗时{elapsed_ms:.1f}ms, "
                   f"平均每行{avg_per_row:.2f}ms")
        
        # 记录性能度量
        OptimizedTableRenderer._record_performance_metrics(
            operation_type='render',
            data_size=len(data),
            render_time_ms=elapsed_ms,
            strategy_used='small_dataset'
        )
        
        return elapsed_ms
    
    @staticmethod
    def render_paginated_dataset(table_widget, data: List[Dict], headers: List[str]) -> float:
        """
        分页数据集渲染器（>75条）
        
        采用增量渲染策略：
        1. 分批处理，每批20行
        2. 批次间允许界面响应
        3. 避免界面卡死
        
        Args:
            table_widget: QTableWidget实例
            data: 行数据列表
            headers: 表头列表
            
        Returns:
            渲染耗时(毫秒)
        """
        start_time = time.time()
        logger = setup_logger('OptimizedTableRenderer')
        
        logger.info(f"[数据流追踪] 开始分页数据集渲染: {len(data)}行 x {len(headers)}列")
        
        # 🔧 [P1-数据流优化] 空数据快速返回
        if len(data) == 0:
            logger.info("[数据流追踪] 分页数据集渲染：检测到空数据，快速返回")
            table_widget.setRowCount(0)
            table_widget.setColumnCount(len(headers) if headers else 0)
            # 🔧 [P0-修复] 设置表头，避免显示数字
            if headers:
                table_widget.setHorizontalHeaderLabels(headers)
            return 0.0  # 0ms耗时
        
        # 分批处理参数
        BATCH_SIZE = 20
        batches_count = (len(data) + BATCH_SIZE - 1) // BATCH_SIZE
        
        logger.debug(f"[数据流追踪] 分页渲染策略: {batches_count}批次，每批{BATCH_SIZE}行")
        
        # 预分配空间
        table_widget.setRowCount(len(data))
        table_widget.setColumnCount(len(headers))
        
        # 🔧 [P0-修复] 设置表头
        if headers:
            table_widget.setHorizontalHeaderLabels(headers)
        
        # 禁用更新，但不禁用信号（允许批次间响应）
        table_widget.setUpdatesEnabled(False)
        
        try:
            items_created = 0
            
            # 分批渲染
            for batch_idx in range(batches_count):
                batch_start = batch_idx * BATCH_SIZE
                batch_end = min(batch_start + BATCH_SIZE, len(data))
                batch_data = data[batch_start:batch_end]
                
                logger.debug(f"[数据流追踪] 处理第{batch_idx + 1}/{batches_count}批次: "
                           f"行{batch_start + 1}-{batch_end}")
                
                # 处理当前批次
                for i, row_data in enumerate(batch_data):
                    row_idx = batch_start + i
                    for col_idx, header in enumerate(headers):
                        value = row_data.get(header, "")
                        item = QTableWidgetItem(str(value) if value is not None else "")
                        
                        # 🔧 [P1-2修复] 增强的数字类型判断和右对齐
                        if OptimizedTableRenderer._is_numeric_value(value, value):
                            item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                        else:
                            item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
                        
                        table_widget.setItem(row_idx, col_idx, item)
                        items_created += 1
                
                # 让事件循环自然调度，避免主动事件泵导致重入
                
                # 每5批次记录一次进度
                if (batch_idx + 1) % 5 == 0:
                    progress = ((batch_idx + 1) / batches_count) * 100
                    logger.debug(f"[数据流追踪] 渲染进度: {progress:.1f}% ({batch_idx + 1}/{batches_count}批次)")
                
        except Exception as e:
            logger.error(f"分页数据集渲染过程出错: {e}")
            raise
        finally:
            # 启用更新
            table_widget.setUpdatesEnabled(True)
        
        elapsed_ms = (time.time() - start_time) * 1000
        avg_per_row = elapsed_ms/len(data) if len(data) > 0 else 0
        logger.info(f"[数据流追踪] 分页数据集渲染完成: 耗时{elapsed_ms:.1f}ms, "
                   f"平均每行{avg_per_row:.2f}ms, "
                   f"总计{items_created}个表格项")
        
        # 记录性能度量
        OptimizedTableRenderer._record_performance_metrics(
            operation_type='render',
            data_size=len(data),
            render_time_ms=elapsed_ms,
            strategy_used='paginated_dataset'
        )
        
        return elapsed_ms
    
    @staticmethod
    def render_with_auto_strategy(table_widget, data: List[Dict], headers: List[str]) -> Dict[str, Any]:
        """
        自动选择渲染策略
        
        根据数据集大小自动选择最优渲染策略
        
        Args:
            table_widget: QTableWidget实例
            data: 行数据列表
            headers: 表头列表
            
        Returns:
            渲染结果信息
        """
        logger = setup_logger('OptimizedTableRenderer')
        
        data_size = len(data)
        
        logger.info(f"[数据流追踪] 自动渲染策略选择: {data_size}行数据")
        
        # 🔧 [P0-修复] 空数据快速处理路径 - 保持表头
        if data_size == 0:
            logger.info("[数据流追踪] 检测到空数据，使用快速清空策略")
            start_time = time.time()
            table_widget.setRowCount(0)
            table_widget.setColumnCount(len(headers) if headers else 0)
            
            # 🔧 [P0-修复] 空表时也要设置表头，避免显示数字
            if headers:
                table_widget.setHorizontalHeaderLabels(headers)
                logger.info(f"🔧 [P0-修复] 空数据集设置表头: {len(headers)}个")
            
            elapsed_ms = (time.time() - start_time) * 1000
            
            return {
                'strategy': 'empty_data',
                'data_size': 0,
                'elapsed_ms': elapsed_ms,
                'performance_rating': 'excellent'
            }
        
        if data_size <= 75:
            # 小数据集策略
            elapsed_ms = OptimizedTableRenderer.render_small_dataset(table_widget, data, headers)
            strategy = 'small_dataset'
            logger.info(f"[数据流追踪] 选择小数据集渲染策略")
        else:
            # 分页数据集策略
            elapsed_ms = OptimizedTableRenderer.render_paginated_dataset(table_widget, data, headers)
            strategy = 'paginated_dataset'
            logger.info(f"[数据流追踪] 选择分页数据集渲染策略")
        
        return {
            'strategy': strategy,
            'data_size': data_size,
            'elapsed_ms': elapsed_ms,
            'performance_rating': OptimizedTableRenderer._calculate_performance_rating(data_size, elapsed_ms)
        }
    
    @staticmethod
    def _calculate_performance_rating(data_size: int, elapsed_ms: float) -> str:
        """
        计算性能评级
        
        Args:
            data_size: 数据行数
            elapsed_ms: 渲染耗时(毫秒)
            
        Returns:
            性能评级字符串
        """
        # 计算每行平均耗时
        avg_ms_per_row = elapsed_ms / data_size if data_size > 0 else 0
        
        if avg_ms_per_row <= 3:
            return 'excellent'  # 优秀 - 每行≤3ms
        elif avg_ms_per_row <= 5:
            return 'good'       # 良好 - 每行≤5ms
        elif avg_ms_per_row <= 8:
            return 'fair'       # 一般 - 每行≤8ms
        else:
            return 'poor'       # 较差 - 每行>8ms
    
    @staticmethod
    def clear_table_efficiently(table_widget) -> float:
        """
        高效清空表格
        
        Args:
            table_widget: QTableWidget实例
            
        Returns:
            清空耗时(毫秒)
        """
        start_time = time.time()
        logger = setup_logger('OptimizedTableRenderer')
        
        logger.debug("[数据流追踪] 开始高效清空表格")
        
        # 禁用更新
        table_widget.setUpdatesEnabled(False)
        
        try:
            # 清空内容但保持结构
            table_widget.clearContents()
            
            # 重置行列数
            table_widget.setRowCount(0)
            table_widget.setColumnCount(0)
            
        finally:
            # 重新启用更新
            table_widget.setUpdatesEnabled(True)
        
        elapsed_ms = (time.time() - start_time) * 1000
        logger.debug(f"[数据流追踪] 表格清空完成: 耗时{elapsed_ms:.1f}ms")
        
        return elapsed_ms
    
    @staticmethod
    def _is_numeric_value(original_value, formatted_value) -> bool:
        """
        🔧 [P0-1修复] 判断一个值是否为数字类型，应该右对齐显示
        """
        try:
            # 1. 原始值是数字类型
            if isinstance(original_value, (int, float)):
                return True
                
            # 2. 格式化后的值是数字字符串
            if isinstance(formatted_value, str) and formatted_value.strip():
                # 去除千分位分隔符和货币符号
                clean_value = formatted_value.replace(',', '').replace('￥', '').replace('$', '').strip()
                try:
                    float(clean_value)
                    return True
                except ValueError:
                    pass
                    
            # 3. 原始值是数字字符串
            if isinstance(original_value, str) and original_value.strip():
                try:
                    float(original_value.replace(',', ''))
                    return True
                except ValueError:
                    pass
                    
            return False
            
        except Exception:
            return False

    @staticmethod
    def render_with_state_preservation(table_widget, data: List[Dict], headers: List[str], 
                                     state_manager=None, table_name: str = "") -> Dict[str, Any]:
        """
        🔧 [P0-4修复] 带状态保护的表格渲染方法
        
        在渲染前保存UI状态，渲染后恢复，确保列宽等设置不丢失
        
        Args:
            table_widget: QTableWidget实例
            data: 行数据列表
            headers: 表头列表
            state_manager: 状态管理器（可选）
            table_name: 表名（用于状态缓存）
            
        Returns:
            渲染结果信息
        """
        logger = setup_logger('OptimizedTableRenderer')
        
        # 第1步：保存当前UI状态
        saved_state = None
        if state_manager and table_name:
            try:
                if hasattr(state_manager, 'get_table_state'):
                    saved_state = state_manager.get_table_state(table_widget)
                    logger.debug(f"🔧 [P0-4修复] 已保存渲染前状态: {table_name}")
            except Exception as e:
                logger.warning(f"🔧 [P0-4修复] 保存渲染前状态失败: {e}")
        
        # 第2步：执行正常渲染
        try:
            render_result = OptimizedTableRenderer.render_with_auto_strategy(
                table_widget, data, headers
            )
            logger.info(f"🔧 [P0-4修复] 渲染完成: {render_result['data_size']}行")
        except Exception as e:
            logger.error(f"🔧 [P0-4修复] 渲染失败: {e}")
            raise
        
        # 第3步：恢复UI状态
        if saved_state and state_manager and table_name:
            try:
                # 延迟恢复，确保渲染完全完成
                safe_single_shot(50, lambda: OptimizedTableRenderer._restore_state_after_render(
                    table_widget, saved_state, state_manager, table_name
                ))
                logger.debug(f"🔧 [P0-4修复] 状态恢复已安排: {table_name}")
            except Exception as e:
                logger.warning(f"🔧 [P0-4修复] 安排状态恢复失败: {e}")
        
        # 第4步：返回扩展的渲染结果
        render_result['state_preserved'] = saved_state is not None
        render_result['table_name'] = table_name
        
        return render_result
    
    @staticmethod
    def _restore_state_after_render(table_widget, saved_state, state_manager, table_name: str):
        """🔧 [P0-4修复] 渲染后恢复状态的辅助方法"""
        try:
            logger = setup_logger('OptimizedTableRenderer')
            
            if hasattr(state_manager, 'apply_table_state'):
                state_manager.apply_table_state(table_widget, saved_state)
                logger.info(f"🔧 [P0-4修复] 渲染后状态恢复成功: {table_name}")
            else:
                logger.warning("🔧 [P0-4修复] 状态管理器缺少apply_table_state方法")
                
        except Exception as e:
            logger.error(f"🔧 [P0-4修复] 渲染后状态恢复失败 {table_name}: {e}")

    @staticmethod
    def _record_performance_metrics(operation_type: str, data_size: int, 
                                   render_time_ms: float, strategy_used: str,
                                   table_name: str = ""):
        """记录性能度量到全局收集器"""
        try:
            from src.core.performance_metrics_collector import get_performance_metrics_collector, PerformanceMetrics
            from datetime import datetime
            
            collector = get_performance_metrics_collector()
            
            metrics = PerformanceMetrics(
                operation_type=operation_type,
                data_size=data_size,
                render_time_ms=render_time_ms,
                strategy_used=strategy_used,
                user_wait_time_ms=render_time_ms,  # 对于渲染操作，等待时间等于渲染时间
                timestamp=datetime.now(),
                table_name=table_name,
                additional_info={
                    'avg_ms_per_row': render_time_ms / data_size if data_size > 0 else 0
                }
            )
            
            collector.record_operation(metrics)
            
        except ImportError:
            # 如果性能收集器不可用，静默忽略
            pass
        except Exception as e:
            logger = setup_logger('OptimizedTableRenderer')
            logger.debug(f"记录性能度量失败: {e}")


class RenderingPerformanceMonitor:
    """渲染性能监控器"""
    
    def __init__(self):
        """初始化性能监控器"""
        self.logger = setup_logger(self.__class__.__name__)
        self._performance_history = []
    
    def record_performance(self, data_size: int, elapsed_ms: float, strategy: str):
        """记录性能数据"""
        record = {
            'timestamp': time.time(),
            'data_size': data_size,
            'elapsed_ms': elapsed_ms,
            'strategy': strategy,
            'avg_ms_per_row': elapsed_ms / data_size if data_size > 0 else 0
        }
        
        self._performance_history.append(record)
        
        # 保持最近100条记录
        if len(self._performance_history) > 100:
            self._performance_history = self._performance_history[-100:]
        
        self.logger.debug(f"[性能监控] 记录渲染性能: {data_size}行, {elapsed_ms:.1f}ms, {strategy}")
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        if not self._performance_history:
            return {'message': '暂无性能数据'}
        
        # 计算统计信息
        total_records = len(self._performance_history)
        avg_render_time = sum(r['elapsed_ms'] for r in self._performance_history) / total_records if total_records > 0 else 0
        avg_data_size = sum(r['data_size'] for r in self._performance_history) / total_records if total_records > 0 else 0
        
        # 策略分布
        strategy_count = {}
        for record in self._performance_history:
            strategy = record['strategy']
            strategy_count[strategy] = strategy_count.get(strategy, 0) + 1
        
        return {
            'total_renders': total_records,
            'avg_render_time_ms': round(avg_render_time, 1),
            'avg_data_size': round(avg_data_size, 1),
            'strategy_distribution': strategy_count,
            'latest_performance': self._performance_history[-1] if self._performance_history else None
        }
