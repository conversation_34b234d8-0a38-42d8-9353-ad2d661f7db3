"""
统一状态管理器 - 架构重构核心组件

解决排序状态、分页状态、字段映射状态分别管理的问题。
提供统一的状态管理接口和状态同步机制。

主要功能：
1. 统一状态存储和管理
2. 状态变更监听和通知
3. 状态持久化和恢复
4. 跨组件状态同步

架构目标：
- 消除状态不一致问题
- 提供可预测的状态变更
- 支持状态回滚和历史记录
"""

from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime
import threading
import json
from pathlib import Path

from src.utils.log_config import setup_logger
from src.utils.logging_utils import bind_context, log_throttle, log_sample, redact


class StateChangeType(Enum):
    """状态变更类型"""
    SORT_CHANGED = "sort_changed"
    PAGE_CHANGED = "page_changed"
    FILTER_CHANGED = "filter_changed"
    FIELD_MAPPING_CHANGED = "field_mapping_changed"
    TABLE_SWITCHED = "table_switched"
    DATA_RELOADED = "data_reloaded"


@dataclass
class StateChange:
    """状态变更记录"""
    change_type: StateChangeType
    table_name: str
    old_value: Any
    new_value: Any
    timestamp: datetime = field(default_factory=datetime.now)
    change_id: str = field(default_factory=lambda: f"change_{datetime.now().timestamp()}")


@dataclass
class TableState:
    """表级状态"""
    table_name: str
    
    # 分页状态
    current_page: int = 1
    page_size: int = 50
    total_records: int = 0
    
    # 排序状态
    sort_columns: List[Dict[str, Any]] = field(default_factory=list)
    
    # 过滤状态
    active_filters: Dict[str, Any] = field(default_factory=dict)
    
    # 字段映射状态
    field_mapping: Dict[str, str] = field(default_factory=dict)
    custom_field_order: List[str] = field(default_factory=list)
    
    # 选择状态
    selected_rows: List[int] = field(default_factory=list)
    expanded_rows: List[int] = field(default_factory=list)
    
    # 🔧 [P0-2修复] UI状态属性
    column_widths: Dict[str, int] = field(default_factory=dict)
    selected_fields: List[str] = field(default_factory=list)
    
    # 元数据
    last_loaded: Optional[datetime] = None
    data_version: int = 1
    is_dirty: bool = False  # 是否有未保存的更改
    
    # 状态变更标记
    sort_changed: bool = False
    page_changed: bool = False
    filter_changed: bool = False
    field_mapping_changed: bool = False
    
    def mark_dirty(self, change_type: StateChangeType):
        """标记状态为脏"""
        self.is_dirty = True
        
        if change_type == StateChangeType.SORT_CHANGED:
            self.sort_changed = True
        elif change_type == StateChangeType.PAGE_CHANGED:
            self.page_changed = True
        elif change_type == StateChangeType.FILTER_CHANGED:
            self.filter_changed = True
        elif change_type == StateChangeType.FIELD_MAPPING_CHANGED:
            self.field_mapping_changed = True
    
    def clear_change_flags(self):
        """清除变更标记"""
        self.sort_changed = False
        self.page_changed = False
        self.filter_changed = False
        self.field_mapping_changed = False
        self.is_dirty = False
    
    def update(self, state_update: Dict[str, Any]):
        """更新状态"""
        for key, value in state_update.items():
            if hasattr(self, key):
                setattr(self, key, value)


@dataclass
class GlobalState:
    """全局状态"""
    current_table: Optional[str] = None
    current_tab: Optional[str] = None  # 新增：当前导航Tab ('wage'|'change')
    active_tables: List[str] = field(default_factory=list)
    navigation_path: List[str] = field(default_factory=list)
    
    # UI状态
    window_geometry: Dict[str, int] = field(default_factory=dict)
    splitter_positions: Dict[str, List[int]] = field(default_factory=dict)
    menu_visibility: bool = False
    
    # 用户偏好
    default_page_size: int = 50
    preferred_date_format: str = "%Y-%m-%d"
    auto_save_enabled: bool = True
    
    # 系统状态
    last_activity: datetime = field(default_factory=datetime.now)
    session_id: str = field(default_factory=lambda: f"session_{datetime.now().timestamp()}")


class StateListener:
    """状态监听器接口"""
    
    def on_table_state_changed(self, table_name: str, table_state: TableState, change: StateChange):
        """表状态变更回调"""
        pass
    
    def on_global_state_changed(self, global_state: GlobalState, change: StateChange):
        """全局状态变更回调"""
        pass


class UnifiedStateManager:
    """统一的状态管理系统"""
    
    def __init__(self, state_file_path: Optional[str] = None):
        base_logger = setup_logger("UnifiedStateManager")
        self.logger = bind_context(base_logger, component="USM")
        
        # 状态存储
        self.table_states: Dict[str, TableState] = {}
        self.global_state = GlobalState()
        
        # 监听器管理
        self.state_listeners: List[StateListener] = []
        
        # 状态变更历史
        self._change_history: List[StateChange] = []
        
        # 线程安全
        self._lock = threading.RLock()
        
        # 持久化配置
        self.state_file_path = state_file_path or "state/unified_state.json"
        
        # 自动保存配置
        self.auto_save_enabled = True
        self.auto_save_interval = 30  # 秒
        
        # 加载已保存的状态
        self._load_state()
        
        self.logger.info("统一状态管理器初始化完成")
    
    def get_table_state(self, table_name: str) -> TableState:
        """获取表状态"""
        with self._lock:
            if table_name not in self.table_states:
                self.table_states[table_name] = TableState(table_name=table_name)
                self.logger.debug(f"创建新的表状态: {table_name}")
            return self.table_states[table_name]
    
    def update_table_state(self, table_name: str, state_update: Dict[str, Any], change_type: StateChangeType = None):
        """
        更新表状态
        
        Args:
            table_name: 表名
            state_update: 状态更新字典
            change_type: 变更类型
        """
        with self._lock:
            table_state = self.get_table_state(table_name)
            old_state = self._serialize_table_state(table_state)
            
            # 更新状态
            table_state.update(state_update)
            
            if change_type:
                table_state.mark_dirty(change_type)
            
            # 创建变更记录
            change = StateChange(
                change_type=change_type or StateChangeType.DATA_RELOADED,
                table_name=table_name,
                old_value=old_state,
                new_value=self._serialize_table_state(table_state)
            )
            
            # 添加到历史记录
            self._add_change_to_history(change)
            
            # 通知监听器
            self._notify_table_state_changed(table_name, table_state, change)
            
            # 自动保存
            if self.auto_save_enabled:
                self._save_state()
            
            if log_throttle('usm-update', 1.0):
                safe_table = redact(table_name)
                self.logger.info(f"表状态已更新: {safe_table}, 变更类型: {change_type}")
    
    def save_table_state_by_name(self, table_name: str, state_data: dict) -> bool:
        """🔧 [P0-1修复] 按表名保存表格状态
        
        Args:
            table_name: 表名
            state_data: 要保存的状态数据（列宽、排序等UI状态）
            
        Returns:
            bool: 保存是否成功
        """
        try:
            with self._lock:
                table_state = self.get_table_state(table_name)
                
                # 保存UI相关状态
                if 'column_widths' in state_data:
                    table_state.column_widths = state_data['column_widths']
                
                if 'sort_columns' in state_data:
                    table_state.sort_columns = state_data['sort_columns']
                
                if 'selected_fields' in state_data:
                    table_state.selected_fields = state_data['selected_fields']
                
                if 'ui_state' in state_data:
                    # 保存完整的UI状态数据
                    if not hasattr(table_state, 'ui_state'):
                        table_state.ui_state = {}
                    table_state.ui_state.update(state_data['ui_state'])
                
                # 标记状态已修改
                table_state.mark_dirty(StateChangeType.DATA_RELOADED)
                
                # 自动保存
                if self.auto_save_enabled:
                    self._save_state()
                
                self.logger.debug(f"🔧 [P0-1修复] 表格状态保存成功: {table_name}")
                return True
                
        except Exception as e:
            self.logger.error(f"🔧 [P0-1修复] 表格状态保存失败: {table_name}, 错误: {e}")
            return False
    
    def restore_table_state_by_name(self, table_name: str) -> Optional[dict]:
        """🔧 [P0-1修复] 按表名恢复表格状态
        
        Args:
            table_name: 表名
            
        Returns:
            Optional[dict]: 恢复的状态数据，如果不存在返回None
        """
        try:
            with self._lock:
                if table_name not in self.table_states:
                    self.logger.debug(f"🔧 [P0-1修复] 表格状态不存在: {table_name}")
                    return None
                
                table_state = self.table_states[table_name]
                
                # 构建状态数据
                state_data = {}
                
                if table_state.column_widths:
                    state_data['column_widths'] = table_state.column_widths
                
                if table_state.sort_columns:
                    state_data['sort_columns'] = table_state.sort_columns
                
                if table_state.selected_fields:
                    state_data['selected_fields'] = table_state.selected_fields
                
                if hasattr(table_state, 'ui_state') and table_state.ui_state:
                    state_data['ui_state'] = table_state.ui_state
                
                self.logger.debug(f"🔧 [P0-1修复] 表格状态恢复成功: {table_name}, 状态项: {list(state_data.keys())}")
                return state_data if state_data else None
                
        except Exception as e:
            self.logger.error(f"🔧 [P0-1修复] 表格状态恢复失败: {table_name}, 错误: {e}")
            return None
    
    def update_global_state(self, state_update: Dict[str, Any], change_type: StateChangeType = None):
        """更新全局状态"""
        with self._lock:
            old_state = self._serialize_global_state()
            
            # 更新全局状态
            for key, value in state_update.items():
                if hasattr(self.global_state, key):
                    setattr(self.global_state, key, value)
            
            # 更新活动时间
            self.global_state.last_activity = datetime.now()
            
            # 创建变更记录
            change = StateChange(
                change_type=change_type or StateChangeType.DATA_RELOADED,
                table_name="__global__",
                old_value=old_state,
                new_value=self._serialize_global_state()
            )
            
            # 添加到历史记录
            self._add_change_to_history(change)
            
            # 通知监听器
            self._notify_global_state_changed(change)
            
            # 自动保存
            if self.auto_save_enabled:
                self._save_state()
            
            if log_throttle('usm-global-update', 1.0):
                self.logger.info(f"全局状态已更新: {change_type}")
    
    def set_state(self, key: str, value: Any):
        """
        设置状态值
        
        通用的状态设置方法，支持点分隔的键路径
        例如: set_state("format_management.config.theme", "dark")
        """
        with self._lock:
            # 解析键路径
            parts = key.split('.')
            
            # 创建状态更新字典
            state_update = {key: value}
            
            # 更新全局状态
            self.update_global_state(state_update, StateChangeType.DATA_RELOADED)
            
            self.logger.debug(f"状态已设置: {key} = {value}")
    
    def get_state(self, key: str, default: Any = None) -> Any:
        """
        获取状态值
        
        通用的状态获取方法，支持点分隔的键路径
        例如: get_state("format_management.config.theme", "light")
        """
        with self._lock:
            # 直接从全局状态中查找
            return self.global_state.custom_data.get(key, default)
    
    def sync_states(self, table_name: str):
        """
        同步相关状态
        
        确保表状态、分页状态、排序状态等的一致性
        """
        with self._lock:
            table_state = self.get_table_state(table_name)
            
            try:
                # 同步排序状态
                if table_state.sort_changed:
                    self._sync_sort_state(table_name, table_state)
                
                # 同步分页状态
                if table_state.page_changed:
                    self._sync_pagination_state(table_name, table_state)
                
                # 同步字段映射状态
                if table_state.field_mapping_changed:
                    self._sync_field_mapping_state(table_name, table_state)
                
                # 清除变更标记
                table_state.clear_change_flags()
                
                self.logger.debug(f"状态同步完成: {table_name}")
                
            except Exception as e:
                self.logger.error(f"状态同步失败: {table_name}, {e}")
    
    def _sync_sort_state(self, table_name: str, table_state: TableState):
        """同步排序状态"""
        # 这里可以与排序管理器同步
        # 暂时只记录日志
        self.logger.debug(f"同步排序状态: {table_name}, 排序列: {len(table_state.sort_columns)}")
    
    def _sync_pagination_state(self, table_name: str, table_state: TableState):
        """同步分页状态"""
        # 这里可以与分页管理器同步
        # 暂时只记录日志
        self.logger.debug(f"同步分页状态: {table_name}, 当前页: {table_state.current_page}")
    
    def _sync_field_mapping_state(self, table_name: str, table_state: TableState):
        """同步字段映射状态"""
        # 这里可以与字段映射管理器同步
        # 暂时只记录日志
        self.logger.debug(f"同步字段映射状态: {table_name}, 映射数: {len(table_state.field_mapping)}")
    
    def add_state_listener(self, listener: StateListener):
        """添加状态监听器"""
        with self._lock:
            if listener not in self.state_listeners:
                self.state_listeners.append(listener)
                self.logger.debug(f"添加状态监听器: {type(listener).__name__}")
    
    def remove_state_listener(self, listener: StateListener):
        """移除状态监听器"""
        with self._lock:
            if listener in self.state_listeners:
                self.state_listeners.remove(listener)
                self.logger.debug(f"移除状态监听器: {type(listener).__name__}")
    
    def _notify_table_state_changed(self, table_name: str, table_state: TableState, change: StateChange):
        """通知表状态变更"""
        for listener in self.state_listeners:
            try:
                listener.on_table_state_changed(table_name, table_state, change)
            except Exception as e:
                self.logger.error(f"状态监听器通知失败: {type(listener).__name__}, {e}")
    
    def _notify_global_state_changed(self, change: StateChange):
        """通知全局状态变更"""
        for listener in self.state_listeners:
            try:
                listener.on_global_state_changed(self.global_state, change)
            except Exception as e:
                self.logger.error(f"全局状态监听器通知失败: {type(listener).__name__}, {e}")
    
    def _add_change_to_history(self, change: StateChange):
        """添加变更到历史记录"""
        try:
            self._change_history.append(change)
            
            # 限制历史记录数量
            max_history = 500
            if len(self._change_history) > max_history:
                self._change_history = self._change_history[-max_history:]
                
        except Exception as e:
            self.logger.error(f"添加变更历史失败: {e}")
    
    def get_change_history(self, table_name: Optional[str] = None, limit: int = 50) -> List[StateChange]:
        """获取变更历史"""
        with self._lock:
            if table_name:
                filtered_history = [c for c in self._change_history if c.table_name == table_name]
            else:
                filtered_history = self._change_history
            
            return filtered_history[-limit:]
    
    def rollback_to_change(self, change_id: str) -> bool:
        """回滚到指定变更"""
        # 这个功能比较复杂，暂时只提供接口
        self.logger.warning(f"回滚功能暂未实现: {change_id}")
        return False
    
    def _serialize_table_state(self, table_state: TableState) -> Dict[str, Any]:
        """序列化表状态"""
        try:
            return {
                "table_name": table_state.table_name,
                "current_page": table_state.current_page,
                "page_size": table_state.page_size,
                "total_records": table_state.total_records,
                "sort_columns": table_state.sort_columns,
                "active_filters": table_state.active_filters,
                "field_mapping": table_state.field_mapping,
                "selected_rows": table_state.selected_rows,
                "expanded_rows": table_state.expanded_rows,
                "column_widths": table_state.column_widths,  # 🔧 [P0-2修复] 添加列宽序列化
                "selected_fields": table_state.selected_fields,  # 🔧 [P0-2修复] 添加选中字段序列化
                "last_loaded": table_state.last_loaded.isoformat() if table_state.last_loaded else None,
                "data_version": table_state.data_version
            }
        except Exception as e:
            self.logger.error(f"序列化表状态失败: {e}")
            return {}
    
    def _serialize_global_state(self) -> Dict[str, Any]:
        """序列化全局状态"""
        try:
            return {
                "current_table": self.global_state.current_table,
                "active_tables": self.global_state.active_tables,
                "navigation_path": self.global_state.navigation_path,
                "window_geometry": self.global_state.window_geometry,
                "splitter_positions": self.global_state.splitter_positions,
                "menu_visibility": self.global_state.menu_visibility,
                "default_page_size": self.global_state.default_page_size,
                "preferred_date_format": self.global_state.preferred_date_format,
                "auto_save_enabled": self.global_state.auto_save_enabled,
                "session_id": self.global_state.session_id
            }
        except Exception as e:
            self.logger.error(f"序列化全局状态失败: {e}")
            return {}
    
    def _save_state(self):
        """保存状态到文件"""
        try:
            state_data = {
                "global_state": self._serialize_global_state(),
                "table_states": {
                    name: self._serialize_table_state(state) 
                    for name, state in self.table_states.items()
                },
                "saved_at": datetime.now().isoformat()
            }
            
            # 确保目录存在
            state_file = Path(self.state_file_path)
            state_file.parent.mkdir(parents=True, exist_ok=True)
            
            # 保存到文件
            with open(state_file, 'w', encoding='utf-8') as f:
                json.dump(state_data, f, indent=2, ensure_ascii=False)
            
            self.logger.debug(f"状态已保存到: {self.state_file_path}")
            
        except Exception as e:
            self.logger.error(f"保存状态失败: {e}")
    
    def _load_state(self):
        """从文件加载状态"""
        try:
            state_file = Path(self.state_file_path)
            if not state_file.exists():
                self.logger.info("状态文件不存在，使用默认状态")
                return
            
            with open(state_file, 'r', encoding='utf-8') as f:
                state_data = json.load(f)
            
            # 加载全局状态
            if "global_state" in state_data:
                global_data = state_data["global_state"]
                for key, value in global_data.items():
                    if hasattr(self.global_state, key):
                        setattr(self.global_state, key, value)
            
            # 加载表状态
            if "table_states" in state_data:
                for table_name, table_data in state_data["table_states"].items():
                    table_state = TableState(table_name=table_name)
                    for key, value in table_data.items():
                        if hasattr(table_state, key):
                            if key == "last_loaded" and value:
                                setattr(table_state, key, datetime.fromisoformat(value))
                            else:
                                setattr(table_state, key, value)
                    self.table_states[table_name] = table_state
            
            self.logger.info(f"状态已从文件加载: {self.state_file_path}")
            
        except Exception as e:
            self.logger.error(f"加载状态失败: {e}")
    
    def get_state_statistics(self) -> Dict[str, Any]:
        """获取状态统计信息"""
        with self._lock:
            return {
                "total_tables": len(self.table_states),
                "active_listeners": len(self.state_listeners),
                "change_history_count": len(self._change_history),
                "current_table": self.global_state.current_table,
                "session_id": self.global_state.session_id,
                "last_activity": self.global_state.last_activity.isoformat()
            } 