"""
Excel文件导入器

功能说明:
- 支持 .xlsx, .xls 格式的Excel文件
- 自动检测文件格式和版本兼容性  
- 分批读取大文件，支持进度回调
- 统一的错误处理和日志记录
- 多编码格式支持
"""

import os
import pandas as pd
import openpyxl
import xlrd
from typing import Dict, List, Any, Optional, Callable, Union, Tuple
from pathlib import Path
import chardet
from loguru import logger

from src.utils.log_config import setup_logger
from src.utils.logging_utils import bind_context, log_throttle, log_sample, redact

class ExcelImporter:
    """Excel文件导入器
    
    提供Excel文件的读取、解析和验证功能
    """
    
    def __init__(self, config: Optional[Union[Dict[str, Any], Any]] = None):
        """初始化Excel导入器
        
        Args:
            config: 配置参数，可以是字典或ImportConfig对象
        """
        base_logger = setup_logger(__name__)
        # 绑定组件上下文，便于跟踪
        self.logger = bind_context(base_logger, component="ExcelImporter")
        
        # 处理配置参数 - 支持字典和ImportConfig对象
        if config is None:
            # 使用默认配置
            self.max_file_size_mb = 100
            self.chunk_size = 1000
            self.supported_formats = ['.xlsx', '.xls']
            self.encoding_priority = ['utf-8', 'gbk', 'gb2312']
        elif isinstance(config, dict):
            # 字典配置
            self.max_file_size_mb = config.get('max_file_size_mb', 100)
            self.chunk_size = config.get('chunk_size', 1000)
            self.supported_formats = config.get('supported_formats', ['.xlsx', '.xls'])
            self.encoding_priority = config.get('encoding_priority', ['utf-8', 'gbk', 'gb2312'])
        else:
            # ImportConfig对象或其他具有属性的对象
            self.max_file_size_mb = getattr(config, 'max_file_size_mb', 100)
            self.chunk_size = getattr(config, 'chunk_size', 1000)
            self.supported_formats = getattr(config, 'supported_formats', ['.xlsx', '.xls'])
            self.encoding_priority = getattr(config, 'encoding_priority', ['utf-8', 'gbk', 'gb2312'])
        
        # 保存原始配置
        self.config = config
        
        # 状态变量
        self.current_file = None
        self.file_info = {}
        self.import_progress = 0
        
    def validate_file(self, file_path: Union[str, Path]) -> Dict[str, Any]:
        """验证Excel文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            验证结果字典，包含文件信息和验证状态
            
        Raises:
            FileNotFoundError: 文件不存在
            ValueError: 文件格式不支持或文件过大
        """
        file_path = Path(file_path)
        
        # 检查文件是否存在
        if not file_path.exists():
            raise FileNotFoundError(f"文件不存在: {file_path}")
            
        # 检查文件扩展名
        file_ext = file_path.suffix.lower()
        if file_ext not in self.supported_formats:
            raise ValueError(f"不支持的文件格式: {file_ext}，支持的格式: {self.supported_formats}")
            
        # 检查文件大小
        file_size_mb = file_path.stat().st_size / (1024 * 1024)
        if file_size_mb > self.max_file_size_mb:
            raise ValueError(f"文件过大: {file_size_mb:.2f}MB，最大支持: {self.max_file_size_mb}MB")
            
        # 检查文件是否被锁定
        try:
            with open(file_path, 'rb') as f:
                f.read(1)
        except PermissionError:
            raise ValueError(f"文件被占用，无法访问: {file_path}")
            
        # 构建文件信息
        file_info = {
            'path': str(file_path),
            'name': file_path.name,
            'size_mb': file_size_mb,
            'extension': file_ext,
            'is_valid': True,
            'error_message': None
        }
        
        if log_throttle('import-validate-file', 1.0):
            self.logger.info(f"文件验证成功: {file_path.name} ({file_size_mb:.2f}MB)")
        return file_info
        
    def detect_excel_version(self, file_path: Union[str, Path]) -> str:
        """检测Excel文件版本
        
        Args:
            file_path: 文件路径
            
        Returns:
            Excel版本: 'xlsx', 'xls', 'unknown'
        """
        file_path = Path(file_path)
        file_ext = file_path.suffix.lower()
        
        if file_ext == '.xlsx':
            try:
                # 尝试用openpyxl打开
                workbook = openpyxl.load_workbook(file_path, read_only=True)
                workbook.close()
                return 'xlsx'
            except Exception:
                return 'unknown'
                
        elif file_ext == '.xls':
            try:
                # 尝试用xlrd打开
                workbook = xlrd.open_workbook(file_path)
                return 'xls'
            except Exception:
                return 'unknown'
                
        return 'unknown'
        
    def get_sheet_names(self, file_path: Union[str, Path]) -> List[str]:
        """获取Excel文件中的工作表名称
        
        Args:
            file_path: 文件路径
            
        Returns:
            工作表名称列表
            
        Raises:
            ValueError: 文件无法读取
        """
        file_path = Path(file_path)
        excel_version = self.detect_excel_version(file_path)
        
        try:
            if excel_version == 'xlsx':
                workbook = openpyxl.load_workbook(file_path, read_only=True)
                sheet_names = workbook.sheetnames
                workbook.close()
                
            elif excel_version == 'xls':
                workbook = xlrd.open_workbook(file_path)
                sheet_names = workbook.sheet_names()
                
            else:
                raise ValueError(f"无法识别的Excel文件格式: {file_path}")
                
            if log_throttle('import-sheet-names', 2.0):
                self.logger.info(f"检测到工作表: {sheet_names}")
            else:
                self.logger.debug(f"检测到工作表（节流）: {sheet_names}")
            return sheet_names
            
        except Exception as e:
            self.logger.error(f"获取工作表名称失败: {e}")
            raise ValueError(f"无法读取Excel文件: {e}")
            
    def preview_data(self, file_path: Union[str, Path], 
                    sheet_name: Optional[str] = None,
                    max_rows: int = 10) -> Dict[str, Any]:
        """预览Excel文件数据
        
        Args:
            file_path: 文件路径
            sheet_name: 工作表名称，为None时使用第一个工作表
            max_rows: 最大预览行数
            
        Returns:
            预览数据字典，包含列名、数据样本、基本统计信息
        """
        file_path = Path(file_path)
        
        try:
            # 读取数据
            result = pd.read_excel(file_path, sheet_name=sheet_name, nrows=max_rows)
            
            # 处理可能的字典返回值（多个sheet）
            if isinstance(result, dict):
                # 如果返回字典，取第一个sheet的数据
                first_sheet_name = list(result.keys())[0]
                df = result[first_sheet_name]
                if log_throttle('import-preview-first-sheet', 2.0):
                    self.logger.info(f"检测到多个工作表，使用第一个: {first_sheet_name}")
                else:
                    self.logger.debug(f"检测到多个工作表（节流），使用第一个: {first_sheet_name}")
            else:
                df = result
            
            # 构建预览信息
            preview_info = {
                'file_path': str(file_path),
                'sheet_name': sheet_name or '默认工作表',
                'columns': list(df.columns),
                'column_count': len(df.columns),
                'preview_rows': len(df),
                'data_types': df.dtypes.to_dict(),
                'sample_data': df.to_dict('records'),
                'has_header': self._detect_header(df),
                'encoding_info': 'Excel格式，无需编码检测'
            }
            
            if log_throttle('import-preview-finish', 1.0):
                self.logger.info(f"数据预览完成: {len(df.columns)}列, {len(df)}行")
            else:
                self.logger.debug(f"数据预览完成（节流）: {len(df.columns)}列, {len(df)}行")
            return preview_info
            
        except Exception as e:
            self.logger.error(f"数据预览失败: {e}")
            raise ValueError(f"预览数据失败: {e}")
            
    def import_data(self, file_path: Union[str, Path],
                   sheet_name: Optional[str] = None,
                   start_row: int = 0,
                   max_rows: Optional[int] = None,
                   columns: Optional[List[str]] = None,
                   progress_callback: Optional[Callable[[int], None]] = None,
                   filter_invalid: bool = True) -> pd.DataFrame:
        """导入Excel数据
        
        Args:
            file_path: 文件路径
            sheet_name: 工作表名称
            start_row: 起始行号
            max_rows: 最大行数，None表示读取全部
            columns: 指定列名，None表示读取全部列
            progress_callback: 进度回调函数
            filter_invalid: 是否过滤无效数据
            
        Returns:
            导入的DataFrame数据
            
        Raises:
            ValueError: 数据导入失败
        """
        file_path = Path(file_path)
        self.current_file = str(file_path)
        
        try:
            # 验证文件
            self.file_info = self.validate_file(file_path)
            
            # 记录开始导入
            if log_throttle('import-start', 1.0):
                self.logger.info(f"开始导入Excel文件: {file_path.name}")
            from src.utils.log_config import log_file_operation
            log_file_operation(str(file_path), 'read', size=f"{self.file_info['size_mb']:.1f}MB")
            
            # 分批读取大文件
            if max_rows and max_rows > self.chunk_size:
                df = self._import_large_file(file_path, sheet_name, start_row, 
                                             max_rows, columns, progress_callback)
            else:
                df = self._import_normal_file(file_path, sheet_name, start_row,
                                              max_rows, columns, progress_callback)
            
            # 如果启用了数据过滤，进行数据验证和过滤
            if filter_invalid and not df.empty:
                pre_filter_columns = len(df.columns)
                df = self._filter_invalid_data(df)
                post_filter_columns = len(df.columns)
                
                # 验证过滤过程没有丢失列
                if pre_filter_columns != post_filter_columns:
                    self.logger.warning(f"数据过滤过程中列数发生变化: {pre_filter_columns} -> {post_filter_columns}")
                
            # 最终验证：确保数据导入完整性  
            if not df.empty:
                print(f"[修复标识] 数据导入最终完成: {len(df)}行 × {len(df.columns)}列")
                print(f"[修复标识] 最终列名: {list(df.columns)}")
                if log_throttle('import-final', 1.0):
                    self.logger.info(f"[修复标识] 数据导入最终完成: {len(df)}行 × {len(df.columns)}列")
                # 列名详情仅DEBUG，且节流
                if log_throttle('import-final-columns', 3.0):
                    self.logger.debug(f"[修复标识] 最终列名: {list(df.columns)}")
            
            return df
                
        except Exception as e:
            self.logger.error(f"Excel数据导入失败: {e}")
            raise ValueError(f"导入失败: {e}")
            
    def _import_normal_file(self, file_path: Path, sheet_name: Optional[str],
                          start_row: int, max_rows: Optional[int],
                          columns: Optional[List[str]],
                          progress_callback: Optional[Callable[[int], None]]) -> pd.DataFrame:
        """导入普通大小的文件"""
        
        try:
            # 读取数据 - 修复：确保读取所有列
            # 当columns为None时，读取所有列；否则使用指定的列
            result = pd.read_excel(
                file_path,
                sheet_name=sheet_name,
                skiprows=start_row,
                nrows=max_rows,
                usecols=columns  # None表示读取所有列
            )
            
            # 日志记录读取的列信息
            if isinstance(result, dict):
                first_key = list(result.keys())[0]
                columns_read = len(result[first_key].columns)
            else:
                columns_read = len(result.columns)
            print(f"[修复标识] Excel读取完成: {columns_read}列 (列过滤: {'是' if columns else '否'})")
            if log_throttle('import-read-columns', 2.0):
                self.logger.info(f"[修复标识] Excel读取完成: {columns_read}列 (列过滤: {'是' if columns else '否'})")
            else:
                self.logger.debug(f"[修复标识] Excel读取完成（节流）: {columns_read}列 (列过滤: {'是' if columns else '否'})")
            
            # 处理可能的字典返回值（多个sheet）
            if isinstance(result, dict):
                # 如果返回字典，取第一个sheet的数据
                first_sheet_name = list(result.keys())[0]
                df = result[first_sheet_name]
                if log_throttle('import-normal-first-sheet', 2.0):
                    self.logger.info(f"检测到多个工作表，使用第一个: {first_sheet_name}")
                else:
                    self.logger.debug(f"检测到多个工作表（节流），使用第一个: {first_sheet_name}")
            else:
                df = result
            
            # 数据清理
            df = self._clean_data(df)
            
            # 更新进度
            if progress_callback:
                progress_callback(100)
            self.import_progress = 100
            
            if log_throttle('import-normal-finish', 1.0):
                self.logger.info(f"导入完成: {len(df)}行 x {len(df.columns)}列")
            else:
                self.logger.debug(f"导入完成（节流）: {len(df)}行 x {len(df.columns)}列")
            return df
            
        except Exception as e:
            self.logger.error(f"文件导入错误: {e}")
            raise
            
    def _import_large_file(self, file_path: Path, sheet_name: Optional[str],
                         start_row: int, max_rows: int,
                         columns: Optional[List[str]],
                         progress_callback: Optional[Callable[[int], None]]) -> pd.DataFrame:
        """分批导入大文件"""
        
        chunks = []
        total_chunks = (max_rows + self.chunk_size - 1) // self.chunk_size
        
        for i in range(total_chunks):
            chunk_start = start_row + i * self.chunk_size
            chunk_size = min(self.chunk_size, max_rows - i * self.chunk_size)
            
            try:
                # 读取分块数据 - 修复：确保读取所有列  
                chunk_result = pd.read_excel(
                    file_path,
                    sheet_name=sheet_name,
                    skiprows=chunk_start,
                    nrows=chunk_size,
                    usecols=columns  # None表示读取所有列
                )
                
                # 日志记录分块读取的列信息
                if i == 0:  # 只在第一个分块时记录列信息
                    if isinstance(chunk_result, dict):
                        first_key = list(chunk_result.keys())[0]
                        columns_read = len(chunk_result[first_key].columns)
                    else:
                        columns_read = len(chunk_result.columns)
                    if log_throttle('import-chunk-read-columns', 2.0):
                        self.logger.info(f"分块Excel读取: {columns_read}列 (列过滤: {'是' if columns else '否'})")
                    else:
                        self.logger.debug(f"分块Excel读取（节流）: {columns_read}列 (列过滤: {'是' if columns else '否'})")
                
                # 处理可能的字典返回值（多个sheet）
                if isinstance(chunk_result, dict):
                    # 如果返回字典，取第一个sheet的数据
                    first_sheet_name = list(chunk_result.keys())[0]
                    chunk_df = chunk_result[first_sheet_name]
                else:
                    chunk_df = chunk_result
                
                # 清理数据
                chunk_df = self._clean_data(chunk_df)
                chunks.append(chunk_df)
                
                # 更新进度
                progress = int((i + 1) / total_chunks * 100)
                if progress_callback:
                    progress_callback(progress)
                self.import_progress = progress
                
                if log_sample('import-chunk-progress', 5):
                    self.logger.debug(f"分块导入进度: {progress}% ({i+1}/{total_chunks})")
                
            except Exception as e:
                self.logger.error(f"分块{i+1}导入失败: {e}")
                raise
                
        # 合并所有分块
        if chunks:
            df = pd.concat(chunks, ignore_index=True)
            if log_throttle('import-large-finish', 1.0):
                self.logger.info(f"大文件导入完成: {len(df)}行 x {len(df.columns)}列")
            else:
                self.logger.debug(f"大文件导入完成（节流）: {len(df)}行 x {len(df.columns)}列")
            return df
        else:
            return pd.DataFrame()
            
    def _clean_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """清理数据 - 修复版本：保留所有列结构
        
        Args:
            df: 原始DataFrame
            
        Returns:
            清理后的DataFrame
        """
        if df.empty:
            return df
        
        original_columns = len(df.columns)
        
        # 【修复】只删除完全为空的行，保留所有列（包括数据为空的列）
        # 这确保Excel中的列结构完全保留，避免字段丢失
        print("[修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行")
        self.logger.info("[修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行")
        df = df.dropna(how='all')  # 只删除全空行，不删除任何列
        
        # 清理列名（保持原有逻辑）
        df.columns = [str(col).strip() for col in df.columns]
        
        # 替换常见的空值表示（保持原有逻辑）
        df = df.replace(['', 'NULL', 'null', 'None', 'none'], pd.NA)
        
        # 日志记录列结构保留情况
        final_columns = len(df.columns)
        self.logger.info(f"数据清理完成: 保留所有 {final_columns} 列 (原始 {original_columns} 列)")
        
        return df
        
    def _detect_header(self, df: pd.DataFrame) -> bool:
        """检测是否包含表头
        
        Args:
            df: DataFrame数据
            
        Returns:
            是否包含表头
        """
        if df.empty:
            return False
            
        # 简单启发式检测：第一行如果包含字符串且后续行主要是数字，则可能是表头
        first_row = df.iloc[0] if len(df) > 0 else None
        if first_row is None:
            return False
            
        # 检查第一行是否大部分是字符串
        string_count = sum(1 for val in first_row if isinstance(val, str))
        return string_count > len(first_row) * 0.5
        
    def get_import_progress(self) -> int:
        """获取当前导入进度
        
        Returns:
            进度百分比 (0-100)
        """
        return self.import_progress
        
    def get_file_info(self) -> Dict[str, Any]:
        """获取当前文件信息
        
        Returns:
            文件信息字典
        """
        return self.file_info.copy() if self.file_info else {}
        
    def export_template(self, output_path: Union[str, Path], 
                       columns: List[str],
                       sample_data: Optional[List[Dict[str, Any]]] = None) -> bool:
        """导出Excel模板文件
        
        Args:
            output_path: 输出文件路径
            columns: 列名列表
            sample_data: 示例数据
            
        Returns:
            是否导出成功
        """
        try:
            output_path = Path(output_path)
            
            # 创建DataFrame
            if sample_data:
                df = pd.DataFrame(sample_data)
            else:
                # 创建空模板
                df = pd.DataFrame(columns=columns)
                
            # 确保输出目录存在
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 导出Excel文件
            df.to_excel(output_path, index=False, engine='openpyxl')
            
            from src.utils.log_config import log_file_operation
            log_file_operation(str(output_path), 'write')
            
            self.logger.info(f"模板文件导出成功: {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"模板导出失败: {e}")
            return False

    def _filter_invalid_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        过滤无效数据
        
        Args:
            df: 原始DataFrame
            
        Returns:
            过滤后的DataFrame
        """
        if df.empty:
            return df
        
        original_count = len(df)
        filtered_df = df.copy()
        invalid_records = []
        
        # 检查姓名字段是否存在
        name_columns = ['姓名', 'name', '员工姓名', '姓名']
        name_column = None
        for col in name_columns:
            if col in df.columns:
                name_column = col
                break
        
        if name_column:
            # 识别姓名为空的记录
            name_empty_mask = df[name_column].isna() | (df[name_column].astype(str).str.strip() == '')
            invalid_indices = df[name_empty_mask].index.tolist()
            
            if invalid_indices:
                # 记录无效数据到日志
                for idx in invalid_indices:
                    record_info = {}
                    for col in df.columns:
                        value = df.loc[idx, col]
                        if pd.notna(value) and str(value).strip():
                            record_info[col] = redact(str(value))
                    
                    invalid_records.append({
                        'row_index': idx + 1,  # 转换为1基索引
                        'reason': f'姓名字段({name_column})为空',
                        'record_data': record_info
                    })
                
                # 过滤掉无效记录
                filtered_df = df[~name_empty_mask].copy()
                
                # 记录到日志（数据质量统计）
                self.logger.info(f"数据质量检查: 已过滤{len(invalid_indices)}条姓名为空的记录")
                # 详细记录仅在debug模式下输出
                for record in invalid_records:
                    self.logger.debug(f"过滤记录[行{record['row_index']}]: {record['reason']}, 数据: {record['record_data']}")
        
        # 检查工号字段（如果存在）
        employee_id_columns = ['工号', 'employee_id', '员工号', '工作证号']
        employee_id_column = None
        for col in employee_id_columns:
            if col in df.columns:
                employee_id_column = col
                break
        
        if employee_id_column and not filtered_df.empty:
            # 识别工号为空的记录
            id_empty_mask = filtered_df[employee_id_column].isna() | (filtered_df[employee_id_column].astype(str).str.strip() == '')
            invalid_id_indices = filtered_df[id_empty_mask].index.tolist()
            
            if invalid_id_indices:
                # 记录工号为空的记录
                for idx in invalid_id_indices:
                    record_info = {}
                    for col in filtered_df.columns:
                        value = filtered_df.loc[idx, col]
                        if pd.notna(value) and str(value).strip():
                            record_info[col] = value
                    
                    # 输出时对可能的敏感信息进行脱敏
                    safe_info = {k: redact(str(v)) for k, v in record_info.items()}
                    self.logger.info(f"过滤记录[行{idx + 1}]: 工号字段({employee_id_column})为空, 数据: {safe_info}")
                
                # 过滤掉工号为空的记录
                filtered_df = filtered_df[~id_empty_mask].copy()
                self.logger.warning(f"数据导入过滤: 发现{len(invalid_id_indices)}条工号为空的记录")
        
        # 重置索引
        if not filtered_df.empty:
            filtered_df = filtered_df.reset_index(drop=True)
        
        filtered_count = len(filtered_df)
        invalid_count = original_count - filtered_count
        
        if invalid_count > 0:
            self.logger.info(f"数据过滤完成: 原始{original_count}条记录，过滤{invalid_count}条无效记录，有效记录{filtered_count}条")
        
        return filtered_df 