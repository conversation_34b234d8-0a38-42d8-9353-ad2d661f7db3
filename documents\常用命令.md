

# 常用命令
---

zip -r ./zip/salary_changes_2025072502.zip /home/<USER>/salary_changes/

scp ubuntu@*************:/home/<USER>/zip/salary_changes_2025072502.zip .

scp .\logs\salary_system.log ubuntu@*************:/home/<USER>/salary_changes/logs

scp .\data\db\salary_system.db ubuntu@*************:/home/<USER>/salary_changes/data/db
scp .\data\db\salary_system.db-shm ubuntu@*************:/home/<USER>/salary_changes/data/db
scp .\data\db\salary_system.db-wal ubuntu@*************:/home/<USER>/salary_changes/data/db

scp .\debug.zip ubuntu@*************:/home/<USER>/salary_changes
scp .\salary_changes.zip ubuntu@*************:/home/<USER>





更改当前目录为：/home/<USER>/salary_changes  
cd /home/<USER>/salary_changes && pwd




---

cd E:\project\case\salary_changes

尝试直接运行
"E:/project/case/salary_changes/.venv/Scripts/python.exe" main.py

# 激活powershell虚拟环境
E:\project\case\salary_changes\.venv\Scripts\Activate.ps1

# 删除源代码目录 src 中所有缓存目录 __pycache__ 
Get-ChildItem -Path "src" -Name "__pycache__" -Recurse -Directory | ForEach-Object { Remove-Item -Path "src\$_" -Recurse -Force; Write-Host "已删除: src\$_" }



我将为双击运行提供一个更稳妥的方式：在 `C:\test\salary_changes\start_admin_shell.bat` 新增一个批处理文件，用于以 Bypass 执行策略启动 PowerShell 脚本并在结束后暂停，适合直接双击。随后会给出文件内容。
Ran tool

不建议直接双击运行 `.ps1`。原因：
- Windows 默认执行策略可能拦截脚本，且双击运行后窗口会立即关闭，难以查看输出。

推荐做法：
- 直接双击这个文件运行：`C:\test\salary_changes\start_admin_shell.bat`（我已创建）。它会：
  - 以 Bypass 执行策略调用 `start_admin_shell.ps1`
  - 结束后 `pause`，窗口不闪退，便于查看日志

如必须双击 `.ps1`，需先配置（一次性）：
- 以管理员身份打开 PowerShell，执行：
  - `Set-ExecutionPolicy -Scope CurrentUser RemoteSigned -Force`
  - `Unblock-File 'C:\test\salary_changes\start_admin_shell.ps1'`
- 再给该脚本创建一个快捷方式，在“目标”中使用：
  - `powershell.exe -NoLogo -NoProfile -ExecutionPolicy Bypass -NoExit -File "C:\test\salary_changes\start_admin_shell.ps1"`

- 已新增文件：`C:\test\salary_changes\start_admin_shell.bat`，用于支持双击稳定运行。





---


claude mcp add playwright -s user -- npx @playwright/mcp@latest

aude mcp add puppeteer--npx-y @modelcontextprotocol/server-puppeteer


vscode插件
Claude Image Paste

# 请将上面方案内容梳理一下，以 markdown 格式独立保存到目录 docs/todo/format 下，以中文命名文件名。

# 你需要形成一个markdown文档来记录处理过程，并报告结论，文件存放路径为：./docs/processing ,文件名称为：年月日时分秒 + 功能（中文）










在系统中，如何手动重置项目各类配置文件？将系统重置为初始状态。

我想问一个问题：我通过菜单访问配置管理器来配置新的系统配置后，能够立即生效吗？

我看到有些人认为“代码文件代码行数不应该太多”，你怎么看？

---




---

ssh-keygen -t rsa -b 4096

type $HOME\.ssh\id_rsa.pub | ssh cc "mkdir -p ~/.ssh && cat >> ~/.ssh/authorized_keys && chmod 700 ~/.ssh && chmod 600 ~/.ssh/authorized_keys"

---







日志设置应该规范，且详细，并且能够适应开发环境到生产环境的变化。

对于经过2次都无法解决的问题，让AI给出5种以上解决办法，然后一个一个去试，成功后，以此为模板，以后遇到类似问题，让AI应用次成功逻辑模板。




请将本次对话内容梳理一下，以 markdown 格式独立保存到目录 docs/todo/20250729 下，以中文命名文件名。

请将上面方案内容梳理一下，以 markdown 格式独立保存到目录 docs/todo 下，以中文命名文件名。



一次解决一个问题，这次请你专注此问题的解决，请你一步一步思考，综合分析，找出问题所在，给出有效解决方案，并给出方案优缺点。

## 你需要仔细阅读项目代码，根据项目实际情况，结合用户反应的几个问题，一步一步思考，梳理出项目当前情况，找出主要原因与次要原因，并给出解决方案。暂不需要具体实现。


打包压缩目录 @salary_changes 为 salary_changes_2025070103.zip ，放置在目录 ./zip 下


zip -r ./zip/salary_changes_2025071104.zip /home/<USER>/salary_changes/

scp ubuntu@*************:/home/<USER>/zip/salary_changes_2025071104.zip .

scp ubuntu@*************:/home/<USER>/salary_changes_2025063004.tar.gz .

scp .\salary_changes\logs\salary_system.log ubuntu@*************:/home/<USER>/salary_changes/logs


E:\project\case\salary_changes\salary_changes\.venv\Scripts\Activate.ps1



scp .\salary_changes.zip ubuntu@*************:/home/<USER>/

scp .\debug.zip ubuntu@*************:/home/<USER>/salary_changes/

你可以在你修改的代码部分添加标识日志信息，这样在运行程序时，如果有标识日志，就说明运行到了你修改的代码部分，否则，那就可以据此判断问题出现在哪些位置了。



