"""
测试异动表数据导入功能
"""
import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))

from src.modules.data_import.multi_sheet_importer import MultiSheetImporter
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.modules.data_storage.database_manager import DatabaseManager
from loguru import logger
import pandas as pd

def test_change_data_import():
    """测试异动表导入功能"""
    logger.info("开始测试异动表导入功能...")
    
    # 1. 初始化组件
    db_manager = DatabaseManager()
    table_manager = DynamicTableManager(db_manager)
    multi_sheet_importer = MultiSheetImporter(table_manager)  # 传递table_manager而不是db_manager
    
    # 2. 创建测试数据 - 使用更符合异动表结构的数据
    from datetime import datetime
    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    test_data = pd.DataFrame({
        'employee_id': ['001', '002', '003'],
        'employee_name': ['张三', '李四', '王五'],
        'change_type': ['加薪', '调岗', '加薪'],
        'field_name': ['基本工资', '部门', '基本工资'],
        'old_value': ['5000', '财务部', '6000'],
        'new_value': ['5500', '人事部', '6800'],
        'change_amount': [500, 0, 800],
        'change_reason': ['年度调薪', '部门调整', '绩效优秀'],
        'month': ['08', '08', '08'],
        'year': [2025, 2025, 2025],
        'detected_at': [current_time, current_time, current_time],
        'verified': [0, 0, 0]
    })
    
    # 3. 保存为测试Excel文件
    test_file = project_root / 'temp' / 'test_change_data.xlsx'
    test_data.to_excel(test_file, sheet_name='异动人员', index=False)
    logger.info(f"创建测试文件: {test_file}")
    
    # 4. 测试导入 - 模拟异动表导入
    logger.info("测试异动表导入...")
    
    # 设置导入参数
    year = 2025
    month = 8
    target_path = "工资表/异动人员表"  # 模拟选择异动人员表
    
    # 执行导入
    result = multi_sheet_importer.import_excel_file(
        file_path=str(test_file),
        year=year,
        month=month,
        target_table=None,
        target_path=target_path
    )
    
    # 5. 验证结果
    if result.get('success'):
        logger.success(f"异动表导入成功!")
        
        # 检查生成的表名
        if 'target_table' in result:
            table_name = result['target_table']
            logger.info(f"生成的表名: {table_name}")
            
            # 验证表名前缀
            if table_name.startswith('change_data'):
                logger.success("✓ 表名前缀正确: change_data")
            else:
                logger.error(f"✗ 表名前缀错误: {table_name}")
        
        # 检查表是否创建
        if 'sheets_data' in result:
            for sheet_name, count in result['sheets_data'].items():
                logger.info(f"  - Sheet '{sheet_name}': {count} 条记录")
    else:
        logger.error(f"异动表导入失败: {result.get('error', '未知错误')}")
    
    # 6. 查询导入的数据
    logger.info("\n查询导入的异动表...")
    # 使用正确的方法获取表名
    query = "SELECT name FROM sqlite_master WHERE type='table' AND name LIKE 'change_data%'"
    result = db_manager.execute_query(query)
    change_tables = [r['name'] for r in result] if result else []
    
    if change_tables:
        logger.success(f"找到 {len(change_tables)} 个异动表:")
        for table in change_tables:
            logger.info(f"  - {table}")
            
            # 查询表内容
            query = f'SELECT * FROM "{table}" LIMIT 5'
            records = db_manager.execute_query(query)
            if records:
                logger.info(f"    前5条记录:")
                for rec in records:
                    logger.info(f"      {rec}")
    else:
        logger.warning("未找到任何异动表")
    
    # 7. 清理测试文件
    if test_file.exists():
        test_file.unlink()
        logger.info(f"清理测试文件: {test_file}")
    
    logger.info("异动表导入功能测试完成!")
    return result.get('success', False) if isinstance(result, dict) else False

if __name__ == "__main__":
    success = test_change_data_import()
    sys.exit(0 if success else 1)