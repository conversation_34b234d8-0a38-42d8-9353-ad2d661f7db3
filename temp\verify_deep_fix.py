#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证异动表深度修复效果
测试新的基于元数据查询的表管理逻辑
"""

import sys
import os
import sqlite3
import time
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))

def test_database_metadata():
    """测试数据库元数据状态"""
    print("🔍 测试数据库元数据状态...")
    
    db_path = "data/db/salary_system.db"
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查异动表元数据
        cursor.execute("SELECT table_name, table_type FROM table_metadata WHERE table_type='change_data';")
        change_metadata = cursor.fetchall()
        
        print(f"📊 change_data类型元数据记录: {len(change_metadata)} 个")
        for row in change_metadata:
            print(f"  ✅ {row[0]} -> 类型: {row[1]}")
        
        # 检查实际表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE 'change_data_%';")
        actual_tables = cursor.fetchall()
        
        print(f"📋 实际异动表: {len(actual_tables)} 个")
        for row in actual_tables:
            print(f"  📄 {row[0]}")
        
        conn.close()
        
        if len(change_metadata) == len(actual_tables) and len(change_metadata) > 0:
            print("✅ 数据库元数据状态正常")
            return True
        else:
            print(f"❌ 元数据与实际表不匹配: 元数据{len(change_metadata)}个, 实际表{len(actual_tables)}个")
            return False
        
    except Exception as e:
        print(f"❌ 测试数据库元数据失败: {e}")
        return False

def test_dynamic_table_manager():
    """测试动态表管理器的新逻辑"""
    print("\n🔍 测试动态表管理器...")
    
    try:
        from modules.data_storage.database_manager import DatabaseManager
        from modules.data_storage.dynamic_table_manager import DynamicTableManager
        
        # 初始化管理器
        db_manager = DatabaseManager()
        table_manager = DynamicTableManager(db_manager)
        
        print("📋 测试get_table_list方法...")
        
        # 测试获取change_data类型的表
        start_time = time.time()
        change_tables = table_manager.get_table_list(table_type='change_data')
        end_time = time.time()
        
        print(f"⏱️  查询耗时: {end_time - start_time:.3f}秒")
        print(f"📊 找到异动表: {len(change_tables)} 个")
        
        for table_info in change_tables:
            table_name = table_info.get('table_name', 'Unknown')
            year = table_info.get('year', 'N/A')
            month = table_info.get('month', 'N/A')
            display_name = table_info.get('display_name', 'N/A')
            record_count = table_info.get('record_count', 'N/A')
            
            print(f"  📄 {table_name}")
            print(f"     年份: {year}, 月份: {month}")
            print(f"     显示名: {display_name}")
            print(f"     记录数: {record_count}")
        
        # 测试获取salary_data类型的表（对比）
        salary_tables = table_manager.get_table_list(table_type='salary_data')
        print(f"📊 对比：工资表 {len(salary_tables)} 个")
        
        if len(change_tables) > 0:
            print("✅ 动态表管理器测试通过")
            return True
        else:
            print("❌ 动态表管理器未找到异动表")
            return False
        
    except Exception as e:
        print(f"❌ 测试动态表管理器失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_navigation_tree_data():
    """测试导航树数据生成"""
    print("\n🔍 测试导航树数据生成...")
    
    try:
        from modules.data_storage.database_manager import DatabaseManager
        from modules.data_storage.dynamic_table_manager import DynamicTableManager
        
        # 初始化管理器
        db_manager = DatabaseManager()
        table_manager = DynamicTableManager(db_manager)
        
        # 测试异动表导航树数据
        start_time = time.time()
        change_nav_data = table_manager.get_change_navigation_tree_data()
        end_time = time.time()
        
        print(f"⏱️  导航树查询耗时: {end_time - start_time:.3f}秒")
        print(f"📊 导航树数据结构: {len(change_nav_data)} 个年份")
        
        for year, months in change_nav_data.items():
            print(f"  📅 {year}年: {len(months)} 个月份")
            for month, categories in months.items():
                print(f"    📅 {month}月: {len(categories)} 个类别")
                for category in categories:
                    table_name = category.get('table_name', 'Unknown')
                    display_name = category.get('display_name', 'Unknown')
                    print(f"      📄 {display_name} ({table_name})")
        
        if len(change_nav_data) > 0:
            print("✅ 导航树数据生成测试通过")
            return True
        else:
            print("❌ 导航树数据为空")
            return False
        
    except Exception as e:
        print(f"❌ 测试导航树数据生成失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_ready_check():
    """测试数据库就绪检查"""
    print("\n🔍 测试数据库就绪检查...")
    
    try:
        from modules.data_storage.database_manager import DatabaseManager
        from modules.data_storage.dynamic_table_manager import DynamicTableManager
        
        # 初始化管理器
        db_manager = DatabaseManager()
        table_manager = DynamicTableManager(db_manager)
        
        # 测试数据库就绪检查
        start_time = time.time()
        is_ready = table_manager._ensure_database_ready()
        end_time = time.time()
        
        print(f"⏱️  就绪检查耗时: {end_time - start_time:.3f}秒")
        print(f"📊 数据库就绪状态: {'✅ 就绪' if is_ready else '❌ 未就绪'}")
        
        if is_ready:
            print("✅ 数据库就绪检查测试通过")
            return True
        else:
            print("❌ 数据库就绪检查失败")
            return False
        
    except Exception as e:
        print(f"❌ 测试数据库就绪检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_startup_simulation():
    """模拟系统启动时的表查询"""
    print("\n🔍 模拟系统启动时的表查询...")
    
    try:
        from modules.data_storage.database_manager import DatabaseManager
        from modules.data_storage.dynamic_table_manager import DynamicTableManager
        
        # 模拟多次启动查询
        success_count = 0
        total_attempts = 5
        
        for i in range(total_attempts):
            print(f"  🚀 模拟启动 {i+1}/{total_attempts}...")
            
            # 重新初始化管理器（模拟新启动）
            db_manager = DatabaseManager()
            table_manager = DynamicTableManager(db_manager)
            
            # 查询异动表
            change_tables = table_manager.get_table_list(table_type='change_data')
            
            if len(change_tables) > 0:
                print(f"    ✅ 找到 {len(change_tables)} 个异动表")
                success_count += 1
            else:
                print(f"    ❌ 未找到异动表")
            
            # 短暂等待
            time.sleep(0.1)
        
        success_rate = success_count / total_attempts * 100
        print(f"📊 启动成功率: {success_rate:.1f}% ({success_count}/{total_attempts})")
        
        if success_rate >= 80:  # 80%以上成功率认为通过
            print("✅ 系统启动模拟测试通过")
            return True
        else:
            print("❌ 系统启动模拟测试失败")
            return False
        
    except Exception as e:
        print(f"❌ 模拟系统启动测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始验证异动表深度修复效果...\n")
    
    results = []
    
    # 执行各项测试
    results.append(("数据库元数据状态", test_database_metadata()))
    results.append(("动态表管理器", test_dynamic_table_manager()))
    results.append(("导航树数据生成", test_navigation_tree_data()))
    results.append(("数据库就绪检查", test_database_ready_check()))
    results.append(("系统启动模拟", test_startup_simulation()))
    
    # 汇总结果
    print("\n" + "="*60)
    print("📊 深度修复验证结果汇总:")
    print("="*60)
    
    success_count = 0
    for name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{name}: {status}")
        if result:
            success_count += 1
    
    print(f"\n总体结果: {success_count}/{len(results)} 项测试通过")
    
    if success_count == len(results):
        print("\n🎉 所有深度修复验证通过！异动表导航问题已彻底解决。")
        print("\n📋 修复成果总结:")
        print("1. ✅ 重构了get_table_list方法，改为基于元数据查询")
        print("2. ✅ 完善了_parse_table_list方法，支持change_data类型")
        print("3. ✅ 统一了重试机制，所有表类型享有一致的重试策略")
        print("4. ✅ 增强了数据库就绪检查，改进WAL模式同步")
        print("5. ✅ 系统启动时能稳定找到异动表")
        print("\n🔄 建议重启系统以验证实际效果。")
        return True
    else:
        print(f"\n❌ 深度修复验证失败，有 {len(results) - success_count} 项未通过验证。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
