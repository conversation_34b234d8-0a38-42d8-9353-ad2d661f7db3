Stack trace:
Frame         Function      Args
0007FFFFB560  00021005FEBA (000210285F48, 00021026AB6E, 0007FFFFB560, 0007FFFFA460) msys-2.0.dll+0x1FEBA
0007FFFFB560  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFB838) msys-2.0.dll+0x67F9
0007FFFFB560  000210046832 (000210285FF9, 0007FFFFB418, 0007FFFFB560, 000000000000) msys-2.0.dll+0x6832
0007FFFFB560  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFB560  0002100690B4 (0007FFFFB570, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFB840  00021006A49D (0007FFFFB570, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFA2A930000 ntdll.dll
7FFA28840000 KERNEL32.DLL
7FFA26B90000 KERNELBASE.dll
7FFA27B50000 USER32.dll
7FFA26AD0000 win32u.dll
7FFA2A4E0000 GDI32.dll
7FFA271A0000 gdi32full.dll
7FFA26AF0000 msvcp_win.dll
7FFA269A0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFA28240000 advapi32.dll
7FFA2A140000 msvcrt.dll
7FFA28080000 sechost.dll
7FFA2A390000 RPCRT4.dll
7FFA26AA0000 bcrypt.dll
7FFA26290000 CRYPTBASE.DLL
7FFA27AC0000 bcryptPrimitives.dll
7FFA2A4B0000 IMM32.DLL
