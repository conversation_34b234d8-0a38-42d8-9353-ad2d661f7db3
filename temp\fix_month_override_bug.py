"""
修复月份覆盖问题的紧急补丁

问题：目标位置选择会自动覆盖用户的数据期间输入
解决：移除自动覆盖逻辑，让两个输入独立
"""

def fix_month_override_issue():
    """
    修复方案：修改 main_dialogs.py 的 _on_target_changed 方法
    """
    
    # 方案1：完全移除自动覆盖（推荐）
    fix_1 = """
    # 在 main_dialogs.py 第2148行
    def _on_target_changed(self, target_info: Dict[str, str]):
        '''处理目标位置变化'''
        self.final_target_path = self.target_selection_widget.get_target_path_string()
        self.logger.info(f"目标位置已更新: {self.final_target_path}")
        
        # ❌ 删除以下自动覆盖代码
        # # 自动填充数据期间
        # if hasattr(self, 'data_period_edit'):
        #     year = target_info.get('year', '')
        #     month = target_info.get('month', '').zfill(2)
        #     if year and month:
        #         self.data_period_edit.setText(f"{year}-{month}")
        
        # 保留其他功能
        category = target_info.get('category', '')
        if category and self.current_file_path:
            self._auto_select_sheet_by_category(category)
        
        self._adjust_import_strategy_by_target(target_info)
    """
    
    # 方案2：仅在数据期间为空时才自动填充
    fix_2 = """
    def _on_target_changed(self, target_info: Dict[str, str]):
        '''处理目标位置变化'''
        self.final_target_path = self.target_selection_widget.get_target_path_string()
        self.logger.info(f"目标位置已更新: {self.final_target_path}")
        
        # 仅在数据期间为空时自动填充，不覆盖用户输入
        if hasattr(self, 'data_period_edit'):
            current_period = self.data_period_edit.text().strip()
            if not current_period:  # 只有为空时才自动填充
                year = target_info.get('year', '')
                month = target_info.get('month', '').zfill(2)
                if year and month:
                    self.data_period_edit.setText(f"{year}-{month}")
                    self.logger.info(f"数据期间为空，自动填充为: {year}-{month}")
        
        # 其他功能保持不变...
    """
    
    # 方案3：添加确认提示
    fix_3 = """
    def _on_target_changed(self, target_info: Dict[str, str]):
        '''处理目标位置变化'''
        self.final_target_path = self.target_selection_widget.get_target_path_string()
        self.logger.info(f"目标位置已更新: {self.final_target_path}")
        
        # 如果数据期间与目标位置的月份不一致，提醒用户
        if hasattr(self, 'data_period_edit'):
            current_period = self.data_period_edit.text().strip()
            year = target_info.get('year', '')
            month = target_info.get('month', '').zfill(2)
            
            if current_period and year and month:
                target_period = f"{year}-{month}"
                if current_period != target_period:
                    reply = QMessageBox.question(
                        self, "月份不一致",
                        f"数据期间({current_period})与目标位置月份({target_period})不一致。\\n"
                        f"是否使用目标位置的月份？\\n\\n"
                        f"选择'是'：使用{target_period}\\n"
                        f"选择'否'：保持{current_period}",
                        QMessageBox.Yes | QMessageBox.No,
                        QMessageBox.No  # 默认选择"否"，保持用户输入
                    )
                    if reply == QMessageBox.Yes:
                        self.data_period_edit.setText(target_period)
        
        # 其他功能...
    """
    
    return {
        "immediate_fix": fix_1,
        "smart_fix": fix_2,
        "interactive_fix": fix_3
    }

def additional_improvements():
    """
    额外的改进建议
    """
    return """
    1. 界面改进：
       - 将"数据期间"改名为"数据所属期间"，更明确
       - 在输入框旁添加说明："此处填写数据实际所属的年月"
       - 目标位置说明："选择数据在系统中的存储位置"
    
    2. 逻辑改进：
       - 目标位置的月份可以作为建议值，但不应强制覆盖
       - 允许用户将不同月份的数据导入到任意位置（灵活性）
       - 导入前明确显示：数据期间 vs 存储位置
    
    3. 长期方案：
       - 考虑移除目标位置中的月份选择
       - 或者让系统根据数据期间自动决定存储位置
       - 避免两个月份输入造成混淆
    """

print("紧急修复方案已生成！")
print("推荐立即实施方案1：完全移除自动覆盖逻辑")