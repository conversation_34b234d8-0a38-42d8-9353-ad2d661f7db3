import sys
import time
from unittest.mock import Mock

from PyQt5.QtWidgets import QApplication

_app = QApplication.instance() or QApplication(sys.argv)

from src.gui.prototype.prototype_main_window import PrototypeMainWindow
from src.core.event_bus import get_event_bus


def test_request_id_logs_on_page_change(monkeypatch):
    logs = []

    class DummyLogger:
        def info(self, msg):
            logs.append(msg)
        def debug(self, msg):
            logs.append(msg)
        def warning(self, msg):
            logs.append(msg)
        def error(self, msg, *args, **kwargs):
            logs.append(str(msg))

    # 使用最小依赖构造主窗体（传入 Mock 管理器）
    win = PrototypeMainWindow(config_manager=Mock(), db_manager=Mock(), dynamic_table_manager=Mock())
    win.logger = DummyLogger()

    # 主窗体订阅事件总线（避免依赖完整架构初始化）
    bus = get_event_bus()
    bus.subscribe("data_updated", win._on_new_data_updated, async_handler=False)

    # 通过 DummyService 模拟数据加载并发布事件
    class DummyService:
        def load_table_data(self, table_name, page, page_size, sort_columns=None, force_reload=False, request_id=None):
            from src.core.event_bus import DataUpdatedEvent
            data_event = DataUpdatedEvent(
                event_type="data_updated",
                table_name=table_name or "测试表",
                data=[],
                metadata={
                    'request_id': request_id or 'UNIT-RID',
                    'request_type': 'page_change',
                    'current_page': page,
                    'page_size': page_size,
                    'total_records': 1000,
                    'sort_columns': [],
                    'timestamp': time.time()
                }
            )
            bus.publish(data_event, wait_for_completion=False)
            class Resp: success=True; data=[]; total_records=1000
            return Resp()

    win.table_data_service = DummyService()

    # 设置当前表名，触发分页入口
    setattr(win, 'current_table_name', '测试表')

    # 为了避免深入到UI依赖，先设置最新token为同一rid，从而处理路径保守
    # 实际入口会生成 rid 并通过DataUpdatedEvent回到 _on_new_data_updated

    # 调用分页入口（新架构）
    win._on_page_changed_new_architecture(2)

    # 验证日志中包含入口与接收日志
    has_entry = any("📍[page-change] 入口" in msg for msg in logs)
    has_received = any("📥[data_event] received" in msg for msg in logs)
    assert has_entry and has_received

