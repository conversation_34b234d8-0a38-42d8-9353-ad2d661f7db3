#!/usr/bin/env python3
"""
P3级问题修复验证测试

验证：
1. 频繁的未找到默认选择路径警告是否已优化
2. 配置一致性警告优化是否生效
3. 排序列为空的频繁警告是否已优化
4. 占位符表名映射优化是否生效
5. 性能日志优化是否生效
"""

import sys
import os
import time
import re
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_navigation_path_warning_optimization():
    """测试导航路径警告优化"""
    print("🔍 测试导航路径警告优化...")
    
    try:
        navigation_panel_file = project_root / "src" / "gui" / "prototype" / "widgets" / "enhanced_navigation_panel.py"
        
        with open(navigation_panel_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        improvements_found = []
        
        # 检查1: 是否有日志节流机制
        if "log_throttle('nav-path-warning', 5.0)" in content:
            improvements_found.append("导航路径警告节流")
        if "🔧 [P3修复] 没有找到任何候选路径，可能数据尚未完全加载" in content:
            improvements_found.append("优化警告信息")
        if "🔧 [P3修复] 工资数据导航已刷新" in content:
            improvements_found.append("改进统计信息")
        if "🔧 [P3修复] 异常：有" in content and "个项目但未能设置默认路径" in content:
            improvements_found.append("异常情况诊断")
        if "🔧 [P3修复] 暂无数据，等待数据加载完成" in content:
            improvements_found.append("正常状态处理")
            
        if len(improvements_found) >= 4:
            print(f"✅ 导航路径警告优化已完成: {', '.join(improvements_found)}")
            return True
        else:
            print(f"❌ 导航路径警告优化不完整: 只找到 {improvements_found}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_configuration_consistency_warning_optimization():
    """测试配置一致性警告优化"""
    print("\n🔍 测试配置一致性警告优化...")
    
    try:
        field_registry_file = project_root / "src" / "modules" / "format_management" / "field_registry.py"
        
        with open(field_registry_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        improvements_found = []
        
        # 检查1: 是否有汇总报告机制
        if "🔧 [P3修复] 验证existing_display_fields配置，防止FormatRenderer降级（优化版本）" in content:
            improvements_found.append("优化版本验证")
        if "empty_fields_tables = []" in content:
            improvements_found.append("汇总统计机制")
        if "只显示前3个表，其余用数量表示" in content:
            improvements_found.append("限制显示数量")
        if "🔧 [P3修复] 验证字段类型一致性（优化版本）" in content:
            improvements_found.append("类型一致性优化")
        if "type_issues = []" in content:
            improvements_found.append("类型问题汇总")
        if "发现{len(type_issues)}个字段类型不一致问题" in content:
            improvements_found.append("问题数量汇总")
            
        if len(improvements_found) >= 5:
            print(f"✅ 配置一致性警告优化已完成: {', '.join(improvements_found)}")
            return True
        else:
            print(f"❌ 配置一致性警告优化不完整: 只找到 {improvements_found}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_sort_column_empty_warning_optimization():
    """测试排序列为空警告优化"""
    print("\n🔍 测试排序列为空警告优化...")
    
    try:
        main_window_file = project_root / "src" / "gui" / "prototype" / "prototype_main_window.py"
        
        with open(main_window_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        improvements_found = []
        
        # 检查1: 是否有日志节流机制
        if "log_throttle('empty-sort-columns', 3.0)" in content:
            improvements_found.append("排序列为空警告节流")
        if "log_throttle('sort-state-restore', 2.0)" in content:
            improvements_found.append("状态恢复日志节流")
        if "log_throttle('no-sort-state', 5.0)" in content:
            improvements_found.append("无状态警告节流")
        if "log_throttle('clear-sort-request', 5.0)" in content:
            improvements_found.append("清空排序请求节流")
        if "🔧 [P3修复] 排序列为空，尝试从状态管理器恢复" in content:
            improvements_found.append("优化警告信息")
        if "🔧 [P3修复] 发布清空排序请求，这是正常操作" in content:
            improvements_found.append("正常操作说明")
            
        if len(improvements_found) >= 5:
            print(f"✅ 排序列为空警告优化已完成: {', '.join(improvements_found)}")
            return True
        else:
            print(f"❌ 排序列为空警告优化不完整: 只找到 {improvements_found}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_placeholder_table_mapping_optimization():
    """测试占位符表名映射优化"""
    print("\n🔍 测试占位符表名映射优化...")
    
    try:
        data_request_manager_file = project_root / "src" / "core" / "unified_data_request_manager.py"
        
        with open(data_request_manager_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        improvements_found = []
        
        # 检查1: 是否有日志节流机制
        if "🔧 [P3修复] 处理特殊表名映射（优化日志频率）" in content:
            improvements_found.append("优化日志频率")
        if "log_throttle('default-table-mapping', 10.0)" in content:
            improvements_found.append("表名映射警告节流")
        if "log_throttle('table-mapping-success', 5.0)" in content:
            improvements_found.append("映射成功日志节流")
        if "🔧 [P3修复] 检测到占位符表名 'default_table'，尝试映射到实际表" in content:
            improvements_found.append("优化警告信息")
        if "🔧 [P3修复] 自动映射 'default_table'" in content:
            improvements_found.append("映射成功信息")
            
        if len(improvements_found) >= 4:
            print(f"✅ 占位符表名映射优化已完成: {', '.join(improvements_found)}")
            return True
        else:
            print(f"❌ 占位符表名映射优化不完整: 只找到 {improvements_found}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_performance_log_optimization():
    """测试性能日志优化"""
    print("\n🔍 测试性能日志优化...")
    
    try:
        table_widget_file = project_root / "src" / "gui" / "prototype" / "widgets" / "virtualized_expandable_table.py"
        
        with open(table_widget_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        improvements_found = []
        
        # 检查1: 是否有性能日志优化
        if "🔧 [P3修复] 优化性能日志输出频率" in content:
            improvements_found.append("性能日志优化")
        if "log_throttle('table-render', 5.0)" in content:
            improvements_found.append("渲染日志节流")
        if "self.logger.debug(f\"🔧 [P3修复] 优化渲染完成" in content:
            improvements_found.append("降级为debug日志")
        if "🔧 [P3修复] 只在性能异常时输出警告" in content:
            improvements_found.append("性能异常警告")
        if "render_result['elapsed_ms'] > 100" in content:
            improvements_found.append("性能阈值检查")
        if "🔧 [P3修复] 渲染性能较慢" in content:
            improvements_found.append("性能警告信息")
            
        if len(improvements_found) >= 5:
            print(f"✅ 性能日志优化已完成: {', '.join(improvements_found)}")
            return True
        else:
            print(f"❌ 性能日志优化不完整: 只找到 {improvements_found}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_code_syntax_after_p3_fixes():
    """测试P3修复后的代码语法"""
    print("\n🔍 测试P3修复后的代码语法...")
    
    try:
        # 测试主要修改的文件
        files_to_check = [
            "src/gui/prototype/widgets/enhanced_navigation_panel.py",
            "src/modules/format_management/field_registry.py",
            "src/gui/prototype/prototype_main_window.py",
            "src/core/unified_data_request_manager.py",
            "src/gui/prototype/widgets/virtualized_expandable_table.py"
        ]
        
        for file_path in files_to_check:
            full_path = project_root / file_path
            if not full_path.exists():
                print(f"❌ 文件不存在: {file_path}")
                return False
                
            with open(full_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 检查语法
            import ast
            try:
                ast.parse(content)
                print(f"✅ {file_path} 语法检查通过")
            except SyntaxError as e:
                print(f"❌ {file_path} 语法错误: {e}")
                return False
        
        print("✅ 所有P3修复文件语法检查通过")
        return True
        
    except Exception as e:
        print(f"❌ 语法检查失败: {e}")
        return False

def test_log_throttle_import_consistency():
    """测试日志节流导入一致性"""
    print("\n🔍 测试日志节流导入一致性...")
    
    try:
        # 检查所有使用log_throttle的文件是否正确导入
        files_with_throttle = [
            "src/gui/prototype/widgets/enhanced_navigation_panel.py",
            "src/gui/prototype/prototype_main_window.py",
            "src/core/unified_data_request_manager.py"
        ]
        
        import_issues = []
        
        for file_path in files_with_throttle:
            full_path = project_root / file_path
            if full_path.exists():
                with open(full_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查是否使用了log_throttle但没有导入
                if "log_throttle(" in content:
                    # 检查两种可能的导入方式
                    has_import = (
                        "from src.utils.log_throttle import log_throttle" in content or
                        "from src.utils.logging_utils import" in content and "log_throttle" in content
                    )
                    if not has_import:
                        import_issues.append(file_path)
        
        if not import_issues:
            print("✅ 所有文件的log_throttle导入都正确")
            return True
        else:
            print(f"❌ 以下文件缺少log_throttle导入: {import_issues}")
            return False
            
    except Exception as e:
        print(f"❌ 导入一致性检查失败: {e}")
        return False

def generate_p3_test_report(results):
    """生成P3测试报告"""
    print("\n" + "="*60)
    print("📊 P3级问题修复验证报告")
    print("="*60)
    
    total_tests = len(results)
    passed_tests = sum(results.values())
    
    print(f"总测试数: {total_tests}")
    print(f"通过测试: {passed_tests}")
    print(f"失败测试: {total_tests - passed_tests}")
    print(f"成功率: {passed_tests/total_tests*100:.1f}%")
    
    print("\n📋 详细结果:")
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    if passed_tests == total_tests:
        print("\n🎉 所有P3级问题修复验证通过！")
        print("\n📝 修复总结:")
        print("1. ✅ 导航路径警告优化已完成")
        print("   - 添加了日志节流机制，降低警告频率")
        print("   - 改进了警告信息的准确性和有用性")
        print("\n2. ✅ 配置一致性警告优化已完成")
        print("   - 实现了汇总报告机制，减少重复警告")
        print("   - 限制了详细信息的显示数量")
        print("\n3. ✅ 排序列为空警告优化已完成")
        print("   - 添加了多级日志节流机制")
        print("   - 优化了警告信息的表达方式")
        print("\n4. ✅ 占位符表名映射优化已完成")
        print("   - 降低了映射警告的输出频率")
        print("   - 改进了映射成功的日志级别")
        print("\n5. ✅ 性能日志优化已完成")
        print("   - 将常规性能日志降级为debug级别")
        print("   - 只在性能异常时输出警告")
        print("\n🎯 建议: 在实际运行中观察日志输出的改善效果")
    else:
        print("\n❌ 部分测试失败，需要进一步检查和修复")
    
    return passed_tests == total_tests

def main():
    """主测试函数"""
    print("🚀 开始P3级问题修复验证测试\n")
    
    # 运行所有测试
    results = {
        "导航路径警告优化": test_navigation_path_warning_optimization(),
        "配置一致性警告优化": test_configuration_consistency_warning_optimization(),
        "排序列为空警告优化": test_sort_column_empty_warning_optimization(),
        "占位符表名映射优化": test_placeholder_table_mapping_optimization(),
        "性能日志优化": test_performance_log_optimization(),
        "代码语法检查": test_code_syntax_after_p3_fixes(),
        "日志节流导入一致性": test_log_throttle_import_consistency()
    }
    
    # 生成报告
    success = generate_p3_test_report(results)
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
