import sys
import time
from PyQt5.QtWidgets import QApplication

_app = QApplication.instance() or QApplication(sys.argv)

from src.gui.prototype.prototype_main_window import PrototypeMainWindow
from src.core.event_bus import DataUpdatedEvent, get_event_bus


def test_discard_old_requests(monkeypatch):
    win = PrototypeMainWindow()
    setattr(win, 'current_table_name', '测试表')

    logs = []
    class DummyLogger:
        def info(self, msg): logs.append(msg)
        def debug(self, msg): logs.append(msg)
        def warning(self, msg): logs.append(msg)
        def error(self, msg, *a, **k): logs.append(str(msg))
    win.logger = DummyLogger()

    bus = get_event_bus()

    # 人工模拟两条事件：旧 rid 和新 rid
    old_rid = "RID-OLD"
    new_rid = "RID-NEW"

    # 先更新最新 token 为新 rid
    # 通过调用分页入口生成内部上下文并设置最新rid（直接调用内部方法简化）
    win._update_table_refresh_token('测试表', new_rid)

    # 发布旧请求事件
    bus.publish(DataUpdatedEvent(
        event_type="data_updated",
        table_name='测试表',
        data=[],
        metadata={'request_id': old_rid, 'request_type': 'page_change', 'current_page': 2, 'page_size': 50, 'total_records': 1000}
    ))

    # 发布新请求事件
    bus.publish(DataUpdatedEvent(
        event_type="data_updated",
        table_name='测试表',
        data=[],
        metadata={'request_id': new_rid, 'request_type': 'page_change', 'current_page': 3, 'page_size': 50, 'total_records': 1000}
    ))

    # 断言：日志出现 discard，且最终状态是新请求页码（3）
    assert any("discard" in m for m in logs)
    assert win.main_workspace.pagination_widget.get_current_page() == 3

