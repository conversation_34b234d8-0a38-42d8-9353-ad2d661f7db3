#!/usr/bin/env python3
"""
P0级问题修复验证测试

验证：
1. total_records 变量未定义问题是否已修复
2. 缓存系统组件缺失问题是否已修复
3. 系统能否正常启动和运行分页功能
"""

import sys
import os
import time
import re
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_code_syntax():
    """测试代码语法是否正确"""
    print("🔍 测试代码语法...")
    
    try:
        # 尝试导入主要模块
        from src.gui.prototype.prototype_main_window import PrototypeMainWindow
        print("✅ 主窗口模块导入成功")
        
        # 检查语法错误
        import ast
        main_window_file = project_root / "src" / "gui" / "prototype" / "prototype_main_window.py"
        
        with open(main_window_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 解析语法
        ast.parse(content)
        print("✅ 代码语法检查通过")
        
        return True
        
    except SyntaxError as e:
        print(f"❌ 语法错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 导入错误: {e}")
        return False

def test_total_records_fix():
    """测试 total_records 变量修复"""
    print("\n🔍 测试 total_records 变量修复...")
    
    try:
        main_window_file = project_root / "src" / "gui" / "prototype" / "prototype_main_window.py"
        
        with open(main_window_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 检查是否还有未定义的 total_records 变量使用
        # 查找 getattr(..., total_records) 模式
        pattern = r'getattr\([^,]+,\s*[\'"]total_records[\'"]\s*,\s*total_records\s*\)'
        matches = re.findall(pattern, content)
        
        if matches:
            print(f"❌ 仍然存在未修复的 total_records 变量使用: {len(matches)} 处")
            for match in matches:
                print(f"   - {match}")
            return False
        else:
            print("✅ 未发现未定义的 total_records 变量使用")
            
        # 检查是否有正确的修复
        fixed_pattern = r'getattr\([^,]+,\s*[\'"]total_records[\'"]\s*,\s*0\s*\)'
        fixed_matches = re.findall(fixed_pattern, content)
        
        if fixed_matches:
            print(f"✅ 发现正确的修复: {len(fixed_matches)} 处")
            return True
        else:
            print("⚠️  未发现预期的修复模式")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_cache_loader_fix():
    """测试缓存加载器修复"""
    print("\n🔍 测试缓存加载器修复...")
    
    try:
        main_window_file = project_root / "src" / "gui" / "prototype" / "prototype_main_window.py"
        
        with open(main_window_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 检查是否有安全的缓存加载器使用
        # 查找直接使用 self._optimized_cache_loader 而没有检查的情况
        lines = content.split('\n')
        unsafe_usage = []
        
        for i, line in enumerate(lines, 1):
            if 'self._optimized_cache_loader.' in line:
                # 检查前面几行是否有安全检查
                context_start = max(0, i-5)
                context_lines = lines[context_start:i]
                context = '\n'.join(context_lines)
                
                # 检查是否有 hasattr 或其他安全检查
                if 'hasattr' not in context and 'if self._optimized_cache_loader' not in context:
                    unsafe_usage.append(f"第{i}行: {line.strip()}")
        
        if unsafe_usage:
            print(f"❌ 发现不安全的缓存加载器使用: {len(unsafe_usage)} 处")
            for usage in unsafe_usage:
                print(f"   - {usage}")
            return False
        else:
            print("✅ 缓存加载器使用已安全化")
            
        # 检查是否有正确的安全检查
        safe_pattern = r'hasattr\(self,\s*[\'"]_optimized_cache_loader[\'"]'
        safe_matches = re.findall(safe_pattern, content)
        
        if safe_matches:
            print(f"✅ 发现安全检查: {len(safe_matches)} 处")
            return True
        else:
            print("⚠️  未发现预期的安全检查模式")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_system_startup():
    """测试系统启动（简单检查）"""
    print("\n🔍 测试系统启动能力...")
    
    try:
        # 尝试创建主要组件实例（不启动GUI）
        from src.modules.system_config.config_manager import ConfigManager
        from src.modules.data_storage.database_manager import DatabaseManager
        
        print("✅ 核心模块导入成功")
        
        # 检查配置文件
        config_file = project_root / "config.json"
        if config_file.exists():
            print("✅ 配置文件存在")
        else:
            print("⚠️  配置文件不存在，但不影响测试")
            
        return True
        
    except Exception as e:
        print(f"❌ 系统启动测试失败: {e}")
        return False

def generate_test_report(results):
    """生成测试报告"""
    print("\n" + "="*60)
    print("📊 P0级问题修复验证报告")
    print("="*60)
    
    total_tests = len(results)
    passed_tests = sum(results.values())
    
    print(f"总测试数: {total_tests}")
    print(f"通过测试: {passed_tests}")
    print(f"失败测试: {total_tests - passed_tests}")
    print(f"成功率: {passed_tests/total_tests*100:.1f}%")
    
    print("\n📋 详细结果:")
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    if passed_tests == total_tests:
        print("\n🎉 所有P0级问题修复验证通过！")
        print("\n📝 修复总结:")
        print("1. ✅ total_records 变量未定义问题已修复")
        print("2. ✅ 缓存系统组件缺失问题已修复")
        print("3. ✅ 代码语法正确，系统可以正常启动")
        print("\n🎯 建议: 可以进行实际运行测试验证修复效果")
    else:
        print("\n❌ 部分测试失败，需要进一步检查和修复")
    
    return passed_tests == total_tests

def main():
    """主测试函数"""
    print("🚀 开始P0级问题修复验证测试\n")
    
    # 运行所有测试
    results = {
        "代码语法检查": test_code_syntax(),
        "total_records变量修复": test_total_records_fix(),
        "缓存加载器修复": test_cache_loader_fix(),
        "系统启动能力": test_system_startup()
    }
    
    # 生成报告
    success = generate_test_report(results)
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
