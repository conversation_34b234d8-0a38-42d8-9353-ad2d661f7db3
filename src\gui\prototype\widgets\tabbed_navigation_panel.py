"""
TabbedNavigationPanel - 标签式数据导航容器

按方案B+C：在左侧导航区域使用Material风格的Tab容器，承载两套独立的数据导航：
- 工资表
- 异动表

目标：
- 与现有 EnhancedNavigationPanel 完全兼容的对外信号与方法（最小侵入接入）
- 默认只激活默认标签页，另一个标签页在首次切换时再触发自动选择最新数据
- 视觉风格复用 MaterialTabWidget
"""

from typing import Optional

from PyQt5.QtCore import pyqtSignal
from PyQt5.QtWidgets import QWidget, QVBoxLayout, QTreeWidget, QGraphicsDropShadowEffect, QFrame
from PyQt5.QtCore import Qt
from typing import Dict

from .material_tab_widget import MaterialTabWidget
from .enhanced_navigation_panel import EnhancedNavigationPanel
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.gui.prototype.managers.communication_manager import ComponentCommunicationManager
from src.utils.log_config import setup_logger


class TabbedNavigationPanel(QWidget):
    """
    标签式数据导航容器。

    对外保持与 `EnhancedNavigationPanel` 相同的关键信号与方法，
    以便在 `PrototypeMainWindow` 中做到无缝替换。
    """

    # 对外信号：与子面板一致
    navigation_changed = pyqtSignal(str, dict)  # (path, context)
    tab_changed = pyqtSignal(str)  # 'wage' | 'change'

    def __init__(
        self,
        dynamic_table_manager: DynamicTableManager,
        comm_manager: ComponentCommunicationManager,
        parent: Optional[QWidget] = None,
        default_tab: str = "wage",  # "wage" | "change"
        expand_latest_on_start: bool = True,
        expand_change_latest_on_start: bool = True,
    ):
        super().__init__(parent)

        self.logger = setup_logger(__name__ + ".TabbedNavigationPanel")
        self.dynamic_table_manager = dynamic_table_manager
        self.comm_manager = comm_manager
        self.default_tab = default_tab if default_tab in ("wage", "change") else "wage"
        self.expand_latest_on_start = expand_latest_on_start
        self.expand_change_latest_on_start = expand_change_latest_on_start

        self._wage_activated_once = False
        self._change_activated_once = False

        self._init_ui()
        self._connect_signals()
        # 初始响应式适配（若主窗口已建立响应式管理则会再次驱动）
        try:
            self.handle_responsive_change('md', {'sidebar_width': 280, 'font_scale': 1.0, 'sidebar_mode': 'fixed'})
        except Exception:
            pass

    # ----------------------------
    # UI
    # ----------------------------
    def _init_ui(self) -> None:
        layout = QVBoxLayout(self)
        # 方案B：左侧卡片与窗体边距增大，底部与右侧分页对齐
        layout.setContentsMargins(16, 8, 8, 16)  # 增加底部边距
        layout.setSpacing(0)
        
        # 设置大小策略，使面板垂直方向扩展填充可用空间
        from PyQt5.QtWidgets import QSizePolicy
        self.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Expanding)

        self.tab_widget = MaterialTabWidget(self)

        # 子导航面板
        self.wage_panel = EnhancedNavigationPanel(
            dynamic_table_manager=self.dynamic_table_manager,
            comm_manager=self.comm_manager,
            state_file="state/user/navigation_state_wage.json",
            root_mode="wage"
        )

        self.change_panel = EnhancedNavigationPanel(
            dynamic_table_manager=self.dynamic_table_manager,
            comm_manager=self.comm_manager,
            state_file="state/user/navigation_state_change.json",
            root_mode="change"
        )

        # 默认仅激活默认Tab，其他Tab先阻塞信号，避免启动时双重自动选择触发
        if self.default_tab == "wage":
            self._wage_activated_once = True
            self.change_panel.blockSignals(True)
            # 可选：为“异动表”子面板预展开最新期（不触发导航事件）
            if self.expand_change_latest_on_start:
                try:
                    if hasattr(self.change_panel, "auto_select_latest_data"):
                        self.change_panel.auto_select_latest_data()
                except Exception:
                    pass
        else:
            self._change_activated_once = True
            self.wage_panel.blockSignals(True)

        # 保存完整标签文本，支持断点切换为icon-only
        self._tab_text_full = {
            'wage': "工资表",
            'change': "异动表",
        }
        self._tab_text_compact = {
            'wage': "💰",
            'change': "🔄",
        }

        self.tab_widget.add_material_tab(self.wage_panel, self._tab_text_full['wage'])
        self.tab_widget.add_material_tab(self.change_panel, self._tab_text_full['change'])

        self.tab_widget.setCurrentIndex(0 if self.default_tab == "wage" else 1)
        layout.addWidget(self.tab_widget)

        # 卡片化外观与阴影
        try:
            self.setObjectName("TabbedNavigationPanelCard")
            self.setStyleSheet(
                """
                QWidget#TabbedNavigationPanelCard {
                    background-color: #FFFFFF;
                    border: 1px solid #E0E0E0;
                    border-radius: 10px;
                }
                """
            )
            shadow = QGraphicsDropShadowEffect(self)
            shadow.setBlurRadius(18)
            shadow.setOffset(0, 4)
            shadow.setColor(Qt.gray)
            self.setGraphicsEffect(shadow)
        except Exception:
            pass

    def _connect_signals(self) -> None:
        # 子面板导航事件桥接
        self.wage_panel.navigation_changed.connect(self._emit_navigation_changed)
        self.change_panel.navigation_changed.connect(self._emit_navigation_changed)

        # 标签切换
        self.tab_widget.tab_changed.connect(self._on_tab_changed)

    # ----------------------------
    # 行为
    # ----------------------------
    def _emit_navigation_changed(self, path: str, context: dict) -> None:
        self.navigation_changed.emit(path, context)
        # 抽屉模式下，选中后自动收起
        try:
            if getattr(self, '_last_sidebar_mode', 'fixed') == 'drawer':
                self.hide_drawer()
        except Exception:
            pass

    def _on_tab_changed(self, index: int) -> None:
        # 首次激活时解除信号阻塞并触发一次“最新数据自动选择”
        if index == 0:  # 工资表
            self.tab_changed.emit('wage')
            # 抽屉模式下切换Tab时自动展开抽屉，避免误以为空白
            try:
                if getattr(self, '_last_sidebar_mode', 'fixed') == 'drawer':
                    self.show_drawer()
            except Exception:
                pass
            if not self._wage_activated_once:
                self._wage_activated_once = True
                try:
                    self.wage_panel.blockSignals(False)
                    if self.expand_latest_on_start and hasattr(self.wage_panel, "auto_select_latest_data"):
                        self.wage_panel.auto_select_latest_data()
                except Exception as e:
                    self.logger.warning(f"激活工资表子面板失败: {e}")
        elif index == 1:  # 异动表
            self.tab_changed.emit('change')
            try:
                if getattr(self, '_last_sidebar_mode', 'fixed') == 'drawer':
                    self.show_drawer()
            except Exception:
                pass
            if not self._change_activated_once:
                self._change_activated_once = True
                try:
                    self.change_panel.blockSignals(False)
                    if self.expand_latest_on_start and hasattr(self.change_panel, "auto_select_latest_data"):
                        self.change_panel.auto_select_latest_data()
                except Exception as e:
                    self.logger.warning(f"激活异动表子面板失败: {e}")

    def _filter_root_for_panel(self, panel: EnhancedNavigationPanel, keep_root_text: str) -> None:
        return

    # ----------------------------
    # 与主窗口兼容的方法（转发到当前或特定子面板）
    # ----------------------------
    def _get_active_panel(self) -> EnhancedNavigationPanel:
        return self.wage_panel if self.tab_widget.currentIndex() == 0 else self.change_panel

    def force_refresh_salary_data(self) -> None:
        # 仅工资表有效
        try:
            if hasattr(self.wage_panel, "force_refresh_salary_data"):
                self.wage_panel.force_refresh_salary_data()
        except Exception as e:
            self.logger.warning(f"force_refresh_salary_data 调用失败: {e}")

    def refresh_navigation_data(self) -> None:
        try:
            panel = self._get_active_panel()
            if hasattr(panel, "refresh_navigation_data"):
                panel.refresh_navigation_data()
            # 若当前活动面板根节点不存在，尝试回退为显示全部根，避免空白
            try:
                tree: QTreeWidget = panel.tree_widget
                roots = [tree.topLevelItem(i) for i in range(tree.topLevelItemCount())]
                visible_roots = [it for it in roots if it and not it.isHidden()]
                if not visible_roots and roots:
                    for it in roots:
                        if it:
                            it.setHidden(False)
            except Exception:
                pass
        except Exception as e:
            self.logger.warning(f"refresh_navigation_data 调用失败: {e}")

    def navigate_to_path(self, path: str) -> None:
        try:
            # 按路径前缀选择目标面板（简单路由）：包含"异动"走异动表，否则默认工资表
            target = self.change_panel if ("异动" in path or "异动人员" in path) else self.wage_panel
            if self.tab_widget.currentWidget() is not target:
                self.tab_widget.setCurrentWidget(target)
            if hasattr(target, "navigate_to_path"):
                target.navigate_to_path(path)
        except Exception as e:
            self.logger.warning(f"navigate_to_path 调用失败: {e}")

    def _cancel_latest_path_retry(self) -> None:
        try:
            for panel in (self.wage_panel, self.change_panel):
                if hasattr(panel, "_cancel_latest_path_retry"):
                    panel._cancel_latest_path_retry()
        except Exception as e:
            self.logger.debug(f"取消自动选择重试失败(忽略): {e}")

    def refresh_navigation_state(self) -> None:
        try:
            panel = self._get_active_panel()
            if hasattr(panel, "refresh_navigation_state"):
                panel.refresh_navigation_state()
        except Exception as e:
            self.logger.warning(f"refresh_navigation_state 调用失败: {e}")

    def sync_with_global_state(self, global_state: dict) -> None:
        try:
            panel = self._get_active_panel()
            if hasattr(panel, "sync_with_global_state"):
                panel.sync_with_global_state(global_state)
        except Exception as e:
            self.logger.warning(f"sync_with_global_state 调用失败: {e}")

    # 响应式接口（由主窗口或通信管理器调用）
    def handle_responsive_change(self, breakpoint: str, config: Dict[str, any]) -> None:
        try:
            # 将sidebar宽度应用到容器
            sidebar_width = config.get('sidebar_width', 280)
            sidebar_mode = config.get('sidebar_mode', 'fixed')
            self._last_sidebar_mode = sidebar_mode
            if sidebar_mode == 'drawer':
                # 抽屉模式：默认隐藏，使用遮罩控制显示
                self.hide()
            else:
                # 固定模式：显示并设置宽度
                self.show()
                self.setFixedWidth(sidebar_width)

            # 标签文本在xs/sm使用icon-only，md及以上使用完整文案
            if breakpoint in ('xs', 'sm'):
                self._set_tab_texts(compact=True)
            else:
                self._set_tab_texts(compact=False)

            # 同步到子面板（字体/控件密度等在子面板内部实现）
            for panel in (self.wage_panel, self.change_panel):
                if hasattr(panel, 'handle_responsive_change'):
                    panel.handle_responsive_change(breakpoint, config)
        except Exception as e:
            self.logger.debug(f"handle_responsive_change 失败(忽略): {e}")

    def _set_tab_texts(self, compact: bool) -> None:
        try:
            if compact:
                self.tab_widget.setTabText(0, self._tab_text_compact['wage'])
                self.tab_widget.setTabText(1, self._tab_text_compact['change'])
            else:
                self.tab_widget.setTabText(0, self._tab_text_full['wage'])
                self.tab_widget.setTabText(1, self._tab_text_full['change'])
        except Exception:
            pass

    # Drawer显隐控制（供主窗口或快捷键使用）
    def show_drawer(self):
        try:
            self.show()
            self.raise_()
            # 显示遮罩
            try:
                parent_widget = self.parent() if self.parent() else None
                if hasattr(self, '_overlay') and self._overlay and parent_widget:
                    self._overlay.setGeometry(parent_widget.rect())
                    self._overlay.show()
            except Exception:
                pass
        except Exception:
            pass

    def hide_drawer(self):
        try:
            self.hide()
            try:
                if hasattr(self, '_overlay') and self._overlay:
                    self._overlay.hide()
            except Exception:
                pass
        except Exception:
            pass

    def toggle_drawer(self):
        try:
            if self.isVisible():
                self.hide_drawer()
            else:
                self.show_drawer()
        except Exception:
            pass

    # 工具方法：判断是否抽屉模式
    def is_drawer_mode(self) -> bool:
        try:
            return getattr(self, '_last_sidebar_mode', 'fixed') == 'drawer'
        except Exception:
            return False

    # 工具方法：高亮并定位到最新期（可指定 panel：'wage'|'change'）
    def spotlight_latest(self, target: str = None) -> None:
        try:
            panel = None
            if target == 'wage':
                panel = self.wage_panel
            elif target == 'change':
                panel = self.change_panel
            else:
                panel = self._get_active_panel()

            if hasattr(panel, 'auto_select_latest_data'):
                panel.auto_select_latest_data()
        except Exception:
            pass


