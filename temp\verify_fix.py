#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证异动表导航显示问题修复效果
"""

import sqlite3
import os
import sys
import json

def verify_database_metadata():
    """验证数据库元数据修复"""
    print("🔍 验证数据库元数据修复...")
    
    db_path = "data/db/salary_system.db"
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查异动表元数据
        cursor.execute("SELECT table_name, table_type FROM table_metadata WHERE table_name LIKE 'change_data_%';")
        change_metadata = cursor.fetchall()
        
        print(f"📊 异动表元数据记录: {len(change_metadata)} 个")
        for row in change_metadata:
            table_name, table_type = row
            print(f"  ✅ {table_name} -> 类型: {table_type}")
            
            if table_type != 'change_data':
                print(f"  ❌ 错误：{table_name} 的类型应该是 'change_data'，实际是 '{table_type}'")
                return False
        
        # 检查change_data类型的记录总数
        cursor.execute("SELECT COUNT(*) FROM table_metadata WHERE table_type='change_data';")
        change_count = cursor.fetchone()[0]
        print(f"🎯 change_data类型记录总数: {change_count}")
        
        if change_count == 0:
            print("❌ 没有找到任何change_data类型的记录")
            return False
        
        conn.close()
        print("✅ 数据库元数据修复验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 验证数据库元数据时出错: {e}")
        return False

def verify_field_mappings():
    """验证字段映射配置"""
    print("\n🔍 验证字段映射配置...")
    
    mappings_path = "state/data/field_mappings.json"
    if not os.path.exists(mappings_path):
        print(f"❌ 字段映射文件不存在: {mappings_path}")
        return False
    
    try:
        with open(mappings_path, 'r', encoding='utf-8') as f:
            mappings = json.load(f)
        
        table_mappings = mappings.get("table_mappings", {})
        
        # 检查是否有change_data配置
        if "change_data" in table_mappings:
            change_config = table_mappings["change_data"]
            print("✅ 找到异动表字段映射配置")
            
            # 检查关键配置项
            field_mappings = change_config.get("field_mappings", {})
            field_types = change_config.get("field_types", {})
            display_fields = change_config.get("display_fields", [])
            hidden_fields = change_config.get("hidden_fields", [])
            
            print(f"  📋 字段映射: {len(field_mappings)} 个")
            print(f"  🏷️  字段类型: {len(field_types)} 个")
            print(f"  👁️  显示字段: {len(display_fields)} 个")
            print(f"  🙈 隐藏字段: {len(hidden_fields)} 个")
            
            # 检查关键字段是否存在
            key_fields = ["employee_id", "employee_name", "department", "total_salary"]
            missing_fields = [field for field in key_fields if field not in field_mappings]
            
            if missing_fields:
                print(f"  ❌ 缺少关键字段映射: {missing_fields}")
                return False
            else:
                print("  ✅ 关键字段映射完整")
            
        else:
            print("❌ 未找到异动表字段映射配置")
            return False
        
        print("✅ 字段映射配置验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 验证字段映射配置时出错: {e}")
        return False

def verify_table_creation_logic():
    """验证表创建逻辑修复"""
    print("\n🔍 验证表创建逻辑修复...")
    
    # 检查dynamic_table_manager.py中的修复
    manager_path = "src/modules/data_storage/dynamic_table_manager.py"
    if not os.path.exists(manager_path):
        print(f"❌ 文件不存在: {manager_path}")
        return False
    
    try:
        with open(manager_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查_record_table_metadata方法是否包含change_data类型判断
        if 'elif "change_data" in schema.table_name:' in content:
            print("✅ 找到change_data类型判断逻辑")
        else:
            print("❌ 未找到change_data类型判断逻辑")
            return False
        
        # 检查create_change_data_table方法是否存在
        if 'def create_change_data_table(' in content:
            print("✅ 找到create_change_data_table方法")
        else:
            print("❌ 未找到create_change_data_table方法")
            return False
        
        print("✅ 表创建逻辑修复验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 验证表创建逻辑时出错: {e}")
        return False

def verify_format_renderer():
    """验证格式渲染器修复"""
    print("\n🔍 验证格式渲染器修复...")
    
    renderer_path = "src/modules/format_management/format_renderer.py"
    if not os.path.exists(renderer_path):
        print(f"❌ 文件不存在: {renderer_path}")
        return False
    
    try:
        with open(renderer_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否包含异动表的默认字段配置
        if 'change_data' in content and '异动表修复' in content:
            print("✅ 找到异动表默认字段配置")
        else:
            print("❌ 未找到异动表默认字段配置")
            return False
        
        print("✅ 格式渲染器修复验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 验证格式渲染器时出错: {e}")
        return False

def main():
    """主验证函数"""
    print("🚀 开始验证异动表导航显示问题修复效果...\n")
    
    results = []
    
    # 验证各个修复点
    results.append(("数据库元数据修复", verify_database_metadata()))
    results.append(("字段映射配置", verify_field_mappings()))
    results.append(("表创建逻辑修复", verify_table_creation_logic()))
    results.append(("格式渲染器修复", verify_format_renderer()))
    
    # 汇总结果
    print("\n" + "="*50)
    print("📊 修复验证结果汇总:")
    print("="*50)
    
    success_count = 0
    for name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{name}: {status}")
        if result:
            success_count += 1
    
    print(f"\n总体结果: {success_count}/{len(results)} 项验证通过")
    
    if success_count == len(results):
        print("\n🎉 所有修复验证通过！异动表导航显示问题已成功修复。")
        print("\n📋 修复内容总结:")
        print("1. ✅ 修复了table_metadata中异动表的table_type字段")
        print("2. ✅ 修复了数据导入流程中的元数据创建逻辑")
        print("3. ✅ 添加了异动表的字段映射配置")
        print("4. ✅ 完善了异动表的默认字段显示配置")
        print("\n🔄 建议重启系统以使修复生效。")
        return True
    else:
        print(f"\n❌ 修复验证失败，有 {len(results) - success_count} 项未通过验证。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
