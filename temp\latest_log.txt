2025-08-14 22:20:05.829 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:479 | 可见行数据顺序: ['19289006', '19339009']
2025-08-14 22:20:05.829 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:442 | 表格数据已设置: 2 行, 17 列
2025-08-14 22:20:05.831 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:417 | 🔧 [新架构] 排序管理器切换到表格: salary_data_2025_12_retired_employees
2025-08-14 22:20:05.832 | INFO     | src.gui.prototype.widgets.column_sort_manager:_reload_field_mapping_for_table:448 | 🔧 [新架构] 为表格 salary_data_2025_12_retired_employees 重新加载 21 个字段映射
2025-08-14 22:20:05.834 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 2 行, 耗时: 41.1ms
2025-08-14 22:20:05.835 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7922 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-14 22:20:05.835 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7935 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-14 22:20:05.836 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1723 | 🆕 [列宽保存] 未找到表格 salary_data_2025_12_retired_employees 的列宽配置
2025-08-14 22:20:05.836 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-14 22:20:05.836 | INFO     | src.gui.prototype.prototype_main_window:set_data:997 | 📊[pagination-write] 跳过写入当前页数据量(total=2)，保持 total=0，等待数据事件纠正
2025-08-14 22:20:05.837 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3887 | 🔧 [P1-1修复] 数据已成功设置到UI，使用新映射表头: 2行, 17列
2025-08-14 22:20:05.837 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 17 个, 行号起始 1, 共 2 行
2025-08-14 22:20:05.837 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3903 | 🔧 [P0-新5修复] 表头强制刷新完成: salary_data_2025_12_retired_employees, 传递参数: 17个表头
2025-08-14 22:20:05.838 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3907 | 🔧 [统一数据设置] 数据已成功设置到UI: 2行, 17列
2025-08-14 22:20:05.838 | INFO     | src.gui.widgets.pagination_widget:batch_update:472 | 📊[pagination-write] batch_update | src=_on_new_data_updated/cached_load | rid=SV-1755181205772-80ccddc9-C | total: 0->2, page: 1->1, size: 50->50, pages: 1->1
2025-08-14 22:20:05.839 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3941 | ✅[pagination-state] now | page=1 / pages=1 / total=2 | rid=SV-1755181205772-80ccddc9-C
2025-08-14 22:20:05.839 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3954 | 🔍 [FINAL修复] 最终调用set_pagination_state: 第1页, 记录1-2 | rid=SV-1755181205772-80ccddc9-C
2025-08-14 22:20:05.839 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4166 | 🔍 [表格调试] ================== set_pagination_state 开始 ==================
2025-08-14 22:20:05.840 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4167 | 🔍 [表格调试] 收到分页状态参数: {'current_page': 1, 'page_size': 50, 'total_records': 2, 'start_record': 1, 'end_record': 2}
2025-08-14 22:20:05.840 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4168 | 🔍 [表格调试] 当前表格行数: 2
2025-08-14 22:20:05.840 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4169 | 🔍 [表格调试] 当前表格列数: 17
2025-08-14 22:20:05.841 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4171 | 🔧 [分页行号修复] 接收分页状态: 第1页, 记录1-2
2025-08-14 22:20:05.841 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1723 | 🆕 [列宽保存] 未找到表格 salary_data_2025_12_retired_employees 的列宽配置
2025-08-14 22:20:05.842 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4198 | 🔧 [P0-3修复] 分页时已恢复列宽设置: salary_data_2025_12_retired_employees
2025-08-14 22:20:05.842 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4206 | 🔧 [分页行号修复] 表格状态: 当前行数=2, 期望行数=2
2025-08-14 22:20:05.842 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 17 个, 行号起始 1, 共 2 行
2025-08-14 22:20:05.843 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_force_update_pagination_row_numbers:4286 | 🔧 [分页行号修复] 强制更新完成: 起始记录1, 共2行
2025-08-14 22:20:05.846 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3958 | 🔍 [FINAL修复] set_pagination_state最终调用完成
2025-08-14 22:20:05.864 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:379 | 已清理所有缓存
2025-08-14 22:20:05.864 | INFO     | src.gui.prototype.prototype_main_window:_validate_component_state_consistency:10878 | ✅ [新架构] 组件状态一致性验证通过
2025-08-14 22:20:05.865 | INFO     | src.gui.prototype.prototype_main_window:complete_navigation_transition:10841 | 🆕 [新架构] 导航迁移完成
2025-08-14 22:20:05.866 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3972 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-08-14 22:20:05.866 | INFO     | src.services.table_data_service:load_table_data:426 | 📨[data_event] published(cache)
2025-08-14 22:20:05.866 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7520 | 🆕 新架构数据加载请求已发送，等待事件系统处理
2025-08-14 22:20:05.936 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7957 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: salary_data_2025_12_retired_employees
2025-08-14 22:20:08.225 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2095 | MainWorkspaceArea 响应式适配: xl
2025-08-14 22:20:09.481 | INFO     | src.gui.prototype.prototype_main_window:_refresh_table_data:8583 | 🔁[scope=table] 表格刷新被触发 | table=salary_data_2025_12_retired_employees | page=1 | size=50 | total_before=2 | request_id=TR-1755181209481-f7b53695
2025-08-14 22:20:09.482 | INFO     | src.gui.prototype.prototype_main_window:_refresh_table_data:8594 | 🔁[scope=table] 使用新架构刷新表格数据: salary_data_2025_12_retired_employees
2025-08-14 22:20:09.483 | INFO     | src.services.table_data_service:load_table_data:366 | 加载表格数据: 页码: 1
2025-08-14 22:20:09.484 | INFO     | src.services.table_data_service:load_table_data:390 | [缓存命中] 使用缓存数据: 第1页
2025-08-14 22:20:09.484 | INFO     | src.services.table_data_service:load_table_data:401 | 📨[data_event] publish(cache) | table=salary_data_2025_12_retired_employees | rows=2 | page=1 | size=50 | total=2 | request_id=SV-1755181209484-957265c3-C
2025-08-14 22:20:09.485 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3785 | 📥[data_event] received | table=salary_data_2025_12_retired_employees | request_id=SV-1755181209484-957265c3-C
2025-08-14 22:20:09.485 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3817 | 数据内容: 2行 x 21列
2025-08-14 22:20:09.486 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3843 | 🔧 [数据更新] 接收到cached_load操作的数据更新事件: salary_data_2025_12_retired_employees, 2行
2025-08-14 22:20:09.488 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7256 | 表 salary_data_2025_12_retired_employees 没有用户偏好设置，显示所有可见字段
2025-08-14 22:20:09.489 | INFO     | src.gui.prototype.prototype_main_window:set_data:873 | 通过公共接口设置新的表格数据(DataFrame): 2 行
2025-08-14 22:20:09.490 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7256 | 表 salary_data_2025_12_retired_employees 没有用户偏好设置，显示所有可见字段
2025-08-14 22:20:09.492 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 2行，表名: salary_data_2025_12_retired_employees
2025-08-14 22:20:09.494 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=19289006, 薪资=N/A
2025-08-14 22:20:09.495 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=19339009, 薪资=N/A
2025-08-14 22:20:09.495 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:5271 | 最大可见行数已更新: 10 -> 10
2025-08-14 22:20:09.496 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5324 | 根据数据量(2)自动调整最大可见行数为: 10
2025-08-14 22:20:09.498 | INFO     | src.modules.format_management.format_renderer:render_dataframe:107 | 🎯 [格式渲染] 隐藏字段配置: ['id', 'updated_at', 'sequence_number', 'created_at']
2025-08-14 22:20:09.498 | INFO     | src.modules.format_management.format_renderer:render_dataframe:115 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-08-14 22:20:09.502 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 增发一次性生活补贴 -> 英文名: one_time_living_allowance -> 类型: float
2025-08-14 22:20:09.504 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-08-14 22:20:09.505 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-08-14 22:20:09.506 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 备注 -> 英文名: remarks -> 类型: string
2025-08-14 22:20:09.507 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 月份 -> 英文名: month -> 类型: month_string_extract_last_two
2025-08-14 22:20:09.508 | INFO     | src.modules.format_management.field_registry:get_display_fields:1552 | 🔧 [P0-2修复] display_order转换完成: 17个字段，原始字段数: 17
2025-08-14 22:20:09.508 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | 🔧 [DEBUG] table_type=retired_employees, display_fields=17个字段
2025-08-14 22:20:09.509 | INFO     | src.modules.format_management.format_renderer:render_dataframe:211 | 🔧 [P0修复] 配置字段匹配完成: 成功匹配 17/17 个字段
2025-08-14 22:20:09.510 | INFO     | src.modules.format_management.format_renderer:render_dataframe:224 | 🔧 [P0修复] 字段重排完成: 原始17个 -> 最终17个字段
2025-08-14 22:20:09.510 | INFO     | src.modules.format_management.format_renderer:render_dataframe:240 | 🎨 [格式渲染] DataFrame格式化完成: retired_employees, 行数: 2, 列数: 17
2025-08-14 22:20:09.511 | INFO     | src.modules.format_management.unified_format_manager:format_data:367 | 🎯 [统一格式管理] 数据格式化完成: retired_employees, 行数: 2, 列数: 17
2025-08-14 22:20:09.513 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 2行数据
2025-08-14 22:20:09.516 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:52 | [数据流追踪] 开始小数据集渲染: 2行 x 17列
2025-08-14 22:20:09.516 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:112 | [数据流追踪] 小数据集渲染完成: 耗时0.0ms, 平均每行0.00ms
2025-08-14 22:20:09.517 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=2, 渲染时间=0.0ms, 策略=small_dataset
2025-08-14 22:20:09.517 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:279 | [数据流追踪] 选择小数据集渲染策略
2025-08-14 22:20:09.518 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19289006
2025-08-14 22:20:09.518 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 19339009
2025-08-14 22:20:09.519 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 2
2025-08-14 22:20:09.519 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[0]: 工号=19289006, 薪资=N/A
2025-08-14 22:20:09.520 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[1]: 工号=19339009, 薪资=N/A
2025-08-14 22:20:09.520 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:479 | 可见行数据顺序: ['19289006', '19339009']
2025-08-14 22:20:09.521 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:442 | 表格数据已设置: 2 行, 17 列
2025-08-14 22:20:09.521 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 2 行, 耗时: 27.1ms
2025-08-14 22:20:09.522 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7922 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-14 22:20:09.522 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7935 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-14 22:20:09.523 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1723 | 🆕 [列宽保存] 未找到表格 salary_data_2025_12_retired_employees 的列宽配置
2025-08-14 22:20:09.523 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-14 22:20:09.524 | INFO     | src.gui.prototype.prototype_main_window:set_data:997 | 📊[pagination-write] 跳过写入当前页数据量(total=2)，保持 total=2，等待数据事件纠正
2025-08-14 22:20:09.528 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3887 | 🔧 [P1-1修复] 数据已成功设置到UI，使用新映射表头: 2行, 17列
2025-08-14 22:20:09.528 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 17 个, 行号起始 1, 共 2 行
2025-08-14 22:20:09.529 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3903 | 🔧 [P0-新5修复] 表头强制刷新完成: salary_data_2025_12_retired_employees, 传递参数: 17个表头
2025-08-14 22:20:09.529 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3907 | 🔧 [统一数据设置] 数据已成功设置到UI: 2行, 17列
2025-08-14 22:20:09.530 | INFO     | src.gui.widgets.pagination_widget:batch_update:472 | 📊[pagination-write] batch_update | src=_on_new_data_updated/cached_load | rid=SV-1755181209484-957265c3-C | total: 2->2, page: 1->1, size: 50->50, pages: 1->1
2025-08-14 22:20:09.530 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3941 | ✅[pagination-state] now | page=1 / pages=1 / total=2 | rid=SV-1755181209484-957265c3-C
2025-08-14 22:20:09.531 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3954 | 🔍 [FINAL修复] 最终调用set_pagination_state: 第1页, 记录1-2 | rid=SV-1755181209484-957265c3-C
2025-08-14 22:20:09.531 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4166 | 🔍 [表格调试] ================== set_pagination_state 开始 ==================
2025-08-14 22:20:09.532 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4167 | 🔍 [表格调试] 收到分页状态参数: {'current_page': 1, 'page_size': 50, 'total_records': 2, 'start_record': 1, 'end_record': 2}
2025-08-14 22:20:09.532 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4168 | 🔍 [表格调试] 当前表格行数: 2
2025-08-14 22:20:09.533 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4169 | 🔍 [表格调试] 当前表格列数: 17
2025-08-14 22:20:09.533 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4171 | 🔧 [分页行号修复] 接收分页状态: 第1页, 记录1-2
2025-08-14 22:20:09.534 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1723 | 🆕 [列宽保存] 未找到表格 salary_data_2025_12_retired_employees 的列宽配置
2025-08-14 22:20:09.534 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4198 | 🔧 [P0-3修复] 分页时已恢复列宽设置: salary_data_2025_12_retired_employees
2025-08-14 22:20:09.535 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4206 | 🔧 [分页行号修复] 表格状态: 当前行数=2, 期望行数=2
2025-08-14 22:20:09.535 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 17 个, 行号起始 1, 共 2 行
2025-08-14 22:20:09.536 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_force_update_pagination_row_numbers:4286 | 🔧 [分页行号修复] 强制更新完成: 起始记录1, 共2行
2025-08-14 22:20:09.539 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3958 | 🔍 [FINAL修复] set_pagination_state最终调用完成
2025-08-14 22:20:09.540 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3972 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-08-14 22:20:09.540 | INFO     | src.services.table_data_service:load_table_data:426 | 📨[data_event] published(cache)
2025-08-14 22:20:09.541 | INFO     | src.gui.prototype.prototype_main_window:_refresh_table_data:8612 | 🔁[scope=table] 新架构刷新成功 | request_id=TR-1755181209481-f7b53695
2025-08-14 22:20:09.624 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7957 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: salary_data_2025_12_retired_employees
2025-08-14 22:20:16.161 | INFO     | src.gui.prototype.prototype_main_window:_on_refresh_data:679 | 🔧[scope=global] 刷新数据功能被触发 | request_id=GR-1755181216161-61cd3f62
2025-08-14 22:20:16.161 | INFO     | src.gui.prototype.prototype_main_window:_on_refresh_data:8660 | 🔧 [P1-3] 全局状态刷新被触发，开始综合性系统刷新。
2025-08-14 22:20:16.162 | INFO     | src.gui.prototype.prototype_main_window:_execute_global_state_refresh:8679 | 🔧 [P1-3] 开始执行全局状态刷新流程
2025-08-14 22:20:16.164 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1021 | 🔧 [P1修复] 找到 4 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-14 22:20:16.165 | INFO     | src.gui.prototype.prototype_main_window:_refresh_core_components:8760 | 🔧 [P1-3] 开始刷新核心组件状态
2025-08-14 22:20:16.165 | INFO     | src.gui.table_header_manager:force_comprehensive_cleanup:944 | 🔧 [P1-1] 开始强制全面清理 2 个表格
2025-08-14 22:20:16.166 | INFO     | src.gui.table_header_manager:_remove_deleted_table_reference:561 | 🔧 [P0-1] 已移除已删除表格的引用: table_0_2307686299200
2025-08-14 22:20:16.166 | INFO     | src.gui.table_header_manager:_remove_deleted_table_reference:561 | 🔧 [P0-1] 已移除已删除表格的引用: table_1_2307686825648
2025-08-14 22:20:16.167 | INFO     | src.gui.table_header_manager:force_comprehensive_cleanup:1005 | 🔧 [P1-1] 强制全面清理完成，成功率: 100.0% (2/2)
2025-08-14 22:20:16.167 | INFO     | src.gui.prototype.prototype_main_window:_refresh_core_components:8768 | 🔧 [P1-3] 表头重影清理完成: {'total_tables': 2, 'cleaned_tables': ['table_0_2307686299200(已清理无效引用)', 'table_1_2307686825648(已清理无效引用)'], 'failed_tables': [], 'total_shadows_found': 0, 'total_shadows_fixed': 0}
2025-08-14 22:20:16.168 | INFO     | src.gui.prototype.prototype_main_window:_refresh_core_components:8791 | 🔧 [P1-3] 核心组件状态刷新完成
2025-08-14 22:20:16.168 | INFO     | src.gui.prototype.prototype_main_window:_refresh_data_layer:8805 | 🔧 [P1-3] 开始刷新数据层
2025-08-14 22:20:16.168 | INFO     | src.services.table_data_service:load_table_data:366 | 加载表格数据: 页码: 1
2025-08-14 22:20:16.169 | INFO     | src.core.unified_data_request_manager:request_table_data:212 | 开始处理数据请求
2025-08-14 22:20:16.169 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:590 | 正在从表 salary_data_2025_12_retired_employees 分页获取数据（支持排序）: 第1页, 每页50条, 排序=0列
2025-08-14 22:20:16.171 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:706 | 成功从表 salary_data_2025_12_retired_employees 获取第1页数据（含排序）: 2 行，总计2行
2025-08-14 22:20:16.175 | INFO     | src.core.unified_data_request_manager:request_table_data:262 | 数据请求处理完成: 字段=21, 行数=2, 耗时=6.0ms
2025-08-14 22:20:16.175 | INFO     | src.services.table_data_service:load_table_data:462 | [新架构] 使用统一格式管理器（单例优化）
2025-08-14 22:20:16.176 | INFO     | src.modules.format_management.format_renderer:render_dataframe:107 | 🎯 [格式渲染] 隐藏字段配置: ['id', 'updated_at', 'sequence_number', 'created_at']
2025-08-14 22:20:16.176 | INFO     | src.modules.format_management.format_renderer:render_dataframe:115 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-08-14 22:20:16.180 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 增发一次性生活补贴 -> 英文名: one_time_living_allowance -> 类型: float
2025-08-14 22:20:16.181 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-08-14 22:20:16.182 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-08-14 22:20:16.183 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 备注 -> 英文名: remarks -> 类型: string
2025-08-14 22:20:16.185 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 月份 -> 英文名: month -> 类型: month_string_extract_last_two
2025-08-14 22:20:16.187 | INFO     | src.modules.format_management.field_registry:get_display_fields:1552 | 🔧 [P0-2修复] display_order转换完成: 17个字段，原始字段数: 17
2025-08-14 22:20:16.187 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | 🔧 [DEBUG] table_type=retired_employees, display_fields=17个字段
2025-08-14 22:20:16.188 | INFO     | src.modules.format_management.format_renderer:render_dataframe:211 | 🔧 [P0修复] 配置字段匹配完成: 成功匹配 17/17 个字段
2025-08-14 22:20:16.188 | INFO     | src.modules.format_management.format_renderer:render_dataframe:219 | 🔧 [P0-关键修复] 保留未配置的业务字段: 4个
2025-08-14 22:20:16.189 | INFO     | src.modules.format_management.format_renderer:render_dataframe:224 | 🔧 [P0修复] 字段重排完成: 原始21个 -> 最终21个字段
2025-08-14 22:20:16.190 | INFO     | src.modules.format_management.format_renderer:render_dataframe:240 | 🎨 [格式渲染] DataFrame格式化完成: retired_employees, 行数: 2, 列数: 21
2025-08-14 22:20:16.190 | INFO     | src.modules.format_management.unified_format_manager:format_data:367 | 🎯 [统一格式管理] 数据格式化完成: retired_employees, 行数: 2, 列数: 21
2025-08-14 22:20:16.192 | INFO     | src.core.unified_state_manager:update_table_state:233 | 表状态已更新: salary_data_2025_12_retired_employees, 变更类型: None
2025-08-14 22:20:16.192 | INFO     | src.services.table_data_service:load_table_data:512 | [修复数据发布] 数据加载成功，发布更新事件: 2行
2025-08-14 22:20:16.193 | INFO     | src.services.table_data_service:load_table_data:516 | 📨[data_event] publish | table=salary_data_2025_12_retired_employees | rows=2 | page=1 | size=50 | total=2 | req_type=RequestType.INITIAL_LOAD | request_id=SV-1755181216193-af8d9fb2
2025-08-14 22:20:16.193 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3785 | 📥[data_event] received | table=salary_data_2025_12_retired_employees | request_id=SV-1755181216193-af8d9fb2
2025-08-14 22:20:16.194 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3817 | 数据内容: 2行 x 21列
2025-08-14 22:20:16.198 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3843 | 🔧 [数据更新] 接收到initial_load操作的数据更新事件: salary_data_2025_12_retired_employees, 2行
2025-08-14 22:20:16.200 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7256 | 表 salary_data_2025_12_retired_employees 没有用户偏好设置，显示所有可见字段
2025-08-14 22:20:16.201 | INFO     | src.gui.prototype.prototype_main_window:set_data:873 | 通过公共接口设置新的表格数据(DataFrame): 2 行
2025-08-14 22:20:16.203 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7256 | 表 salary_data_2025_12_retired_employees 没有用户偏好设置，显示所有可见字段
2025-08-14 22:20:16.204 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 2行，表名: salary_data_2025_12_retired_employees
2025-08-14 22:20:16.205 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=19289006, 薪资=N/A
2025-08-14 22:20:16.205 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=19339009, 薪资=N/A
2025-08-14 22:20:16.206 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:5271 | 最大可见行数已更新: 10 -> 10
2025-08-14 22:20:16.208 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5324 | 根据数据量(2)自动调整最大可见行数为: 10
2025-08-14 22:20:16.211 | INFO     | src.modules.format_management.format_renderer:render_dataframe:107 | 🎯 [格式渲染] 隐藏字段配置: ['id', 'updated_at', 'sequence_number', 'created_at']
2025-08-14 22:20:16.211 | INFO     | src.modules.format_management.format_renderer:render_dataframe:115 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-08-14 22:20:16.213 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 增发一次性生活补贴 -> 英文名: one_time_living_allowance -> 类型: float
2025-08-14 22:20:16.214 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-08-14 22:20:16.215 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-08-14 22:20:16.215 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 备注 -> 英文名: remarks -> 类型: string
2025-08-14 22:20:16.216 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 月份 -> 英文名: month -> 类型: month_string_extract_last_two
2025-08-14 22:20:16.216 | INFO     | src.modules.format_management.field_registry:get_display_fields:1552 | 🔧 [P0-2修复] display_order转换完成: 17个字段，原始字段数: 17
2025-08-14 22:20:16.219 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | 🔧 [DEBUG] table_type=retired_employees, display_fields=17个字段
2025-08-14 22:20:16.219 | INFO     | src.modules.format_management.format_renderer:render_dataframe:211 | 🔧 [P0修复] 配置字段匹配完成: 成功匹配 17/17 个字段
2025-08-14 22:20:16.220 | INFO     | src.modules.format_management.format_renderer:render_dataframe:224 | 🔧 [P0修复] 字段重排完成: 原始17个 -> 最终17个字段
2025-08-14 22:20:16.220 | INFO     | src.modules.format_management.format_renderer:render_dataframe:240 | 🎨 [格式渲染] DataFrame格式化完成: retired_employees, 行数: 2, 列数: 17
2025-08-14 22:20:16.220 | INFO     | src.modules.format_management.unified_format_manager:format_data:367 | 🎯 [统一格式管理] 数据格式化完成: retired_employees, 行数: 2, 列数: 17
2025-08-14 22:20:16.222 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 2行数据
2025-08-14 22:20:16.222 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:52 | [数据流追踪] 开始小数据集渲染: 2行 x 17列
2025-08-14 22:20:16.223 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:112 | [数据流追踪] 小数据集渲染完成: 耗时1.0ms, 平均每行0.50ms
2025-08-14 22:20:16.223 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=2, 渲染时间=1.0ms, 策略=small_dataset
2025-08-14 22:20:16.224 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:279 | [数据流追踪] 选择小数据集渲染策略
2025-08-14 22:20:16.224 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19289006
2025-08-14 22:20:16.224 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 19339009
2025-08-14 22:20:16.225 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 2
2025-08-14 22:20:16.225 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[0]: 工号=19289006, 薪资=N/A
2025-08-14 22:20:16.225 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[1]: 工号=19339009, 薪资=N/A
2025-08-14 22:20:16.226 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:479 | 可见行数据顺序: ['19289006', '19339009']
2025-08-14 22:20:16.226 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:442 | 表格数据已设置: 2 行, 17 列
2025-08-14 22:20:16.227 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 2 行, 耗时: 23.0ms
2025-08-14 22:20:16.230 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7922 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-14 22:20:16.230 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7935 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-14 22:20:16.231 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1723 | 🆕 [列宽保存] 未找到表格 salary_data_2025_12_retired_employees 的列宽配置
2025-08-14 22:20:16.231 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-14 22:20:16.231 | INFO     | src.gui.prototype.prototype_main_window:set_data:997 | 📊[pagination-write] 跳过写入当前页数据量(total=2)，保持 total=2，等待数据事件纠正
2025-08-14 22:20:16.232 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3887 | 🔧 [P1-1修复] 数据已成功设置到UI，使用新映射表头: 2行, 17列
2025-08-14 22:20:16.232 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 17 个, 行号起始 1, 共 2 行
2025-08-14 22:20:16.232 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3903 | 🔧 [P0-新5修复] 表头强制刷新完成: salary_data_2025_12_retired_employees, 传递参数: 17个表头
2025-08-14 22:20:16.233 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3907 | 🔧 [统一数据设置] 数据已成功设置到UI: 2行, 17列
2025-08-14 22:20:16.233 | INFO     | src.gui.widgets.pagination_widget:batch_update:472 | 📊[pagination-write] batch_update | src=_on_new_data_updated/initial_load | rid=SV-1755181216193-af8d9fb2 | total: 2->2, page: 1->1, size: 50->50, pages: 1->1
2025-08-14 22:20:16.233 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3941 | ✅[pagination-state] now | page=1 / pages=1 / total=2 | rid=SV-1755181216193-af8d9fb2
2025-08-14 22:20:16.234 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3954 | 🔍 [FINAL修复] 最终调用set_pagination_state: 第1页, 记录1-2 | rid=SV-1755181216193-af8d9fb2
2025-08-14 22:20:16.234 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4166 | 🔍 [表格调试] ================== set_pagination_state 开始 ==================
2025-08-14 22:20:16.234 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4167 | 🔍 [表格调试] 收到分页状态参数: {'current_page': 1, 'page_size': 50, 'total_records': 2, 'start_record': 1, 'end_record': 2}
2025-08-14 22:20:16.235 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4168 | 🔍 [表格调试] 当前表格行数: 2
2025-08-14 22:20:16.235 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4169 | 🔍 [表格调试] 当前表格列数: 17
2025-08-14 22:20:16.236 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4171 | 🔧 [分页行号修复] 接收分页状态: 第1页, 记录1-2
2025-08-14 22:20:16.236 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1723 | 🆕 [列宽保存] 未找到表格 salary_data_2025_12_retired_employees 的列宽配置
2025-08-14 22:20:16.236 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4198 | 🔧 [P0-3修复] 分页时已恢复列宽设置: salary_data_2025_12_retired_employees
2025-08-14 22:20:16.237 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4206 | 🔧 [分页行号修复] 表格状态: 当前行数=2, 期望行数=2
2025-08-14 22:20:16.237 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 17 个, 行号起始 1, 共 2 行
2025-08-14 22:20:16.237 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_force_update_pagination_row_numbers:4286 | 🔧 [分页行号修复] 强制更新完成: 起始记录1, 共2行
2025-08-14 22:20:16.238 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3958 | 🔍 [FINAL修复] set_pagination_state最终调用完成
2025-08-14 22:20:16.241 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3972 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-08-14 22:20:16.241 | INFO     | src.services.table_data_service:load_table_data:539 | 📨[data_event] published
2025-08-14 22:20:16.242 | INFO     | src.gui.prototype.prototype_main_window:_refresh_data_layer:8814 | 🔧 [P1-3] 表格数据刷新成功: salary_data_2025_12_retired_employees
2025-08-14 22:20:16.242 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1984 | 开始强制刷新工资数据导航... (尝试 1/3)
2025-08-14 22:20:16.242 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1992 | 找到工资表节点: 工资表
2025-08-14 22:20:16.243 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 12月 > A岗职工', '工资表 > 2025年 > 12月 > 全部在职人员', '工资表 > 2025年', '工资表 > 2025年 > 12月', '工资表 > 2025年 > 05月 > 全部在职人员']
2025-08-14 22:20:16.244 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7299 | 导航变化: 工资表 > 2025年 > 12月 > 离休人员
2025-08-14 22:20:16.244 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10384 | 🚫 [用户要求] 表格切换不显示加载条: salary_data_2025_12_retired_employees -> None
2025-08-14 22:20:16.245 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10400 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-08-14 22:20:16.245 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8205 | 🔧 [表名生成] 从完整路径生成表名: ['工资表', '2025年', '12月', '离休人员'] -> salary_data_2025_12_retired_employees
2025-08-14 22:20:16.246 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-14 22:20:16.246 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:362 | 已清理表 salary_data_2025_12_retired_employees 的缓存
2025-08-14 22:20:16.247 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:11070 | 已注册 2 个表格到表头管理器
2025-08-14 22:20:16.247 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1188 | 🔧 [P1-3] 开始智能表头重影检测，共 2 个表格
2025-08-14 22:20:16.248 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1261 | 🔧 [P1-3] 智能表头重影检测完成，耗时 1.00ms
2025-08-14 22:20:16.248 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1262 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-08-14 22:20:16.248 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7515 | 🆕 使用新架构加载数据: salary_data_2025_12_retired_employees（通过事件系统）
2025-08-14 22:20:16.249 | INFO     | src.services.table_data_service:load_table_data:390 | [缓存命中] 使用缓存数据: 第1页
2025-08-14 22:20:16.252 | INFO     | src.services.table_data_service:load_table_data:401 | 📨[data_event] publish(cache) | table=salary_data_2025_12_retired_employees | rows=2 | page=1 | size=50 | total=2 | request_id=SV-1755181216252-5bd64809-C
2025-08-14 22:20:16.252 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3785 | 📥[data_event] received | table=salary_data_2025_12_retired_employees | request_id=SV-1755181216252-5bd64809-C
2025-08-14 22:20:16.253 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3817 | 数据内容: 2行 x 21列
2025-08-14 22:20:16.253 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3843 | 🔧 [数据更新] 接收到cached_load操作的数据更新事件: salary_data_2025_12_retired_employees, 2行
2025-08-14 22:20:16.254 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7256 | 表 salary_data_2025_12_retired_employees 没有用户偏好设置，显示所有可见字段
2025-08-14 22:20:16.255 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3887 | 🔧 [P1-1修复] 数据已成功设置到UI，使用新映射表头: 2行, 17列
2025-08-14 22:20:16.255 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 17 个, 行号起始 1, 共 2 行
2025-08-14 22:20:16.256 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3903 | 🔧 [P0-新5修复] 表头强制刷新完成: salary_data_2025_12_retired_employees, 传递参数: 17个表头
2025-08-14 22:20:16.256 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3907 | 🔧 [统一数据设置] 数据已成功设置到UI: 2行, 17列
2025-08-14 22:20:16.256 | INFO     | src.gui.widgets.pagination_widget:batch_update:472 | 📊[pagination-write] batch_update | src=_on_new_data_updated/cached_load | rid=SV-1755181216252-5bd64809-C | total: 0->2, page: 1->1, size: 50->50, pages: 1->1
2025-08-14 22:20:16.257 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3941 | ✅[pagination-state] now | page=1 / pages=1 / total=2 | rid=SV-1755181216252-5bd64809-C
2025-08-14 22:20:16.257 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3954 | 🔍 [FINAL修复] 最终调用set_pagination_state: 第1页, 记录1-2 | rid=SV-1755181216252-5bd64809-C
2025-08-14 22:20:16.257 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4166 | 🔍 [表格调试] ================== set_pagination_state 开始 ==================
2025-08-14 22:20:16.258 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4167 | 🔍 [表格调试] 收到分页状态参数: {'current_page': 1, 'page_size': 50, 'total_records': 2, 'start_record': 1, 'end_record': 2}
2025-08-14 22:20:16.258 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4168 | 🔍 [表格调试] 当前表格行数: 2
2025-08-14 22:20:16.258 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4169 | 🔍 [表格调试] 当前表格列数: 17
2025-08-14 22:20:16.259 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4171 | 🔧 [分页行号修复] 接收分页状态: 第1页, 记录1-2
2025-08-14 22:20:16.259 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1723 | 🆕 [列宽保存] 未找到表格 salary_data_2025_12_retired_employees 的列宽配置
2025-08-14 22:20:16.260 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4198 | 🔧 [P0-3修复] 分页时已恢复列宽设置: salary_data_2025_12_retired_employees
2025-08-14 22:20:16.263 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4206 | 🔧 [分页行号修复] 表格状态: 当前行数=2, 期望行数=2
2025-08-14 22:20:16.263 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 17 个, 行号起始 1, 共 2 行
2025-08-14 22:20:16.263 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_force_update_pagination_row_numbers:4286 | 🔧 [分页行号修复] 强制更新完成: 起始记录1, 共2行
2025-08-14 22:20:16.264 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3958 | 🔍 [FINAL修复] set_pagination_state最终调用完成
2025-08-14 22:20:16.285 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:379 | 已清理所有缓存
2025-08-14 22:20:16.285 | INFO     | src.gui.prototype.prototype_main_window:_validate_component_state_consistency:10878 | ✅ [新架构] 组件状态一致性验证通过
2025-08-14 22:20:16.286 | INFO     | src.gui.prototype.prototype_main_window:complete_navigation_transition:10841 | 🆕 [新架构] 导航迁移完成
2025-08-14 22:20:16.287 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3972 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-08-14 22:20:16.287 | INFO     | src.services.table_data_service:load_table_data:426 | 📨[data_event] published(cache)
2025-08-14 22:20:16.287 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7520 | 🆕 新架构数据加载请求已发送，等待事件系统处理
2025-08-14 22:20:16.289 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1021 | 🔧 [P1修复] 找到 4 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-14 22:20:16.290 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:2081 | 创建年份节点: 2025年，包含 1 个月份
2025-08-14 22:20:16.292 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_navigation_state:1893 | 🔧 [P2-2] 开始恢复导航状态...
2025-08-14 22:20:16.296 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 12月 > A岗职工', '工资表 > 2025年 > 12月 > 全部在职人员', '工资表 > 2025年', '工资表 > 2025年 > 12月', '工资表 > 2025年 > 12月 > 离休人员']
2025-08-14 22:20:16.297 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_navigation_state:1924 | 🔧 [P2-2] 导航状态恢复完成: 6个展开项
2025-08-14 22:20:16.298 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:2221 | force_refresh_salary_data 执行完成
2025-08-14 22:20:16.298 | INFO     | src.gui.prototype.prototype_main_window:_refresh_data_layer:8828 | 🔧 [P1-3] 导航面板数据已刷新
2025-08-14 22:20:16.298 | INFO     | src.gui.prototype.prototype_main_window:_refresh_data_layer:8839 | 🔧 [P1-3] 数据层刷新完成
2025-08-14 22:20:16.299 | INFO     | src.gui.prototype.prototype_main_window:_refresh_ui_components:8853 | 🔧 [P1-3] 开始刷新UI组件状态
2025-08-14 22:20:16.299 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:refresh_navigation_state:1207 | 🔧 [P2-2] 开始刷新导航面板状态
2025-08-14 22:20:16.299 | INFO     | src.gui.prototype.widgets.smart_search_debounce:reset_statistics:657 | 统计信息已重置
2025-08-14 22:20:16.300 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:refresh_navigation_state:1241 | 🔧 [P2-2] 导航面板状态刷新完成
2025-08-14 22:20:16.300 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:sync_with_global_state:1254 | 🔧 [P2-2] 开始与全局状态同步
2025-08-14 22:20:16.300 | INFO     | src.gui.widgets.pagination_widget:refresh_pagination_state:594 | 🔧 [P2-1] 开始刷新分页组件状态
2025-08-14 22:20:16.301 | INFO     | src.gui.prototype.prototype_main_window:_refresh_ui_components:8919 | 🔧 [P1-3] UI组件状态刷新完成
2025-08-14 22:20:16.301 | INFO     | src.gui.prototype.prototype_main_window:_refresh_display_state:8933 | 🔧 [P1-3] 开始刷新显示状态
2025-08-14 22:20:16.372 | INFO     | src.gui.prototype.prototype_main_window:_force_global_redraw:9137 | 🔧 [P2-重影修复] 执行表头重影清理...
2025-08-14 22:20:16.373 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1984 | 开始强制刷新工资数据导航... (尝试 1/3)
2025-08-14 22:20:16.374 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1992 | 找到工资表节点: 工资表
2025-08-14 22:20:16.375 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 12月 > A岗职工', '工资表 > 2025年 > 12月 > 全部在职人员', '工资表 > 2025年', '工资表 > 2025年 > 12月', '工资表 > 2025年 > 12月 > 离休人员']
2025-08-14 22:20:16.376 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:1025 | 导航选择: 工资表 > 2025年 > 12月 > 离休人员
2025-08-14 22:20:16.377 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1021 | 🔧 [P1修复] 找到 4 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-14 22:20:16.380 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_navigation_state:1893 | 🔧 [P2-2] 开始恢复导航状态...
2025-08-14 22:20:16.390 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 12月 > A岗职工', '工资表 > 2025年 > 12月 > 全部在职人员', '工资表 > 2025年', '工资表 > 2025年 > 12月', '工资表 > 2025年 > 12月 > 离休人员']
2025-08-14 22:20:16.390 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_navigation_state:1924 | 🔧 [P2-2] 导航状态恢复完成: 7个展开项
2025-08-14 22:20:16.391 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:2221 | force_refresh_salary_data 执行完成
2025-08-14 22:20:16.391 | INFO     | src.gui.prototype.prototype_main_window:_force_global_redraw:9152 | 🔧 [组件检查] 导航面板数据已刷新
2025-08-14 22:20:16.392 | INFO     | src.gui.prototype.prototype_main_window:_force_global_redraw:9157 | 🔧 [P2-重影修复] 使用新架构刷新表格数据: salary_data_2025_12_retired_employees
2025-08-14 22:20:16.392 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:590 | 正在从表 salary_data_2025_12_retired_employees 分页获取数据（支持排序）: 第1页, 每页50条, 排序=0列
2025-08-14 22:20:16.395 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:706 | 成功从表 salary_data_2025_12_retired_employees 获取第1页数据（含排序）: 2 行，总计2行
2025-08-14 22:20:16.396 | INFO     | src.modules.format_management.format_renderer:render_dataframe:107 | 🎯 [格式渲染] 隐藏字段配置: ['id', 'updated_at', 'sequence_number', 'created_at']
2025-08-14 22:20:16.396 | INFO     | src.modules.format_management.format_renderer:render_dataframe:115 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-08-14 22:20:16.400 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 增发一次性生活补贴 -> 英文名: one_time_living_allowance -> 类型: float
2025-08-14 22:20:16.401 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-08-14 22:20:16.402 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-08-14 22:20:16.402 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 备注 -> 英文名: remarks -> 类型: string
2025-08-14 22:20:16.405 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 月份 -> 英文名: month -> 类型: month_string_extract_last_two
2025-08-14 22:20:16.406 | INFO     | src.modules.format_management.field_registry:get_display_fields:1552 | 🔧 [P0-2修复] display_order转换完成: 17个字段，原始字段数: 17
2025-08-14 22:20:16.406 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | 🔧 [DEBUG] table_type=retired_employees, display_fields=17个字段
2025-08-14 22:20:16.407 | INFO     | src.modules.format_management.format_renderer:render_dataframe:211 | 🔧 [P0修复] 配置字段匹配完成: 成功匹配 17/17 个字段
2025-08-14 22:20:16.407 | INFO     | src.modules.format_management.format_renderer:render_dataframe:219 | 🔧 [P0-关键修复] 保留未配置的业务字段: 4个
2025-08-14 22:20:16.408 | INFO     | src.modules.format_management.format_renderer:render_dataframe:224 | 🔧 [P0修复] 字段重排完成: 原始21个 -> 最终21个字段
2025-08-14 22:20:16.409 | INFO     | src.modules.format_management.format_renderer:render_dataframe:240 | 🎨 [格式渲染] DataFrame格式化完成: retired_employees, 行数: 2, 列数: 21
2025-08-14 22:20:16.409 | INFO     | src.modules.format_management.unified_format_manager:format_data:367 | 🎯 [统一格式管理] 数据格式化完成: retired_employees, 行数: 2, 列数: 21
2025-08-14 22:20:16.410 | INFO     | src.services.table_data_service:load_table_data:516 | 📨[data_event] publish | table=salary_data_2025_12_retired_employees | rows=2 | page=1 | size=50 | total=2 | req_type=RequestType.INITIAL_LOAD | request_id=SV-1755181216410-a1369dea
2025-08-14 22:20:16.411 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3785 | 📥[data_event] received | table=salary_data_2025_12_retired_employees | request_id=SV-1755181216410-a1369dea
2025-08-14 22:20:16.411 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3817 | 数据内容: 2行 x 21列
2025-08-14 22:20:16.411 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3843 | 🔧 [数据更新] 接收到initial_load操作的数据更新事件: salary_data_2025_12_retired_employees, 2行
2025-08-14 22:20:16.413 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7256 | 表 salary_data_2025_12_retired_employees 没有用户偏好设置，显示所有可见字段
2025-08-14 22:20:16.417 | INFO     | src.gui.prototype.prototype_main_window:set_data:873 | 通过公共接口设置新的表格数据(DataFrame): 2 行
2025-08-14 22:20:16.418 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7256 | 表 salary_data_2025_12_retired_employees 没有用户偏好设置，显示所有可见字段
2025-08-14 22:20:16.419 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 2行，表名: salary_data_2025_12_retired_employees
2025-08-14 22:20:16.420 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=19289006, 薪资=N/A
2025-08-14 22:20:16.421 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=19339009, 薪资=N/A
2025-08-14 22:20:16.421 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:5271 | 最大可见行数已更新: 10 -> 10
2025-08-14 22:20:16.422 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5324 | 根据数据量(2)自动调整最大可见行数为: 10
2025-08-14 22:20:16.424 | INFO     | src.modules.format_management.format_renderer:render_dataframe:107 | 🎯 [格式渲染] 隐藏字段配置: ['id', 'updated_at', 'sequence_number', 'created_at']
2025-08-14 22:20:16.427 | INFO     | src.modules.format_management.format_renderer:render_dataframe:115 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-08-14 22:20:16.429 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 增发一次性生活补贴 -> 英文名: one_time_living_allowance -> 类型: float
2025-08-14 22:20:16.430 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-08-14 22:20:16.431 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-08-14 22:20:16.431 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 备注 -> 英文名: remarks -> 类型: string
2025-08-14 22:20:16.432 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 月份 -> 英文名: month -> 类型: month_string_extract_last_two
2025-08-14 22:20:16.433 | INFO     | src.modules.format_management.field_registry:get_display_fields:1552 | 🔧 [P0-2修复] display_order转换完成: 17个字段，原始字段数: 17
2025-08-14 22:20:16.433 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | 🔧 [DEBUG] table_type=retired_employees, display_fields=17个字段
2025-08-14 22:20:16.433 | INFO     | src.modules.format_management.format_renderer:render_dataframe:211 | 🔧 [P0修复] 配置字段匹配完成: 成功匹配 17/17 个字段
2025-08-14 22:20:16.434 | INFO     | src.modules.format_management.format_renderer:render_dataframe:224 | 🔧 [P0修复] 字段重排完成: 原始17个 -> 最终17个字段
2025-08-14 22:20:16.434 | INFO     | src.modules.format_management.format_renderer:render_dataframe:240 | 🎨 [格式渲染] DataFrame格式化完成: retired_employees, 行数: 2, 列数: 17
2025-08-14 22:20:16.435 | INFO     | src.modules.format_management.unified_format_manager:format_data:367 | 🎯 [统一格式管理] 数据格式化完成: retired_employees, 行数: 2, 列数: 17
2025-08-14 22:20:16.437 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 2行数据
2025-08-14 22:20:16.438 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:52 | [数据流追踪] 开始小数据集渲染: 2行 x 17列
2025-08-14 22:20:16.439 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:112 | [数据流追踪] 小数据集渲染完成: 耗时1.0ms, 平均每行0.50ms
2025-08-14 22:20:16.439 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=2, 渲染时间=1.0ms, 策略=small_dataset
2025-08-14 22:20:16.440 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:279 | [数据流追踪] 选择小数据集渲染策略
2025-08-14 22:20:16.440 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19289006
2025-08-14 22:20:16.440 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 19339009
2025-08-14 22:20:16.441 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 2
2025-08-14 22:20:16.441 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[0]: 工号=19289006, 薪资=N/A
2025-08-14 22:20:16.441 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[1]: 工号=19339009, 薪资=N/A
2025-08-14 22:20:16.442 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:479 | 可见行数据顺序: ['19289006', '19339009']
2025-08-14 22:20:16.442 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 2 行, 耗时: 22.0ms
2025-08-14 22:20:16.443 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7922 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-14 22:20:16.443 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7935 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-14 22:20:16.444 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1723 | 🆕 [列宽保存] 未找到表格 salary_data_2025_12_retired_employees 的列宽配置
2025-08-14 22:20:16.444 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-14 22:20:16.444 | INFO     | src.gui.prototype.prototype_main_window:set_data:997 | 📊[pagination-write] 跳过写入当前页数据量(total=2)，保持 total=2，等待数据事件纠正
2025-08-14 22:20:16.445 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3887 | 🔧 [P1-1修复] 数据已成功设置到UI，使用新映射表头: 2行, 17列
2025-08-14 22:20:16.445 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 17 个, 行号起始 1, 共 2 行
2025-08-14 22:20:16.445 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3903 | 🔧 [P0-新5修复] 表头强制刷新完成: salary_data_2025_12_retired_employees, 传递参数: 17个表头
2025-08-14 22:20:16.446 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3907 | 🔧 [统一数据设置] 数据已成功设置到UI: 2行, 17列
2025-08-14 22:20:16.446 | INFO     | src.gui.widgets.pagination_widget:batch_update:472 | 📊[pagination-write] batch_update | src=_on_new_data_updated/initial_load | rid=SV-1755181216410-a1369dea | total: 2->2, page: 1->1, size: 50->50, pages: 1->1
2025-08-14 22:20:16.447 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3941 | ✅[pagination-state] now | page=1 / pages=1 / total=2 | rid=SV-1755181216410-a1369dea
2025-08-14 22:20:16.450 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3954 | 🔍 [FINAL修复] 最终调用set_pagination_state: 第1页, 记录1-2 | rid=SV-1755181216410-a1369dea
2025-08-14 22:20:16.450 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4166 | 🔍 [表格调试] ================== set_pagination_state 开始 ==================
2025-08-14 22:20:16.451 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4167 | 🔍 [表格调试] 收到分页状态参数: {'current_page': 1, 'page_size': 50, 'total_records': 2, 'start_record': 1, 'end_record': 2}
2025-08-14 22:20:16.451 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4168 | 🔍 [表格调试] 当前表格行数: 2
2025-08-14 22:20:16.451 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4169 | 🔍 [表格调试] 当前表格列数: 17
2025-08-14 22:20:16.452 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4171 | 🔧 [分页行号修复] 接收分页状态: 第1页, 记录1-2
2025-08-14 22:20:16.452 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1723 | 🆕 [列宽保存] 未找到表格 salary_data_2025_12_retired_employees 的列宽配置
2025-08-14 22:20:16.453 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4198 | 🔧 [P0-3修复] 分页时已恢复列宽设置: salary_data_2025_12_retired_employees
2025-08-14 22:20:16.453 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4206 | 🔧 [分页行号修复] 表格状态: 当前行数=2, 期望行数=2
2025-08-14 22:20:16.453 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 17 个, 行号起始 1, 共 2 行
2025-08-14 22:20:16.454 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_force_update_pagination_row_numbers:4286 | 🔧 [分页行号修复] 强制更新完成: 起始记录1, 共2行
2025-08-14 22:20:16.454 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3958 | 🔍 [FINAL修复] set_pagination_state最终调用完成
2025-08-14 22:20:16.454 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3972 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-08-14 22:20:16.455 | INFO     | src.services.table_data_service:load_table_data:539 | 📨[data_event] published
2025-08-14 22:20:16.455 | INFO     | src.gui.prototype.prototype_main_window:_force_global_redraw:9162 | 🔧 [P2-重影修复] 数据刷新成功
2025-08-14 22:20:16.456 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9205 | 🔧 [P1-2] 开始智能显示亮度修复（增强防护）
2025-08-14 22:20:16.456 | INFO     | src.gui.prototype.prototype_main_window:_refresh_display_state:8958 | 🔧 [P1-3] 显示状态刷新完成
2025-08-14 22:20:16.456 | INFO     | src.gui.prototype.prototype_main_window:_verify_refresh_results:8972 | 🔧 [P1-3] 开始验证刷新结果
2025-08-14 22:20:16.457 | INFO     | src.gui.prototype.prototype_main_window:_verify_refresh_results:8999 | 🔧 [P1-3] 全局状态刷新成功: {'success': True, 'elapsed_time': 0.29416918754577637, 'total_stages': 10, 'expected_stages': 5, 'completed_stages': 5, 'error_count': 0, 'errors': []}
2025-08-14 22:20:16.458 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7957 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: salary_data_2025_12_retired_employees
2025-08-14 22:20:16.544 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7957 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: salary_data_2025_12_retired_employees
2025-08-14 22:20:18.497 | INFO     | src.gui.prototype.prototype_main_window:_refresh_table_data:8583 | 🔁[scope=table] 表格刷新被触发 | table=salary_data_2025_12_retired_employees | page=1 | size=50 | total_before=2 | request_id=TR-1755181218497-bb50c359
2025-08-14 22:20:18.498 | INFO     | src.gui.prototype.prototype_main_window:_refresh_table_data:8594 | 🔁[scope=table] 使用新架构刷新表格数据: salary_data_2025_12_retired_employees
2025-08-14 22:20:18.499 | INFO     | src.services.table_data_service:load_table_data:366 | 加载表格数据: 页码: 1
2025-08-14 22:20:18.499 | INFO     | src.services.table_data_service:load_table_data:390 | [缓存命中] 使用缓存数据: 第1页
2025-08-14 22:20:18.500 | INFO     | src.services.table_data_service:load_table_data:401 | 📨[data_event] publish(cache) | table=salary_data_2025_12_retired_employees | rows=2 | page=1 | size=50 | total=2 | request_id=SV-1755181218500-1791ae75-C
2025-08-14 22:20:18.500 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3785 | 📥[data_event] received | table=salary_data_2025_12_retired_employees | request_id=SV-1755181218500-1791ae75-C
2025-08-14 22:20:18.501 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3817 | 数据内容: 2行 x 21列
2025-08-14 22:20:18.501 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3843 | 🔧 [数据更新] 接收到cached_load操作的数据更新事件: salary_data_2025_12_retired_employees, 2行
2025-08-14 22:20:18.504 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7256 | 表 salary_data_2025_12_retired_employees 没有用户偏好设置，显示所有可见字段
2025-08-14 22:20:18.504 | INFO     | src.gui.prototype.prototype_main_window:set_data:873 | 通过公共接口设置新的表格数据(DataFrame): 2 行
2025-08-14 22:20:18.506 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7256 | 表 salary_data_2025_12_retired_employees 没有用户偏好设置，显示所有可见字段
2025-08-14 22:20:18.507 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 2行，表名: salary_data_2025_12_retired_employees
2025-08-14 22:20:18.510 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=19289006, 薪资=N/A
2025-08-14 22:20:18.511 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=19339009, 薪资=N/A
2025-08-14 22:20:18.511 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:5271 | 最大可见行数已更新: 10 -> 10
2025-08-14 22:20:18.511 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5324 | 根据数据量(2)自动调整最大可见行数为: 10
2025-08-14 22:20:18.514 | INFO     | src.modules.format_management.format_renderer:render_dataframe:107 | 🎯 [格式渲染] 隐藏字段配置: ['id', 'updated_at', 'sequence_number', 'created_at']
2025-08-14 22:20:18.515 | INFO     | src.modules.format_management.format_renderer:render_dataframe:115 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-08-14 22:20:18.518 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 增发一次性生活补贴 -> 英文名: one_time_living_allowance -> 类型: float
2025-08-14 22:20:18.519 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-08-14 22:20:18.522 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-08-14 22:20:18.523 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 备注 -> 英文名: remarks -> 类型: string
2025-08-14 22:20:18.524 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 月份 -> 英文名: month -> 类型: month_string_extract_last_two
2025-08-14 22:20:18.524 | INFO     | src.modules.format_management.field_registry:get_display_fields:1552 | 🔧 [P0-2修复] display_order转换完成: 17个字段，原始字段数: 17
2025-08-14 22:20:18.525 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | 🔧 [DEBUG] table_type=retired_employees, display_fields=17个字段
2025-08-14 22:20:18.525 | INFO     | src.modules.format_management.format_renderer:render_dataframe:211 | 🔧 [P0修复] 配置字段匹配完成: 成功匹配 17/17 个字段
2025-08-14 22:20:18.526 | INFO     | src.modules.format_management.format_renderer:render_dataframe:224 | 🔧 [P0修复] 字段重排完成: 原始17个 -> 最终17个字段
2025-08-14 22:20:18.527 | INFO     | src.modules.format_management.format_renderer:render_dataframe:240 | 🎨 [格式渲染] DataFrame格式化完成: retired_employees, 行数: 2, 列数: 17
2025-08-14 22:20:18.527 | INFO     | src.modules.format_management.unified_format_manager:format_data:367 | 🎯 [统一格式管理] 数据格式化完成: retired_employees, 行数: 2, 列数: 17
2025-08-14 22:20:18.529 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 2行数据
2025-08-14 22:20:18.533 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:52 | [数据流追踪] 开始小数据集渲染: 2行 x 17列
2025-08-14 22:20:18.533 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:112 | [数据流追踪] 小数据集渲染完成: 耗时0.0ms, 平均每行0.00ms
2025-08-14 22:20:18.534 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=2, 渲染时间=0.0ms, 策略=small_dataset
2025-08-14 22:20:18.534 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:279 | [数据流追踪] 选择小数据集渲染策略
2025-08-14 22:20:18.534 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19289006
2025-08-14 22:20:18.535 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 19339009
2025-08-14 22:20:18.535 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 2
2025-08-14 22:20:18.536 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[0]: 工号=19289006, 薪资=N/A
2025-08-14 22:20:18.536 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[1]: 工号=19339009, 薪资=N/A
2025-08-14 22:20:18.537 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:479 | 可见行数据顺序: ['19289006', '19339009']
2025-08-14 22:20:18.537 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:442 | 表格数据已设置: 2 行, 17 列
2025-08-14 22:20:18.538 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 2 行, 耗时: 28.1ms
2025-08-14 22:20:18.538 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7922 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-14 22:20:18.539 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7935 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-14 22:20:18.539 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1723 | 🆕 [列宽保存] 未找到表格 salary_data_2025_12_retired_employees 的列宽配置
2025-08-14 22:20:18.540 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-14 22:20:18.540 | INFO     | src.gui.prototype.prototype_main_window:set_data:997 | 📊[pagination-write] 跳过写入当前页数据量(total=2)，保持 total=2，等待数据事件纠正
2025-08-14 22:20:18.545 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3887 | 🔧 [P1-1修复] 数据已成功设置到UI，使用新映射表头: 2行, 17列
2025-08-14 22:20:18.545 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 17 个, 行号起始 1, 共 2 行
2025-08-14 22:20:18.546 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3903 | 🔧 [P0-新5修复] 表头强制刷新完成: salary_data_2025_12_retired_employees, 传递参数: 17个表头
2025-08-14 22:20:18.546 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3907 | 🔧 [统一数据设置] 数据已成功设置到UI: 2行, 17列
2025-08-14 22:20:18.547 | INFO     | src.gui.widgets.pagination_widget:batch_update:472 | 📊[pagination-write] batch_update | src=_on_new_data_updated/cached_load | rid=SV-1755181218500-1791ae75-C | total: 2->2, page: 1->1, size: 50->50, pages: 1->1
2025-08-14 22:20:18.547 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3941 | ✅[pagination-state] now | page=1 / pages=1 / total=2 | rid=SV-1755181218500-1791ae75-C
2025-08-14 22:20:18.548 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3954 | 🔍 [FINAL修复] 最终调用set_pagination_state: 第1页, 记录1-2 | rid=SV-1755181218500-1791ae75-C
2025-08-14 22:20:18.548 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4166 | 🔍 [表格调试] ================== set_pagination_state 开始 ==================
2025-08-14 22:20:18.549 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4167 | 🔍 [表格调试] 收到分页状态参数: {'current_page': 1, 'page_size': 50, 'total_records': 2, 'start_record': 1, 'end_record': 2}
2025-08-14 22:20:18.549 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4168 | 🔍 [表格调试] 当前表格行数: 2
2025-08-14 22:20:18.550 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4169 | 🔍 [表格调试] 当前表格列数: 17
2025-08-14 22:20:18.550 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4171 | 🔧 [分页行号修复] 接收分页状态: 第1页, 记录1-2
2025-08-14 22:20:18.551 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1723 | 🆕 [列宽保存] 未找到表格 salary_data_2025_12_retired_employees 的列宽配置
2025-08-14 22:20:18.551 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4198 | 🔧 [P0-3修复] 分页时已恢复列宽设置: salary_data_2025_12_retired_employees
2025-08-14 22:20:18.551 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4206 | 🔧 [分页行号修复] 表格状态: 当前行数=2, 期望行数=2
2025-08-14 22:20:18.551 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 17 个, 行号起始 1, 共 2 行
2025-08-14 22:20:18.552 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_force_update_pagination_row_numbers:4286 | 🔧 [分页行号修复] 强制更新完成: 起始记录1, 共2行
2025-08-14 22:20:18.552 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3958 | 🔍 [FINAL修复] set_pagination_state最终调用完成
2025-08-14 22:20:18.552 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3972 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-08-14 22:20:18.555 | INFO     | src.services.table_data_service:load_table_data:426 | 📨[data_event] published(cache)
2025-08-14 22:20:18.556 | INFO     | src.gui.prototype.prototype_main_window:_refresh_table_data:8612 | 🔁[scope=table] 新架构刷新成功 | request_id=TR-1755181218497-bb50c359
2025-08-14 22:20:18.640 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7957 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: salary_data_2025_12_retired_employees
2025-08-14 22:20:20.601 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_refresh:8515 | 🔄[scope=page] 分页刷新被触发 | table=salary_data_2025_12_retired_employees | page=1 | size=50 | total_before=2 | request_id=PR-1755181220601-2a17b519
2025-08-14 22:20:20.602 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_refresh:8534 | 🔄[scope=page] 使用新架构刷新当前页: salary_data_2025_12_retired_employees, 第1页
2025-08-14 22:20:20.603 | INFO     | src.services.table_data_service:load_table_data:366 | 加载表格数据: 页码: 1
2025-08-14 22:20:20.603 | INFO     | src.services.table_data_service:load_table_data:390 | [缓存命中] 使用缓存数据: 第1页
2025-08-14 22:20:20.604 | INFO     | src.services.table_data_service:load_table_data:401 | 📨[data_event] publish(cache) | table=salary_data_2025_12_retired_employees | rows=2 | page=1 | size=50 | total=2 | request_id=SV-1755181220604-545948c2-C
2025-08-14 22:20:20.604 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3785 | 📥[data_event] received | table=salary_data_2025_12_retired_employees | request_id=SV-1755181220604-545948c2-C
2025-08-14 22:20:20.605 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3817 | 数据内容: 2行 x 21列
2025-08-14 22:20:20.605 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3843 | 🔧 [数据更新] 接收到cached_load操作的数据更新事件: salary_data_2025_12_retired_employees, 2行
2025-08-14 22:20:20.608 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7256 | 表 salary_data_2025_12_retired_employees 没有用户偏好设置，显示所有可见字段
2025-08-14 22:20:20.608 | INFO     | src.gui.prototype.prototype_main_window:set_data:873 | 通过公共接口设置新的表格数据(DataFrame): 2 行
2025-08-14 22:20:20.610 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7256 | 表 salary_data_2025_12_retired_employees 没有用户偏好设置，显示所有可见字段
2025-08-14 22:20:20.612 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 2行，表名: salary_data_2025_12_retired_employees
2025-08-14 22:20:20.614 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=19289006, 薪资=N/A
2025-08-14 22:20:20.615 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=19339009, 薪资=N/A
2025-08-14 22:20:20.615 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:5271 | 最大可见行数已更新: 10 -> 10
2025-08-14 22:20:20.616 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5324 | 根据数据量(2)自动调整最大可见行数为: 10
2025-08-14 22:20:20.618 | INFO     | src.modules.format_management.format_renderer:render_dataframe:107 | 🎯 [格式渲染] 隐藏字段配置: ['id', 'updated_at', 'sequence_number', 'created_at']
2025-08-14 22:20:20.618 | INFO     | src.modules.format_management.format_renderer:render_dataframe:115 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-08-14 22:20:20.622 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 增发一次性生活补贴 -> 英文名: one_time_living_allowance -> 类型: float
2025-08-14 22:20:20.624 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-08-14 22:20:20.625 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-08-14 22:20:20.626 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 备注 -> 英文名: remarks -> 类型: string
2025-08-14 22:20:20.627 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 月份 -> 英文名: month -> 类型: month_string_extract_last_two
2025-08-14 22:20:20.628 | INFO     | src.modules.format_management.field_registry:get_display_fields:1552 | 🔧 [P0-2修复] display_order转换完成: 17个字段，原始字段数: 17
2025-08-14 22:20:20.628 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | 🔧 [DEBUG] table_type=retired_employees, display_fields=17个字段
2025-08-14 22:20:20.629 | INFO     | src.modules.format_management.format_renderer:render_dataframe:211 | 🔧 [P0修复] 配置字段匹配完成: 成功匹配 17/17 个字段
2025-08-14 22:20:20.630 | INFO     | src.modules.format_management.format_renderer:render_dataframe:224 | 🔧 [P0修复] 字段重排完成: 原始17个 -> 最终17个字段
2025-08-14 22:20:20.630 | INFO     | src.modules.format_management.format_renderer:render_dataframe:240 | 🎨 [格式渲染] DataFrame格式化完成: retired_employees, 行数: 2, 列数: 17
2025-08-14 22:20:20.631 | INFO     | src.modules.format_management.unified_format_manager:format_data:367 | 🎯 [统一格式管理] 数据格式化完成: retired_employees, 行数: 2, 列数: 17
2025-08-14 22:20:20.633 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 2行数据
2025-08-14 22:20:20.636 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:52 | [数据流追踪] 开始小数据集渲染: 2行 x 17列
2025-08-14 22:20:20.636 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:112 | [数据流追踪] 小数据集渲染完成: 耗时0.0ms, 平均每行0.00ms
2025-08-14 22:20:20.637 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=2, 渲染时间=0.0ms, 策略=small_dataset
2025-08-14 22:20:20.637 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:279 | [数据流追踪] 选择小数据集渲染策略
2025-08-14 22:20:20.638 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19289006
2025-08-14 22:20:20.638 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 19339009
2025-08-14 22:20:20.639 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 2
2025-08-14 22:20:20.639 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[0]: 工号=19289006, 薪资=N/A
2025-08-14 22:20:20.640 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[1]: 工号=19339009, 薪资=N/A
2025-08-14 22:20:20.640 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:479 | 可见行数据顺序: ['19289006', '19339009']
2025-08-14 22:20:20.640 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:442 | 表格数据已设置: 2 行, 17 列
2025-08-14 22:20:20.641 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 2 行, 耗时: 27.1ms
2025-08-14 22:20:20.642 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7922 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-14 22:20:20.642 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7935 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-14 22:20:20.643 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1723 | 🆕 [列宽保存] 未找到表格 salary_data_2025_12_retired_employees 的列宽配置
2025-08-14 22:20:20.643 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-14 22:20:20.644 | INFO     | src.gui.prototype.prototype_main_window:set_data:997 | 📊[pagination-write] 跳过写入当前页数据量(total=2)，保持 total=2，等待数据事件纠正
2025-08-14 22:20:20.648 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3887 | 🔧 [P1-1修复] 数据已成功设置到UI，使用新映射表头: 2行, 17列
2025-08-14 22:20:20.648 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 17 个, 行号起始 1, 共 2 行
2025-08-14 22:20:20.648 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3903 | 🔧 [P0-新5修复] 表头强制刷新完成: salary_data_2025_12_retired_employees, 传递参数: 17个表头
2025-08-14 22:20:20.649 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3907 | 🔧 [统一数据设置] 数据已成功设置到UI: 2行, 17列
2025-08-14 22:20:20.649 | INFO     | src.gui.widgets.pagination_widget:batch_update:472 | 📊[pagination-write] batch_update | src=_on_new_data_updated/cached_load | rid=SV-1755181220604-545948c2-C | total: 2->2, page: 1->1, size: 50->50, pages: 1->1
2025-08-14 22:20:20.650 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3941 | ✅[pagination-state] now | page=1 / pages=1 / total=2 | rid=SV-1755181220604-545948c2-C
2025-08-14 22:20:20.650 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3954 | 🔍 [FINAL修复] 最终调用set_pagination_state: 第1页, 记录1-2 | rid=SV-1755181220604-545948c2-C
2025-08-14 22:20:20.651 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4166 | 🔍 [表格调试] ================== set_pagination_state 开始 ==================
2025-08-14 22:20:20.651 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4167 | 🔍 [表格调试] 收到分页状态参数: {'current_page': 1, 'page_size': 50, 'total_records': 2, 'start_record': 1, 'end_record': 2}
2025-08-14 22:20:20.652 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4168 | 🔍 [表格调试] 当前表格行数: 2
2025-08-14 22:20:20.652 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4169 | 🔍 [表格调试] 当前表格列数: 17
2025-08-14 22:20:20.652 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4171 | 🔧 [分页行号修复] 接收分页状态: 第1页, 记录1-2
2025-08-14 22:20:20.653 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1723 | 🆕 [列宽保存] 未找到表格 salary_data_2025_12_retired_employees 的列宽配置
2025-08-14 22:20:20.653 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4198 | 🔧 [P0-3修复] 分页时已恢复列宽设置: salary_data_2025_12_retired_employees
2025-08-14 22:20:20.654 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4206 | 🔧 [分页行号修复] 表格状态: 当前行数=2, 期望行数=2
2025-08-14 22:20:20.654 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 17 个, 行号起始 1, 共 2 行
2025-08-14 22:20:20.654 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_force_update_pagination_row_numbers:4286 | 🔧 [分页行号修复] 强制更新完成: 起始记录1, 共2行
2025-08-14 22:20:20.655 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3958 | 🔍 [FINAL修复] set_pagination_state最终调用完成
2025-08-14 22:20:20.655 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3972 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-08-14 22:20:20.655 | INFO     | src.services.table_data_service:load_table_data:426 | 📨[data_event] published(cache)
2025-08-14 22:20:20.656 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_refresh:8544 | 🔄[scope=page] 分页刷新成功: salary_data_2025_12_retired_employees, 第1页 | request_id=PR-1755181220601-2a17b519
2025-08-14 22:20:20.743 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7957 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: salary_data_2025_12_retired_employees
2025-08-14 22:20:23.939 | INFO     | __main__:main:523 | 应用程序正常退出
2025-08-14 22:20:23.942 | INFO     | src.gui.table_header_manager:cleanup_callback:162 | 🔧 [P1-1] 表格 table_0_2307686299200 已自动清理（弱引用回调）
