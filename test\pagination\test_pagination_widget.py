import sys
import math
import time
import threading

from PyQt5.QtWidgets import QApplication

# 确保 QApplication 单例
_app = QApplication.instance() or QApplication(sys.argv)

from src.gui.widgets.pagination_widget import PaginationWidget


def test_batch_update_atomicity_updates_all_fields():
    w = PaginationWidget()
    # 初始设置，避免 None 干扰
    w.set_total_records(0)

    rid = "TEST-RID-001"
    w.batch_update(total_records=1396, current_page=6, page_size=50, source="unit-test", request_id=rid)

    assert w.state.total_records == 1396
    assert w.state.page_size == 50
    assert w.state.current_page == 6
    assert w.state.total_pages == math.ceil(1396 / 50)
    # 起止记录：第251-300条
    assert w.state.start_record == 251
    assert w.state.end_record == 300
    # 信息标签内容检查（包含关键片段即可）
    assert "共1396条记录" in w.info_label.text()
    assert "显示第251-300条" in w.info_label.text()


def test_page_size_changed_signal_emitted(qtbot=None):
    # qtbot 非必须，直接连接信号计数
    w = PaginationWidget()

    # 设置总记录数，便于验证 total_pages
    w.set_total_records(1000)

    emitted = {"count": 0, "last_size": None}

    def on_size_changed(size: int):
        emitted["count"] += 1
        emitted["last_size"] = size

    w.page_size_changed.connect(on_size_changed)

    # 修改页面大小
    w.set_page_size(100)

    assert emitted["count"] == 1
    assert emitted["last_size"] == 100
    assert w.state.page_size == 100
    assert w.state.total_pages == math.ceil(1000 / 100)

