#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试异动表导入修复是否生效

测试场景：
1. 测试异动表表名生成
2. 测试导航跳转逻辑
"""

import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.gui.prototype.prototype_main_window import PrototypeMainWindow
from src.utils.log_config import setup_logger

def test_table_name_generation():
    """测试表名生成逻辑"""
    logger = setup_logger(__name__)
    
    # 创建一个mock对象用于测试
    class MockMainWindow:
        def __init__(self):
            self.logger = logger
            
        def _generate_table_name_from_path(self, path_list):
            """复制修复后的方法逻辑"""
            import re
            
            # 检查路径是否为空
            if not path_list:
                self.logger.warning("🔧 [表名生成] 路径列表为空")
                return ""
            
            # 处理异动人员表的情况
            if len(path_list) > 0 and path_list[0] == "异动人员表":
                self.logger.info(f"🔧 [表名生成] 处理异动表路径: {path_list}")
                
                # 检查路径完整性
                if len(path_list) < 4:
                    self.logger.warning(f"🔧 [表名生成] 异动表路径不完整({len(path_list)}层): {path_list}")
                    return ""
                
                # 生成异动表名
                base_name = "change_data"
                year = re.sub(r'\D', '', path_list[1])
                month = re.sub(r'\D', '', path_list[2]).zfill(2)
                
                # 异动类型映射
                change_type_map = {
                    "起薪": "new_salary",
                    "调薪": "salary_adjustment",
                    "离职": "resignation",
                    "退休": "retirement",
                    "全部在职人员": "active_employees",
                    "离休人员": "retired_employees",
                    "退休人员": "pension_employees",
                    "A岗职工": "a_grade_employees",
                    "其他": "other"
                }
                change_type = change_type_map.get(path_list[3], "unknown")
                
                table_name = f"{base_name}_{year}_{month}_{change_type}"
                self.logger.info(f"🔧 [表名生成] 生成异动表名: {table_name}")
                return table_name
            
            # 检查第一级是否是"工资表"
            if len(path_list) > 0 and path_list[0] != "工资表":
                self.logger.warning(f"🔧 [表名生成] 未识别的路径类型: {path_list[0]}")
                return ""
            
            # 处理工资表路径
            if len(path_list) >= 4:
                base_name = "salary_data"
                year = re.sub(r'\D', '', path_list[1])
                month = re.sub(r'\D', '', path_list[2]).zfill(2)
                
                category = path_list[3]
                category_map = {
                    "全部在职人员": "active_employees",
                    "A岗职工": "a_grade_employees",
                    "B岗职工": "b_grade_employees",
                    "退休人员": "pension_employees",
                    "离休人员": "retired_employees",
                    "其他人员": "other_employees"
                }
                category_en = category_map.get(category, "unknown_category")
                
                table_name = f"{base_name}_{year}_{month}_{category_en}"
                self.logger.info(f"🔧 [表名生成] 从完整路径生成表名: {path_list} -> {table_name}")
                return table_name
            
            return ""
    
    # 测试
    window = MockMainWindow()
    
    print("\n=== 测试异动表表名生成 ===")
    
    # 测试案例1：异动表 - 全部在职人员
    path1 = ["异动人员表", "2025年", "12月", "全部在职人员"]
    result1 = window._generate_table_name_from_path(path1)
    print(f"路径: {' > '.join(path1)}")
    print(f"生成表名: {result1}")
    print(f"预期表名: change_data_2025_12_active_employees")
    print(f"测试结果: {'PASS' if result1 == 'change_data_2025_12_active_employees' else 'FAIL'}")
    
    print()
    
    # 测试案例2：异动表 - 退休人员
    path2 = ["异动人员表", "2025年", "08月", "退休人员"]
    result2 = window._generate_table_name_from_path(path2)
    print(f"路径: {' > '.join(path2)}")
    print(f"生成表名: {result2}")
    print(f"预期表名: change_data_2025_08_pension_employees")
    print(f"测试结果: {'PASS' if result2 == 'change_data_2025_08_pension_employees' else 'FAIL'}")
    
    print()
    
    # 测试案例3：工资表路径（确保不影响原有功能）
    path3 = ["工资表", "2025年", "08月", "全部在职人员"]
    result3 = window._generate_table_name_from_path(path3)
    print(f"路径: {' > '.join(path3)}")
    print(f"生成表名: {result3}")
    print(f"预期表名: salary_data_2025_08_active_employees")
    print(f"测试结果: {'PASS' if result3 == 'salary_data_2025_08_active_employees' else 'FAIL'}")
    
    print()
    
    # 测试案例4：不完整的异动表路径
    path4 = ["异动人员表", "2025年", "12月"]
    result4 = window._generate_table_name_from_path(path4)
    print(f"路径: {' > '.join(path4)}")
    print(f"生成表名: {result4}")
    print(f"预期表名: （空字符串）")
    print(f"测试结果: {'PASS' if result4 == '' else 'FAIL'}")
    
    print()
    
    # 测试案例5：未知路径类型
    path5 = ["其他表", "2025年", "12月", "测试"]
    result5 = window._generate_table_name_from_path(path5)
    print(f"路径: {' > '.join(path5)}")
    print(f"生成表名: {result5}")
    print(f"预期表名: （空字符串）")
    print(f"测试结果: {'PASS' if result5 == '' else 'FAIL'}")
    
    print("\n=== 测试完成 ===")
    
    # 汇总结果
    all_passed = (
        result1 == 'change_data_2025_12_active_employees' and
        result2 == 'change_data_2025_08_pension_employees' and
        result3 == 'salary_data_2025_08_active_employees' and
        result4 == '' and
        result5 == ''
    )
    
    if all_passed:
        print("\n[SUCCESS] 所有测试通过！异动表表名生成逻辑修复成功。")
    else:
        print("\n[FAILED] 部分测试失败，请检查修复逻辑。")
    
    return all_passed

if __name__ == "__main__":
    success = test_table_name_generation()
    sys.exit(0 if success else 1)