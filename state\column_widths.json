{"default_table": {"column_widths": [79, 79, 103, 127, 103, 145, 145, 79, 103, 157, 91, 103, 103, 103, 79, 103, 181, 79, 79, 103, 121, 100], "column_names": ["工号", "姓名", "部门名称", "人员类别代码", "人员类别", "2025年岗位工资", "2025年薪级工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "交通补贴", "物业补贴", "住房补贴", "车补", "通讯补贴", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "代扣代存养老保险"], "timestamp": 1755258191.4468012}, "change_data_2025_12_retired_employees": {"column_widths": [100, 100, 100, 100, 1132], "column_names": ["姓名", "部门名称", "补发", "借支", "护理费"], "timestamp": 1755258577.2476273}, "change_data_2025_12_pension_employees": {"column_widths": [100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 145, 100, 100, 100], "column_names": ["姓名", "部门名称", "人员类别代码", "津贴", "结余津贴", "物业补贴", "住房补贴", "补发", "借支", "应发工资", "基本退休费", "离退休生活补贴", "护理费", "增资预付", "保险扣款"], "timestamp": 1755258605.365345}, "salary_data_2025_08_a_grade_employees": {"column_widths": [100, 100, 100, 100, 100, 143, 149, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 100], "column_names": ["工号", "姓名", "部门名称", "人员类别", "人员类别代码", "2025年岗位工资", "2025年校龄工资", "津贴", "结余津贴", "2025年基础性绩效", "卫生费", "2025年生活补贴", "车补", "2025年奖励性绩效预发", "补发", "借支", "应发工资", "2025公积金", "保险扣款", "代扣代存养老保险", "年份", "月份"], "timestamp": 1755258677.919339}}