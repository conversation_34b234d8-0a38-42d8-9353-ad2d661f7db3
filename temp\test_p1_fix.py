#!/usr/bin/env python3
"""
测试P1级WAL数据库状态修复效果
"""
import sys
import os
import time
sys.path.insert(0, os.path.abspath('.'))

from src.modules.data_storage.database_manager import DatabaseManager
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.modules.system_config.config_manager import ConfigManager

def test_p1_fix():
    print("=== P1级WAL数据库状态修复测试 ===")
    
    # 模拟系统启动过程
    print("\n🚀 模拟系统启动过程...")
    
    # 1. 初始化配置管理器
    print("1. 初始化配置管理器...")
    config_manager = ConfigManager()
    config_manager.load_config()
    
    # 2. 初始化数据库管理器
    print("2. 初始化数据库管理器...")
    db_manager = DatabaseManager(config_manager=config_manager)
    
    # 3. 初始化动态表管理器（包含P1修复）
    print("3. 初始化动态表管理器（包含P1修复）...")
    start_time = time.time()
    table_manager = DynamicTableManager(db_manager, config_manager)
    init_time = time.time() - start_time
    print(f"   初始化耗时: {init_time:.3f}s")
    
    # 4. 测试表查询功能
    print("\n📊 测试表查询功能...")
    
    # 测试1：查询所有表
    print("测试1: 查询所有表")
    start_time = time.time()
    all_tables = table_manager.get_table_list()
    query_time = time.time() - start_time
    print(f"   找到 {len(all_tables)} 个表，耗时: {query_time:.3f}s")
    
    # 测试2：查询salary_data类型的表
    print("测试2: 查询salary_data类型的表")
    start_time = time.time()
    salary_tables = table_manager.get_table_list(table_type='salary_data')
    query_time = time.time() - start_time
    print(f"   找到 {len(salary_tables)} 个salary_data表，耗时: {query_time:.3f}s")
    
    if salary_tables:
        print("   salary_data表列表:")
        for table in salary_tables:
            table_name = table.get('table_name', 'unknown')
            display_name = table.get('display_name', 'unknown')
            year = table.get('year', 'unknown')
            month = table.get('month', 'unknown')
            print(f"     - {table_name} ({display_name}, {year}年{month}月)")
    
    # 测试3：多次查询一致性
    print("\n测试3: 多次查询一致性")
    results = []
    for i in range(5):
        start_time = time.time()
        tables = table_manager.get_table_list(table_type='salary_data')
        query_time = time.time() - start_time
        count = len(tables)
        results.append(count)
        print(f"   第{i+1}次查询: {count} 个表，耗时: {query_time:.3f}s")
    
    # 检查一致性
    if len(set(results)) == 1:
        print("   ✅ 多次查询结果一致")
    else:
        print(f"   ❌ 多次查询结果不一致: {results}")
    
    # 测试4：导航树数据
    print("\n测试4: 导航树数据")
    start_time = time.time()
    tree_data = table_manager.get_navigation_tree_data()
    query_time = time.time() - start_time
    print(f"   导航树数据获取耗时: {query_time:.3f}s")
    print(f"   导航树年份数: {len(tree_data)}")
    
    for year, months in tree_data.items():
        print(f"     {year}年: {len(months)} 个月")
        for month, items in months.items():
            print(f"       {month}月: {len(items)} 个项目")
    
    # 测试5：数据库状态验证
    print("\n测试5: 数据库状态验证")
    
    # 检查数据库就绪状态
    ready = table_manager._ensure_database_ready()
    print(f"   数据库就绪状态: {ready}")
    
    # 检查WAL状态
    wal_result = db_manager.execute_query("PRAGMA wal_checkpoint(PASSIVE)")
    if wal_result:
        info = wal_result[0]
        print(f"   WAL状态: busy={info.get('busy', 0)}, log={info.get('log', 0)}, checkpointed={info.get('checkpointed', 0)}")
    
    # 测试6：性能基准
    print("\n测试6: 性能基准")
    
    # 连续查询性能
    start_time = time.time()
    for i in range(10):
        table_manager.get_table_list(table_type='salary_data')
    total_time = time.time() - start_time
    avg_time = total_time / 10
    print(f"   10次连续查询总耗时: {total_time:.3f}s")
    print(f"   平均每次查询耗时: {avg_time:.3f}s")
    
    # 总结
    print("\n📋 测试总结:")
    print(f"   ✅ 系统启动成功")
    print(f"   ✅ 表查询功能正常")
    print(f"   ✅ 找到 {len(salary_tables)} 个工资数据表")
    print(f"   ✅ 查询结果一致性: {'通过' if len(set(results)) == 1 else '失败'}")
    print(f"   ✅ 数据库状态: {'就绪' if ready else '未就绪'}")
    print(f"   ✅ 平均查询性能: {avg_time:.3f}s")
    
    if len(salary_tables) > 0 and len(set(results)) == 1 and ready:
        print("\n🎉 P1修复测试通过！系统启动时能正确找到现有数据表")
        return True
    else:
        print("\n❌ P1修复测试失败，需要进一步调试")
        return False

if __name__ == "__main__":
    success = test_p1_fix()
    sys.exit(0 if success else 1)
