#!/usr/bin/env python3
"""
测试分页刷新和表格刷新修复效果

验证：
1. _on_pagination_refresh 方法是否正确移动到 PrototypeMainWindow
2. _refresh_table_data 方法是否正确移动到 PrototypeMainWindow  
3. 信号连接是否正确
4. 不再出现 AttributeError: 'MainWorkspaceArea' object has no attribute '_get_active_table_name'
"""

import sys
import os
import inspect
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_method_locations():
    """测试方法是否在正确的类中"""
    print("🔍 测试方法位置...")
    
    try:
        # 导入类
        from src.gui.prototype.prototype_main_window import PrototypeMainWindow, MainWorkspaceArea
        
        # 检查 PrototypeMainWindow 中是否有正确的方法
        prototype_methods = dir(PrototypeMainWindow)
        workspace_methods = dir(MainWorkspaceArea)
        
        # 1. 检查 _on_pagination_refresh 是否在 PrototypeMainWindow 中
        if '_on_pagination_refresh' in prototype_methods:
            print("✅ _on_pagination_refresh 方法已正确移动到 PrototypeMainWindow")
        else:
            print("❌ _on_pagination_refresh 方法未在 PrototypeMainWindow 中找到")
            
        # 2. 检查 _refresh_table_data 是否在 PrototypeMainWindow 中
        if '_refresh_table_data' in prototype_methods:
            print("✅ _refresh_table_data 方法已正确移动到 PrototypeMainWindow")
        else:
            print("❌ _refresh_table_data 方法未在 PrototypeMainWindow 中找到")
            
        # 3. 检查 MainWorkspaceArea 中是否还有这些方法
        if '_on_pagination_refresh' not in workspace_methods:
            print("✅ _on_pagination_refresh 方法已从 MainWorkspaceArea 中移除")
        else:
            print("❌ _on_pagination_refresh 方法仍在 MainWorkspaceArea 中")
            
        if '_refresh_table_data' not in workspace_methods:
            print("✅ _refresh_table_data 方法已从 MainWorkspaceArea 中移除")
        else:
            print("❌ _refresh_table_data 方法仍在 MainWorkspaceArea 中")
            
        # 4. 检查 _get_active_table_name 是否在 PrototypeMainWindow 中
        if '_get_active_table_name' in prototype_methods:
            print("✅ _get_active_table_name 方法在 PrototypeMainWindow 中")
        else:
            print("❌ _get_active_table_name 方法未在 PrototypeMainWindow 中找到")
            
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_method_signatures():
    """测试方法签名是否正确"""
    print("\n🔍 测试方法签名...")
    
    try:
        from src.gui.prototype.prototype_main_window import PrototypeMainWindow
        
        # 检查方法签名
        if hasattr(PrototypeMainWindow, '_on_pagination_refresh'):
            method = getattr(PrototypeMainWindow, '_on_pagination_refresh')
            sig = inspect.signature(method)
            print(f"✅ _on_pagination_refresh 签名: {sig}")
            
        if hasattr(PrototypeMainWindow, '_refresh_table_data'):
            method = getattr(PrototypeMainWindow, '_refresh_table_data')
            sig = inspect.signature(method)
            print(f"✅ _refresh_table_data 签名: {sig}")
            
        if hasattr(PrototypeMainWindow, '_get_active_table_name'):
            method = getattr(PrototypeMainWindow, '_get_active_table_name')
            sig = inspect.signature(method)
            print(f"✅ _get_active_table_name 签名: {sig}")
            
        return True
        
    except Exception as e:
        print(f"❌ 测试方法签名失败: {e}")
        return False

def test_code_content():
    """测试代码内容是否正确修改"""
    print("\n🔍 测试代码内容...")
    
    try:
        main_window_file = project_root / "src" / "gui" / "prototype" / "prototype_main_window.py"
        
        if not main_window_file.exists():
            print("❌ 主窗口文件不存在")
            return False
            
        with open(main_window_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 检查是否有正确的信号连接
        if "self.main_workspace.pagination_widget.refresh_requested.connect(self._on_pagination_refresh)" in content:
            print("✅ 找到正确的分页刷新信号连接")
        else:
            print("❌ 未找到正确的分页刷新信号连接")
            
        # 检查是否移除了错误的连接
        if "# self.pagination_widget.refresh_requested.connect(self._on_pagination_refresh)" in content:
            print("✅ 错误的信号连接已被注释掉")
        else:
            print("⚠️  错误的信号连接可能未被正确处理")
            
        # 检查方法是否正确移动
        pagination_refresh_count = content.count("def _on_pagination_refresh(self):")
        refresh_table_data_count = content.count("def _refresh_table_data(self):")
        
        print(f"📊 _on_pagination_refresh 方法定义数量: {pagination_refresh_count}")
        print(f"📊 _refresh_table_data 方法定义数量: {refresh_table_data_count}")
        
        if pagination_refresh_count == 1:
            print("✅ _on_pagination_refresh 方法只有一个定义")
        else:
            print(f"❌ _on_pagination_refresh 方法有 {pagination_refresh_count} 个定义")
            
        if refresh_table_data_count == 1:
            print("✅ _refresh_table_data 方法只有一个定义")
        else:
            print(f"❌ _refresh_table_data 方法有 {refresh_table_data_count} 个定义")
            
        return True
        
    except Exception as e:
        print(f"❌ 测试代码内容失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试分页刷新和表格刷新修复效果\n")
    
    results = []
    
    # 运行测试
    results.append(test_method_locations())
    results.append(test_method_signatures())
    results.append(test_code_content())
    
    # 汇总结果
    print("\n📊 测试结果汇总:")
    passed = sum(results)
    total = len(results)
    
    print(f"✅ 通过: {passed}/{total}")
    
    if passed == total:
        print("\n🎉 所有测试通过！修复效果良好。")
        print("\n📋 修复总结:")
        print("1. ✅ _on_pagination_refresh 方法已移动到 PrototypeMainWindow")
        print("2. ✅ _refresh_table_data 方法已移动到 PrototypeMainWindow")
        print("3. ✅ 分页刷新信号已正确连接到主窗口")
        print("4. ✅ 错误的信号连接已被移除")
        print("\n🎯 预期效果:")
        print("- 分页刷新按钮应该能正常工作")
        print("- 表格刷新按钮应该能正常工作")
        print("- 不再出现 AttributeError: 'MainWorkspaceArea' object has no attribute '_get_active_table_name'")
    else:
        print(f"\n❌ 有 {total - passed} 个测试失败，需要进一步检查。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
