# 时间导入遮蔽导致UI不显示数据：问题分析与修复建议

更新时间：2025-08-12

## 背景
- 现象：数据导入提示成功，但列表区域不显示新数据。
- 要求：按时间线分析最新日志，并结合源码交叉验证，定位根因并提出修复方案。

## 时间线要点（日志证据）
1. 启动后初期无工资数据表，多次 UI set_data 失败，报错：UnboundLocalError: cannot access local variable 'time'...
2. 15:24:23–15:24:33 多 Sheet 导入成功：建表与写入 4 张表（2/13/1396/62 条）。
3. 导航刷新并自动跳转到 "工资表 > 2025年 > 08月 > 全部在职人员"；分页策略选择 pagination；数据库分页查询成功返回第1页 50 行。
4. 统一字段与格式化完成（50×24）。
5. 在 UI 分页数据落表 set_data 过程中，再次发生同样 UnboundLocalError，UI 保持空表。
6. 切换至 A岗 职工视图亦然，服务层 load_table_data 同报 UnboundLocalError。

结论：导入与数据库/格式化链路均正确，异常发生在 UI/服务层数据设置流程，致使数据未显示。

## 根因（源码交叉验证）
- 典型触发机制：函数体内存在 `import time` 或 `from time import time`，则 `time` 在整个函数作用域被视为“局部变量”。若在该函数更早位置调用了 `time.time()`，会抛出 `UnboundLocalError`。
- 命中点：
  - `src/gui/prototype/prototype_main_window.py::set_data` 中先调用 `time.time()`，后在函数体内存在 `import uuid, time`，导致报错。
  - `src/services/table_data_service.py::load_table_data` 中同理，函数体内多处 `import uuid, time`，而更早处已有 `time.time()` 调用，触发同类异常。

## 影响
- UI 层 set_data 被异常中断，导致表格不显示新数据；
- 服务层 load_table_data 失败时，走回退逻辑显示空表头；
- 导航/分页显示被阻断，出现“延迟设置行号失败”等伴随症状。

## 修复原则
- 统一在模块顶层 `import time`（必要时 `import time as _time`），函数体内严禁 `import time` / `from time import time`；
- 在需要生成请求ID等位置，直接复用模块顶层 `time` 与 `uuid`；
- 建议全局扫描清理此类用法，避免隐患。

## 已实施的最小修复（本次）
- 移除以下函数体内的 `import uuid, time`：
  - PrototypeMainWindow：`_on_refresh_data`、`set_data`（分页分支）、`_on_pagination_refresh`、`_refresh_table_data`、`_check_and_load_data_with_pagination`（分页UI同步）、`_on_page_size_changed_new_architecture`；并将 `from time import time` / `import time as _t` 改为使用模块顶层 `time.time()`。
  - TableDataService：`load_table_data` 内的三处 `import uuid, time` 去除；
- 在两个模块的文件顶部显式 `import uuid`（若缺失）。

## 建议的后续动作
1. 全项目扫描：清除函数体内 `import time` / `from time import time` / `import time as ...` 模式。
2. 端到端验证：重复“导入→导航→分页加载→显示”流程，确认不再出现 UnboundLocalError，且表格可见数据正常。
3. （可选）完善缓存加载器初始化，规避 `_optimized_cache_loader` 回退告警。
4. （可选）统一月份显示格式（08月/8月），降低路径不一致带来的困惑。
5. （开发期）遵循“日志不节流”策略，临时移除 log_throttle，便于排查。

## 验证提示
- 关键日志不应再出现：`UnboundLocalError("cannot access local variable 'time'..." )`；
- 应出现：分页数据加载成功、字段映射/格式化成功、表格 set_data 成功并展示记录数的日志；

---
本文件遵循“项目问题记录文档位置约定”，位于 docs/problems。

