2025-08-13 13:30:38.491 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4206 | 🔧 [分页行号修复] 表格状态: 当前行数=50, 期望行数=50
2025-08-13 13:30:38.492 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 24 个, 行号起始 151, 共 50 行
2025-08-13 13:30:38.492 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_force_update_pagination_row_numbers:4286 | 🔧 [分页行号修复] 强制更新完成: 起始记录151, 共50行
2025-08-13 13:30:38.492 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3930 | 🔍 [FINAL修复] set_pagination_state最终调用完成
2025-08-13 13:30:38.493 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3944 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-08-13 13:30:38.493 | INFO     | src.services.table_data_service:load_table_data:426 | 📨[data_event] published(cache)
2025-08-13 13:30:38.493 | INFO     | src.services.table_data_service:get_paginated_data:884 | 🔧 [P1修复] 分页数据获取成功: salary_data_2025_08_active_employees, 返回 50 行
2025-08-13 13:30:38.494 | INFO     | src.gui.prototype.prototype_main_window:_execute_pagination_data_load:5050 | 🔧 [事件优化] 分页数据加载成功，UI更新已加入队列: 第4页
2025-08-13 13:30:38.494 | INFO     | src.core.pagination_state_manager:complete_pagination:189 | 🔧 [P1修复] 分页操作已完成: salary_data_2025_08_active_employees, 页4, 成功=True, 耗时=0.012s
2025-08-13 13:30:38.495 | INFO     | src.core.pagination_event_optimizer:process_events:167 | 🔧 [事件处理] 批量处理完成: 1个事件
2025-08-13 13:30:38.551 | INFO     | src.gui.prototype.prototype_main_window:_restore_table_ui_state:10110 | 🔧 [P0-新4修复] 通过StateManager恢复状态成功: salary_data_2025_08_active_employees
2025-08-13 13:30:38.551 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7957 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: salary_data_2025_08_active_employees
2025-08-13 13:30:38.595 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 50行，表名: None
2025-08-13 13:30:38.596 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=20221025, 薪资=1251.00
2025-08-13 13:30:38.596 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=20191761, 薪资=1251.00
2025-08-13 13:30:38.596 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:5271 | 最大可见行数已更新: 50 -> 50
2025-08-13 13:30:38.597 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5324 | 根据数据量(50)自动调整最大可见行数为: 50
2025-08-13 13:30:38.598 | INFO     | src.modules.format_management.format_renderer:render_dataframe:107 | 🎯 [格式渲染] 隐藏字段配置: ['sequence', 'id', 'row_number', 'created_at', 'updated_at']
2025-08-13 13:30:38.599 | INFO     | src.modules.format_management.format_renderer:render_dataframe:115 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-08-13 13:30:38.605 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-08-13 13:30:38.606 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-08-13 13:30:38.607 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 月份 -> 英文名: month -> 类型: month_string
2025-08-13 13:30:38.607 | INFO     | src.modules.format_management.field_registry:get_display_fields:1551 | 🔧 [P0-2修复] display_order转换完成: 24个字段，原始字段数: 24
2025-08-13 13:30:38.608 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | 🔧 [DEBUG] table_type=active_employees, display_fields=24个字段
2025-08-13 13:30:38.608 | INFO     | src.modules.format_management.format_renderer:render_dataframe:210 | 🔧 [P1修复] 字段匹配完成: 成功匹配 24/24 个字段
2025-08-13 13:30:38.609 | INFO     | src.modules.format_management.format_renderer:render_dataframe:214 | 🎯 [格式渲染] 已按display_order排列字段: 24个字段
2025-08-13 13:30:38.609 | INFO     | src.modules.format_management.format_renderer:render_dataframe:234 | 🎨 [格式渲染] DataFrame格式化完成: active_employees, 行数: 50, 列数: 24
2025-08-13 13:30:38.609 | INFO     | src.modules.format_management.unified_format_manager:format_data:367 | 🎯 [统一格式管理] 数据格式化完成: active_employees, 行数: 50, 列数: 24
2025-08-13 13:30:38.617 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 50行数据
2025-08-13 13:30:38.617 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:52 | [数据流追踪] 开始小数据集渲染: 50行 x 24列
2025-08-13 13:30:38.622 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:112 | [数据流追踪] 小数据集渲染完成: 耗时5.0ms, 平均每行0.10ms
2025-08-13 13:30:38.622 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=50, 渲染时间=5.0ms, 策略=small_dataset
2025-08-13 13:30:38.622 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:279 | [数据流追踪] 选择小数据集渲染策略
2025-08-13 13:30:38.623 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 20221025
2025-08-13 13:30:38.623 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20191761
2025-08-13 13:30:38.623 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20171620
2025-08-13 13:30:38.623 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20171630
2025-08-13 13:30:38.624 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181658
2025-08-13 13:30:38.624 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 50
2025-08-13 13:30:38.624 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[0]: 工号=20221025, 薪资=1251.00
2025-08-13 13:30:38.625 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[1]: 工号=20191761, 薪资=1251.00
2025-08-13 13:30:38.625 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[2]: 工号=20171620, 薪资=1251.00
2025-08-13 13:30:38.625 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[3]: 工号=20171630, 薪资=1251.00
2025-08-13 13:30:38.625 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[4]: 工号=20181658, 薪资=1251.00
2025-08-13 13:30:38.626 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:479 | 可见行数据顺序: ['20221025', '20191761', '20171620', '20171630', '20181658']
2025-08-13 13:30:38.627 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 50 行, 耗时: 31.0ms
2025-08-13 13:30:38.627 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3092 | 🔧 [重影修复] 数据设置完成，行号已由HeaderUpdateManager统一管理
2025-08-13 13:30:38.627 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7922 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-13 13:30:38.628 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7932 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-08-13 13:30:38.628 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7933 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 升序
2025-08-13 13:30:38.629 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: salary_data_2025_08_active_employees (24/24 列)
2025-08-13 13:30:38.629 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3157 | 🔧 [重影修复] 行号已由HeaderUpdateManager统一管理
2025-08-13 13:30:38.629 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-13 13:30:38.629 | INFO     | src.gui.prototype.prototype_main_window:_restore_table_ui_state:10110 | 🔧 [P0-新4修复] 通过StateManager恢复状态成功: salary_data_2025_08_active_employees
2025-08-13 13:30:38.630 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 24 个, 行号起始 151, 共 50 行
2025-08-13 13:30:38.630 | INFO     | src.gui.prototype.prototype_main_window:_update_pagination_ui:5103 | 🔧 [事件优化] UI更新完成: 第4页，50条记录
2025-08-13 13:30:38.630 | INFO     | src.core.pagination_event_optimizer:process_events:167 | 🔧 [事件处理] 批量处理完成: 1个事件
2025-08-13 13:30:38.677 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: salary_data_2025_08_active_employees (24/24 列)
2025-08-13 13:30:38.677 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_adjust_column_widths_delayed_with_restore:4730 | 🔧 [P0-3修复] 用户列宽设置已恢复: salary_data_2025_08_active_employees
2025-08-13 13:30:38.678 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_adjust_column_widths_delayed_with_restore:4752 | 🔧 [列宽修复] 使用用户保存的列宽，跳过默认调整
2025-08-13 13:30:38.729 | INFO     | src.gui.prototype.prototype_main_window:_restore_table_ui_state:10110 | 🔧 [P0-新4修复] 通过StateManager恢复状态成功: salary_data_2025_08_active_employees
2025-08-13 13:30:38.729 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7957 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: salary_data_2025_08_active_employees
2025-08-13 13:30:41.380 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4740 | 📍[page-change] 入口 | table=salary_data_2025_08_active_employees | target_page=5 | request_id=PG-1755063041380-7958c790
2025-08-13 13:30:41.381 | INFO     | src.core.pagination_state_manager:can_start_pagination:113 | ✅ [允许] 分页请求可以开始: salary_data_2025_08_active_employees, 页5
2025-08-13 13:30:41.383 | INFO     | src.gui.prototype.prototype_main_window:_save_current_table_ui_state:10036 | 🔧 [P0-2修复] 通过StateManager保存状态成功: salary_data_2025_08_active_employees
2025-08-13 13:30:41.383 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4819 | 🔧 [分页请求] 第5页, 表: salary_data_2025_08_active_employees, 上下文已设置
2025-08-13 13:30:41.384 | INFO     | src.core.request_deduplication_manager:__init__:65 | 请求去重管理器初始化完成，默认TTL: 0.3秒
2025-08-13 13:30:41.384 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4836 | 🔧 [立即修复] 分页请求通过强化去重检查: salary_data_2025_08_active_employees, 第5页
2025-08-13 13:30:41.385 | INFO     | src.services.table_data_service:load_table_data:366 | 加载表格数据: 页码: 5
2025-08-13 13:30:41.385 | INFO     | src.core.unified_data_request_manager:request_table_data:212 | 开始处理数据请求
2025-08-13 13:30:41.385 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:779 | [FIX] [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_08_active_employees' 中（类型: REAL）
2025-08-13 13:30:41.386 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:779 | [FIX] [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_08_active_employees' 中（类型: REAL）
2025-08-13 13:30:41.386 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:590 | 正在从表 salary_data_2025_08_active_employees 分页获取数据（支持排序）: 第5页, 每页50条, 排序=1列
2025-08-13 13:30:41.387 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:779 | [FIX] [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_08_active_employees' 中（类型: REAL）
2025-08-13 13:30:41.390 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:706 | 成功从表 salary_data_2025_08_active_employees 获取第5页数据（含排序）: 50 行，总计1396行
2025-08-13 13:30:41.391 | INFO     | src.core.unified_data_request_manager:request_table_data:262 | 数据请求处理完成: 字段=28, 行数=50, 耗时=6.0ms
2025-08-13 13:30:41.392 | INFO     | src.services.table_data_service:load_table_data:462 | [新架构] 使用统一格式管理器（单例优化）
2025-08-13 13:30:41.392 | INFO     | src.modules.format_management.format_renderer:render_dataframe:107 | 🎯 [格式渲染] 隐藏字段配置: ['sequence', 'id', 'row_number', 'created_at', 'updated_at']
2025-08-13 13:30:41.393 | INFO     | src.modules.format_management.format_renderer:render_dataframe:115 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-08-13 13:30:41.400 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-08-13 13:30:41.401 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-08-13 13:30:41.403 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 月份 -> 英文名: month -> 类型: month_string
2025-08-13 13:30:41.405 | INFO     | src.modules.format_management.field_registry:get_display_fields:1551 | 🔧 [P0-2修复] display_order转换完成: 24个字段，原始字段数: 24
2025-08-13 13:30:41.406 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | 🔧 [DEBUG] table_type=active_employees, display_fields=24个字段
2025-08-13 13:30:41.406 | INFO     | src.modules.format_management.format_renderer:render_dataframe:210 | 🔧 [P1修复] 字段匹配完成: 成功匹配 24/24 个字段
2025-08-13 13:30:41.407 | INFO     | src.modules.format_management.format_renderer:render_dataframe:214 | 🎯 [格式渲染] 已按display_order排列字段: 24个字段
2025-08-13 13:30:41.407 | INFO     | src.modules.format_management.format_renderer:render_dataframe:234 | 🎨 [格式渲染] DataFrame格式化完成: active_employees, 行数: 50, 列数: 24
2025-08-13 13:30:41.408 | INFO     | src.modules.format_management.unified_format_manager:format_data:367 | 🎯 [统一格式管理] 数据格式化完成: active_employees, 行数: 50, 列数: 24
2025-08-13 13:30:41.409 | INFO     | src.core.unified_state_manager:update_table_state:232 | 表状态已更新: salary_data_2025_08_active_employees, 变更类型: None
2025-08-13 13:30:41.409 | INFO     | src.services.table_data_service:load_table_data:512 | [修复数据发布] 数据加载成功，发布更新事件: 50行
2025-08-13 13:30:41.409 | INFO     | src.services.table_data_service:load_table_data:516 | 📨[data_event] publish | table=salary_data_2025_08_active_employees | rows=50 | page=5 | size=50 | total=1396 | req_type=RequestType.INITIAL_LOAD | request_id=PG-1755063041380-7958c790
2025-08-13 13:30:41.410 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3757 | 📥[data_event] received | table=salary_data_2025_08_active_employees | request_id=PG-1755063041380-7958c790
2025-08-13 13:30:41.410 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3789 | 数据内容: 50行 x 24列
2025-08-13 13:30:41.410 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3815 | 🔧 [数据更新] 接收到initial_load操作的数据更新事件: salary_data_2025_08_active_employees, 50行
2025-08-13 13:30:41.412 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:6861 | 表 salary_data_2025_08_active_employees 没有用户偏好设置，显示所有可见字段
2025-08-13 13:30:41.412 | INFO     | src.gui.prototype.prototype_main_window:set_data:848 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-08-13 13:30:41.414 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:6861 | 表 salary_data_2025_08_active_employees 没有用户偏好设置，显示所有可见字段
2025-08-13 13:30:41.416 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 50行，表名: salary_data_2025_08_active_employees
2025-08-13 13:30:41.417 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=20191757, 薪资=1251.00
2025-08-13 13:30:41.418 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=20181667, 薪资=1251.00
2025-08-13 13:30:41.418 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:5271 | 最大可见行数已更新: 50 -> 50
2025-08-13 13:30:41.418 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5324 | 根据数据量(50)自动调整最大可见行数为: 50
2025-08-13 13:30:41.420 | INFO     | src.modules.format_management.format_renderer:render_dataframe:107 | 🎯 [格式渲染] 隐藏字段配置: ['sequence', 'id', 'row_number', 'created_at', 'updated_at']
2025-08-13 13:30:41.420 | INFO     | src.modules.format_management.format_renderer:render_dataframe:115 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-08-13 13:30:41.426 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-08-13 13:30:41.427 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-08-13 13:30:41.428 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 月份 -> 英文名: month -> 类型: month_string
2025-08-13 13:30:41.428 | INFO     | src.modules.format_management.field_registry:get_display_fields:1551 | 🔧 [P0-2修复] display_order转换完成: 24个字段，原始字段数: 24
2025-08-13 13:30:41.429 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | 🔧 [DEBUG] table_type=active_employees, display_fields=24个字段
2025-08-13 13:30:41.429 | INFO     | src.modules.format_management.format_renderer:render_dataframe:210 | 🔧 [P1修复] 字段匹配完成: 成功匹配 24/24 个字段
2025-08-13 13:30:41.430 | INFO     | src.modules.format_management.format_renderer:render_dataframe:214 | 🎯 [格式渲染] 已按display_order排列字段: 24个字段
2025-08-13 13:30:41.430 | INFO     | src.modules.format_management.format_renderer:render_dataframe:234 | 🎨 [格式渲染] DataFrame格式化完成: active_employees, 行数: 50, 列数: 24
2025-08-13 13:30:41.430 | INFO     | src.modules.format_management.unified_format_manager:format_data:367 | 🎯 [统一格式管理] 数据格式化完成: active_employees, 行数: 50, 列数: 24
2025-08-13 13:30:41.439 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 50行数据
2025-08-13 13:30:41.440 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:52 | [数据流追踪] 开始小数据集渲染: 50行 x 24列
2025-08-13 13:30:41.444 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:112 | [数据流追踪] 小数据集渲染完成: 耗时4.0ms, 平均每行0.08ms
2025-08-13 13:30:41.444 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=50, 渲染时间=4.0ms, 策略=small_dataset
2025-08-13 13:30:41.445 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:279 | [数据流追踪] 选择小数据集渲染策略
2025-08-13 13:30:41.445 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 20191757
2025-08-13 13:30:41.445 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20181667
2025-08-13 13:30:41.446 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20251009
2025-08-13 13:30:41.446 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20021324
2025-08-13 13:30:41.446 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20201010
2025-08-13 13:30:41.447 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 50
2025-08-13 13:30:41.447 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[0]: 工号=20191757, 薪资=1251.00
2025-08-13 13:30:41.447 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[1]: 工号=20181667, 薪资=1251.00
2025-08-13 13:30:41.447 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[2]: 工号=20251009, 薪资=1251.00
2025-08-13 13:30:41.448 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[3]: 工号=20021324, 薪资=1309.00
2025-08-13 13:30:41.448 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[4]: 工号=20201010, 薪资=1339.00
2025-08-13 13:30:41.448 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:479 | 可见行数据顺序: ['20191757', '20181667', '20251009', '20021324', '20201010']
2025-08-13 13:30:41.448 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:442 | 表格数据已设置: 50 行, 24 列
2025-08-13 13:30:41.450 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 50 行, 耗时: 34.0ms
2025-08-13 13:30:41.451 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7922 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-13 13:30:41.451 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7932 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-08-13 13:30:41.451 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7933 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 升序
2025-08-13 13:30:41.457 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: salary_data_2025_08_active_employees (24/24 列)
2025-08-13 13:30:41.458 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-13 13:30:41.458 | INFO     | src.gui.prototype.prototype_main_window:set_data:972 | 📊[pagination-write] 跳过写入当前页数据量(total=50)，保持 total=1396，等待数据事件纠正
2025-08-13 13:30:41.459 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3859 | 🔧 [P1-1修复] 数据已成功设置到UI，使用新映射表头: 50行, 24列
2025-08-13 13:30:41.459 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 24 个, 行号起始 1, 共 50 行
2025-08-13 13:30:41.459 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3875 | 🔧 [P0-新5修复] 表头强制刷新完成: salary_data_2025_08_active_employees, 传递参数: 24个表头
2025-08-13 13:30:41.460 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3879 | 🔧 [统一数据设置] 数据已成功设置到UI: 50行, 24列
2025-08-13 13:30:41.460 | INFO     | src.gui.widgets.pagination_widget:batch_update:472 | 📊[pagination-write] batch_update | src=_on_new_data_updated/initial_load | rid=PG-1755063041380-7958c790 | total: 1396->1396, page: 5->5, size: 50->50, pages: 28->28
2025-08-13 13:30:41.460 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3913 | ✅[pagination-state] now | page=5 / pages=28 / total=1396 | rid=PG-1755063041380-7958c790
2025-08-13 13:30:41.461 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3926 | 🔍 [FINAL修复] 最终调用set_pagination_state: 第5页, 记录201-250 | rid=PG-1755063041380-7958c790
2025-08-13 13:30:41.461 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4166 | 🔍 [表格调试] ================== set_pagination_state 开始 ==================
2025-08-13 13:30:41.461 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4167 | 🔍 [表格调试] 收到分页状态参数: {'current_page': 5, 'page_size': 50, 'total_records': 1396, 'start_record': 201, 'end_record': 250}
2025-08-13 13:30:41.462 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4168 | 🔍 [表格调试] 当前表格行数: 50
2025-08-13 13:30:41.462 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4169 | 🔍 [表格调试] 当前表格列数: 24
2025-08-13 13:30:41.462 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4171 | 🔧 [分页行号修复] 接收分页状态: 第5页, 记录201-250
2025-08-13 13:30:41.463 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: salary_data_2025_08_active_employees (24/24 列)
2025-08-13 13:30:41.463 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4198 | 🔧 [P0-3修复] 分页时已恢复列宽设置: salary_data_2025_08_active_employees
2025-08-13 13:30:41.463 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4206 | 🔧 [分页行号修复] 表格状态: 当前行数=50, 期望行数=50
2025-08-13 13:30:41.464 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 24 个, 行号起始 201, 共 50 行
2025-08-13 13:30:41.464 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_force_update_pagination_row_numbers:4286 | 🔧 [分页行号修复] 强制更新完成: 起始记录201, 共50行
2025-08-13 13:30:41.465 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3930 | 🔍 [FINAL修复] set_pagination_state最终调用完成
2025-08-13 13:30:41.465 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3944 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-08-13 13:30:41.465 | INFO     | src.services.table_data_service:load_table_data:539 | 📨[data_event] published
2025-08-13 13:30:41.465 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4852 | 🚫 [用户要求] 不显示加载条，直接处理数据: salary_data_2025_08_active_employees 第5页
2025-08-13 13:30:41.466 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4855 | 🔧 [根本修复] 分页数据获取成功: 第5页, 50行
2025-08-13 13:30:41.466 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4861 | 🔧 [根本修复] 获取数据的前3个工号: ['无工号']
2025-08-13 13:30:41.466 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4869 | [数据流追踪] 分页数据请求成功: 第5页, 50行
2025-08-13 13:30:41.467 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4878 | 🔧 [立即修复] 分页处理标志已清除，允许UI更新: 第5页
2025-08-13 13:30:41.467 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4883 | 🧹 [智能修复] 数据返回完成，缓存状态: False, 第5页
2025-08-13 13:30:41.467 | INFO     | src.gui.widgets.pagination_widget:batch_update:472 | 📊[pagination-write] batch_update | src=_on_pagination_data_loaded | rid=PL-1755063041467-641f2494 | total: 1396->1396, page: 5->5, size: 50->50, pages: 28->28
2025-08-13 13:30:41.468 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4896 | ✅[pagination-state] now | page=5 / pages=28 / total=1396 | rid=PL-1755063041467-641f2494
2025-08-13 13:30:41.468 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4905 | 🔧 [P0-2修复] 字段映射应用完成: 24列
2025-08-13 13:30:41.469 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4914 | 🔧 [P0-2修复] 分页UI更新成功: 第5页, 50行数据
2025-08-13 13:30:41.469 | INFO     | src.gui.prototype.prototype_main_window:_restore_table_ui_state:10110 | 🔧 [P0-新4修复] 通过StateManager恢复状态成功: salary_data_2025_08_active_employees
2025-08-13 13:30:41.469 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 24 个, 行号起始 201, 共 50 行
2025-08-13 13:30:41.470 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4932 | 🔧 [P0-1修复] 强制刷新表头完成: 24个中文表头, 记录201-250
2025-08-13 13:30:41.470 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4943 | 🔧 [P0-2修复] 分页处理完成，UI已更新: 第5页
2025-08-13 13:30:41.470 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4979 | 🔧 [立即修复] 分页处理标志已原子清除: 第5页
2025-08-13 13:30:41.471 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:5004 | 🔧 [立即修复] 分页处理完成，标志已清除: 第5页
2025-08-13 13:30:41.471 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4740 | 📍[page-change] 入口 | table=salary_data_2025_08_active_employees | target_page=5 | request_id=PG-1755063041471-06499c16
2025-08-13 13:30:41.471 | INFO     | src.core.pagination_state_manager:can_start_pagination:113 | ✅ [允许] 分页请求可以开始: salary_data_2025_08_active_employees, 页5
2025-08-13 13:30:41.472 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4797 | 🔧 [立即修复] 分页事件被去重优化: 第5页
2025-08-13 13:30:41.472 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4979 | 🔧 [立即修复] 分页处理标志已原子清除: 第5页
2025-08-13 13:30:41.472 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:5004 | 🔧 [立即修复] 分页处理完成，标志已清除: 第5页
2025-08-13 13:30:41.491 | INFO     | src.core.pagination_state_manager:start_pagination:152 | 🔧 [开始] 分页操作已开始: salary_data_2025_08_active_employees, 页5, ID=page_1755063041491414_24336
2025-08-13 13:30:41.491 | INFO     | src.gui.prototype.prototype_main_window:_execute_pagination_data_load:5010 | 🔧 [事件优化] 开始执行分页数据加载: 第5页
2025-08-13 13:30:41.492 | INFO     | src.core.request_deduplication_manager:__init__:65 | 请求去重管理器初始化完成，默认TTL: 0.3秒
2025-08-13 13:30:41.492 | INFO     | src.services.table_data_service:get_paginated_data:872 | 🔧 [P1修复] 获取分页数据: salary_data_2025_08_active_employees, 页码: 5, 页大小: 50
2025-08-13 13:30:41.492 | INFO     | src.services.table_data_service:load_table_data:390 | [缓存命中] 使用缓存数据: 第5页
2025-08-13 13:30:41.492 | INFO     | src.services.table_data_service:load_table_data:401 | 📨[data_event] publish(cache) | table=salary_data_2025_08_active_employees | rows=50 | page=5 | size=50 | total=1396 | request_id=SV-1755063041492-3f796cb0-C
2025-08-13 13:30:41.493 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3757 | 📥[data_event] received | table=salary_data_2025_08_active_employees | request_id=SV-1755063041492-3f796cb0-C
2025-08-13 13:30:41.493 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3789 | 数据内容: 50行 x 24列
2025-08-13 13:30:41.493 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3815 | 🔧 [数据更新] 接收到cached_load操作的数据更新事件: salary_data_2025_08_active_employees, 50行
2025-08-13 13:30:41.494 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:6861 | 表 salary_data_2025_08_active_employees 没有用户偏好设置，显示所有可见字段
2025-08-13 13:30:41.495 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3859 | 🔧 [P1-1修复] 数据已成功设置到UI，使用新映射表头: 50行, 24列
2025-08-13 13:30:41.495 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 24 个, 行号起始 1, 共 50 行
2025-08-13 13:30:41.496 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3875 | 🔧 [P0-新5修复] 表头强制刷新完成: salary_data_2025_08_active_employees, 传递参数: 24个表头
2025-08-13 13:30:41.496 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3879 | 🔧 [统一数据设置] 数据已成功设置到UI: 50行, 24列
2025-08-13 13:30:41.496 | INFO     | src.gui.widgets.pagination_widget:batch_update:472 | 📊[pagination-write] batch_update | src=_on_new_data_updated/cached_load | rid=SV-1755063041492-3f796cb0-C | total: 1396->1396, page: 5->5, size: 50->50, pages: 28->28
2025-08-13 13:30:41.497 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3913 | ✅[pagination-state] now | page=5 / pages=28 / total=1396 | rid=SV-1755063041492-3f796cb0-C
2025-08-13 13:30:41.497 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3926 | 🔍 [FINAL修复] 最终调用set_pagination_state: 第5页, 记录201-250 | rid=SV-1755063041492-3f796cb0-C
2025-08-13 13:30:41.497 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4166 | 🔍 [表格调试] ================== set_pagination_state 开始 ==================
2025-08-13 13:30:41.498 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4167 | 🔍 [表格调试] 收到分页状态参数: {'current_page': 5, 'page_size': 50, 'total_records': 1396, 'start_record': 201, 'end_record': 250}
2025-08-13 13:30:41.498 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4168 | 🔍 [表格调试] 当前表格行数: 50
2025-08-13 13:30:41.498 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4169 | 🔍 [表格调试] 当前表格列数: 24
2025-08-13 13:30:41.499 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4171 | 🔧 [分页行号修复] 接收分页状态: 第5页, 记录201-250
2025-08-13 13:30:41.499 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: salary_data_2025_08_active_employees (24/24 列)
2025-08-13 13:30:41.499 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4198 | 🔧 [P0-3修复] 分页时已恢复列宽设置: salary_data_2025_08_active_employees
2025-08-13 13:30:41.500 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4206 | 🔧 [分页行号修复] 表格状态: 当前行数=50, 期望行数=50
2025-08-13 13:30:41.500 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 24 个, 行号起始 201, 共 50 行
2025-08-13 13:30:41.500 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_force_update_pagination_row_numbers:4286 | 🔧 [分页行号修复] 强制更新完成: 起始记录201, 共50行
2025-08-13 13:30:41.501 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3930 | 🔍 [FINAL修复] set_pagination_state最终调用完成
2025-08-13 13:30:41.501 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3944 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-08-13 13:30:41.501 | INFO     | src.services.table_data_service:load_table_data:426 | 📨[data_event] published(cache)
2025-08-13 13:30:41.501 | INFO     | src.services.table_data_service:get_paginated_data:884 | 🔧 [P1修复] 分页数据获取成功: salary_data_2025_08_active_employees, 返回 50 行
2025-08-13 13:30:41.502 | INFO     | src.gui.prototype.prototype_main_window:_execute_pagination_data_load:5050 | 🔧 [事件优化] 分页数据加载成功，UI更新已加入队列: 第5页
2025-08-13 13:30:41.502 | INFO     | src.core.pagination_state_manager:complete_pagination:189 | 🔧 [P1修复] 分页操作已完成: salary_data_2025_08_active_employees, 页5, 成功=True, 耗时=0.012s
2025-08-13 13:30:41.502 | INFO     | src.core.pagination_event_optimizer:process_events:167 | 🔧 [事件处理] 批量处理完成: 1个事件
2025-08-13 13:30:41.564 | INFO     | src.gui.prototype.prototype_main_window:_restore_table_ui_state:10110 | 🔧 [P0-新4修复] 通过StateManager恢复状态成功: salary_data_2025_08_active_employees
2025-08-13 13:30:41.564 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7957 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: salary_data_2025_08_active_employees
2025-08-13 13:30:41.603 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 50行，表名: None
2025-08-13 13:30:41.604 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=20191757, 薪资=1251.00
2025-08-13 13:30:41.604 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=20181667, 薪资=1251.00
2025-08-13 13:30:41.604 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:5271 | 最大可见行数已更新: 50 -> 50
2025-08-13 13:30:41.605 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5324 | 根据数据量(50)自动调整最大可见行数为: 50
2025-08-13 13:30:41.606 | INFO     | src.modules.format_management.format_renderer:render_dataframe:107 | 🎯 [格式渲染] 隐藏字段配置: ['sequence', 'id', 'row_number', 'created_at', 'updated_at']
2025-08-13 13:30:41.606 | INFO     | src.modules.format_management.format_renderer:render_dataframe:115 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-08-13 13:30:41.613 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-08-13 13:30:41.614 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-08-13 13:30:41.615 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 月份 -> 英文名: month -> 类型: month_string
2025-08-13 13:30:41.616 | INFO     | src.modules.format_management.field_registry:get_display_fields:1551 | 🔧 [P0-2修复] display_order转换完成: 24个字段，原始字段数: 24
2025-08-13 13:30:41.616 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | 🔧 [DEBUG] table_type=active_employees, display_fields=24个字段
2025-08-13 13:30:41.616 | INFO     | src.modules.format_management.format_renderer:render_dataframe:210 | 🔧 [P1修复] 字段匹配完成: 成功匹配 24/24 个字段
2025-08-13 13:30:41.617 | INFO     | src.modules.format_management.format_renderer:render_dataframe:214 | 🎯 [格式渲染] 已按display_order排列字段: 24个字段
2025-08-13 13:30:41.618 | INFO     | src.modules.format_management.format_renderer:render_dataframe:234 | 🎨 [格式渲染] DataFrame格式化完成: active_employees, 行数: 50, 列数: 24
2025-08-13 13:30:41.618 | INFO     | src.modules.format_management.unified_format_manager:format_data:367 | 🎯 [统一格式管理] 数据格式化完成: active_employees, 行数: 50, 列数: 24
2025-08-13 13:30:41.626 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 50行数据
2025-08-13 13:30:41.626 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:52 | [数据流追踪] 开始小数据集渲染: 50行 x 24列
2025-08-13 13:30:41.631 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:112 | [数据流追踪] 小数据集渲染完成: 耗时5.0ms, 平均每行0.10ms
2025-08-13 13:30:41.631 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=50, 渲染时间=5.0ms, 策略=small_dataset
2025-08-13 13:30:41.631 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:279 | [数据流追踪] 选择小数据集渲染策略
2025-08-13 13:30:41.632 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 20191757
2025-08-13 13:30:41.633 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20181667
2025-08-13 13:30:41.633 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20251009
2025-08-13 13:30:41.633 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20021324
2025-08-13 13:30:41.633 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20201010
2025-08-13 13:30:41.634 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 50
2025-08-13 13:30:41.634 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[0]: 工号=20191757, 薪资=1251.00
2025-08-13 13:30:41.634 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[1]: 工号=20181667, 薪资=1251.00
2025-08-13 13:30:41.635 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[2]: 工号=20251009, 薪资=1251.00
2025-08-13 13:30:41.635 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[3]: 工号=20021324, 薪资=1309.00
2025-08-13 13:30:41.635 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[4]: 工号=20201010, 薪资=1339.00
2025-08-13 13:30:41.636 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:479 | 可见行数据顺序: ['20191757', '20181667', '20251009', '20021324', '20201010']
2025-08-13 13:30:41.637 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 50 行, 耗时: 32.5ms
2025-08-13 13:30:41.637 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3092 | 🔧 [重影修复] 数据设置完成，行号已由HeaderUpdateManager统一管理
2025-08-13 13:30:41.637 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7922 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-13 13:30:41.637 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7932 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-08-13 13:30:41.638 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7933 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 升序
2025-08-13 13:30:41.638 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: salary_data_2025_08_active_employees (24/24 列)
2025-08-13 13:30:41.639 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3157 | 🔧 [重影修复] 行号已由HeaderUpdateManager统一管理
2025-08-13 13:30:41.639 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-13 13:30:41.639 | INFO     | src.gui.prototype.prototype_main_window:_restore_table_ui_state:10110 | 🔧 [P0-新4修复] 通过StateManager恢复状态成功: salary_data_2025_08_active_employees
2025-08-13 13:30:41.639 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 24 个, 行号起始 201, 共 50 行
2025-08-13 13:30:41.639 | INFO     | src.gui.prototype.prototype_main_window:_update_pagination_ui:5103 | 🔧 [事件优化] UI更新完成: 第5页，50条记录
2025-08-13 13:30:41.640 | INFO     | src.core.pagination_event_optimizer:process_events:167 | 🔧 [事件处理] 批量处理完成: 1个事件
2025-08-13 13:30:41.686 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: salary_data_2025_08_active_employees (24/24 列)
2025-08-13 13:30:41.686 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_adjust_column_widths_delayed_with_restore:4730 | 🔧 [P0-3修复] 用户列宽设置已恢复: salary_data_2025_08_active_employees
2025-08-13 13:30:41.687 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_adjust_column_widths_delayed_with_restore:4752 | 🔧 [列宽修复] 使用用户保存的列宽，跳过默认调整
2025-08-13 13:30:41.740 | INFO     | src.gui.prototype.prototype_main_window:_restore_table_ui_state:10110 | 🔧 [P0-新4修复] 通过StateManager恢复状态成功: salary_data_2025_08_active_employees
2025-08-13 13:30:41.740 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7957 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: salary_data_2025_08_active_employees
2025-08-13 13:30:45.972 | INFO     | src.gui.prototype.prototype_main_window:_on_refresh_data:654 | 🔧[scope=global] 刷新数据功能被触发 | request_id=GR-1755063045972-37e57ad5
2025-08-13 13:30:45.973 | INFO     | src.gui.prototype.prototype_main_window:_on_refresh_data:8233 | 🔧 [P1-3] 全局状态刷新被触发，开始综合性系统刷新。
2025-08-13 13:30:45.973 | INFO     | src.gui.prototype.prototype_main_window:_execute_global_state_refresh:8252 | 🔧 [P1-3] 开始执行全局状态刷新流程
2025-08-13 13:30:45.975 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1021 | 🔧 [P1修复] 找到 4 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-13 13:30:45.975 | INFO     | src.gui.prototype.prototype_main_window:_refresh_core_components:8333 | 🔧 [P1-3] 开始刷新核心组件状态
2025-08-13 13:30:45.976 | INFO     | src.gui.table_header_manager:force_comprehensive_cleanup:944 | 🔧 [P1-1] 开始强制全面清理 2 个表格
2025-08-13 13:30:45.976 | INFO     | src.gui.table_header_manager:_remove_deleted_table_reference:561 | 🔧 [P0-1] 已移除已删除表格的引用: table_0_3203488081392
2025-08-13 13:30:45.976 | INFO     | src.gui.table_header_manager:_remove_deleted_table_reference:561 | 🔧 [P0-1] 已移除已删除表格的引用: table_1_3203487968576
2025-08-13 13:30:45.977 | INFO     | src.gui.table_header_manager:force_comprehensive_cleanup:1005 | 🔧 [P1-1] 强制全面清理完成，成功率: 100.0% (2/2)
2025-08-13 13:30:45.977 | INFO     | src.gui.prototype.prototype_main_window:_refresh_core_components:8341 | 🔧 [P1-3] 表头重影清理完成: {'total_tables': 2, 'cleaned_tables': ['table_0_3203488081392(已清理无效引用)', 'table_1_3203487968576(已清理无效引用)'], 'failed_tables': [], 'total_shadows_found': 0, 'total_shadows_fixed': 0}
2025-08-13 13:30:45.978 | INFO     | src.gui.prototype.prototype_main_window:_refresh_core_components:8364 | 🔧 [P1-3] 核心组件状态刷新完成
2025-08-13 13:30:45.978 | INFO     | src.gui.prototype.prototype_main_window:_refresh_data_layer:8378 | 🔧 [P1-3] 开始刷新数据层
2025-08-13 13:30:45.978 | INFO     | src.services.table_data_service:load_table_data:366 | 加载表格数据: 页码: 5
2025-08-13 13:30:45.979 | INFO     | src.core.unified_data_request_manager:request_table_data:212 | 开始处理数据请求
2025-08-13 13:30:45.979 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:779 | [FIX] [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_08_active_employees' 中（类型: REAL）
2025-08-13 13:30:45.980 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:779 | [FIX] [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_08_active_employees' 中（类型: REAL）
2025-08-13 13:30:45.980 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:590 | 正在从表 salary_data_2025_08_active_employees 分页获取数据（支持排序）: 第5页, 每页50条, 排序=1列
2025-08-13 13:30:45.981 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:779 | [FIX] [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_08_active_employees' 中（类型: REAL）
2025-08-13 13:30:45.984 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:706 | 成功从表 salary_data_2025_08_active_employees 获取第5页数据（含排序）: 50 行，总计1396行
2025-08-13 13:30:45.985 | INFO     | src.core.unified_data_request_manager:request_table_data:262 | 数据请求处理完成: 字段=28, 行数=50, 耗时=6.1ms
2025-08-13 13:30:45.985 | INFO     | src.services.table_data_service:load_table_data:462 | [新架构] 使用统一格式管理器（单例优化）
2025-08-13 13:30:45.986 | INFO     | src.modules.format_management.format_renderer:render_dataframe:107 | 🎯 [格式渲染] 隐藏字段配置: ['sequence', 'id', 'row_number', 'created_at', 'updated_at']
2025-08-13 13:30:45.986 | INFO     | src.modules.format_management.format_renderer:render_dataframe:115 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-08-13 13:30:45.994 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-08-13 13:30:45.995 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-08-13 13:30:45.997 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 月份 -> 英文名: month -> 类型: month_string
2025-08-13 13:30:45.999 | INFO     | src.modules.format_management.field_registry:get_display_fields:1551 | 🔧 [P0-2修复] display_order转换完成: 24个字段，原始字段数: 24
2025-08-13 13:30:45.999 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | 🔧 [DEBUG] table_type=active_employees, display_fields=24个字段
2025-08-13 13:30:46.000 | INFO     | src.modules.format_management.format_renderer:render_dataframe:210 | 🔧 [P1修复] 字段匹配完成: 成功匹配 24/24 个字段
2025-08-13 13:30:46.001 | INFO     | src.modules.format_management.format_renderer:render_dataframe:214 | 🎯 [格式渲染] 已按display_order排列字段: 24个字段
2025-08-13 13:30:46.001 | INFO     | src.modules.format_management.format_renderer:render_dataframe:234 | 🎨 [格式渲染] DataFrame格式化完成: active_employees, 行数: 50, 列数: 24
2025-08-13 13:30:46.002 | INFO     | src.modules.format_management.unified_format_manager:format_data:367 | 🎯 [统一格式管理] 数据格式化完成: active_employees, 行数: 50, 列数: 24
2025-08-13 13:30:46.003 | INFO     | src.core.unified_state_manager:update_table_state:232 | 表状态已更新: salary_data_2025_08_active_employees, 变更类型: None
2025-08-13 13:30:46.004 | INFO     | src.services.table_data_service:load_table_data:512 | [修复数据发布] 数据加载成功，发布更新事件: 50行
2025-08-13 13:30:46.004 | INFO     | src.services.table_data_service:load_table_data:516 | 📨[data_event] publish | table=salary_data_2025_08_active_employees | rows=50 | page=5 | size=50 | total=1396 | req_type=RequestType.INITIAL_LOAD | request_id=SV-1755063046004-82d9ede8
2025-08-13 13:30:46.005 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3757 | 📥[data_event] received | table=salary_data_2025_08_active_employees | request_id=SV-1755063046004-82d9ede8
2025-08-13 13:30:46.005 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3789 | 数据内容: 50行 x 24列
2025-08-13 13:30:46.006 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3815 | 🔧 [数据更新] 接收到initial_load操作的数据更新事件: salary_data_2025_08_active_employees, 50行
2025-08-13 13:30:46.007 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:6861 | 表 salary_data_2025_08_active_employees 没有用户偏好设置，显示所有可见字段
2025-08-13 13:30:46.008 | INFO     | src.gui.prototype.prototype_main_window:set_data:848 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-08-13 13:30:46.010 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:6861 | 表 salary_data_2025_08_active_employees 没有用户偏好设置，显示所有可见字段
2025-08-13 13:30:46.012 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 50行，表名: salary_data_2025_08_active_employees
2025-08-13 13:30:46.014 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=20191757, 薪资=1251.00
2025-08-13 13:30:46.015 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=20181667, 薪资=1251.00
2025-08-13 13:30:46.015 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:5271 | 最大可见行数已更新: 50 -> 50
2025-08-13 13:30:46.015 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5324 | 根据数据量(50)自动调整最大可见行数为: 50
2025-08-13 13:30:46.018 | INFO     | src.modules.format_management.format_renderer:render_dataframe:107 | 🎯 [格式渲染] 隐藏字段配置: ['sequence', 'id', 'row_number', 'created_at', 'updated_at']
2025-08-13 13:30:46.018 | INFO     | src.modules.format_management.format_renderer:render_dataframe:115 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-08-13 13:30:46.027 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-08-13 13:30:46.028 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-08-13 13:30:46.029 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 月份 -> 英文名: month -> 类型: month_string
2025-08-13 13:30:46.030 | INFO     | src.modules.format_management.field_registry:get_display_fields:1551 | 🔧 [P0-2修复] display_order转换完成: 24个字段，原始字段数: 24
2025-08-13 13:30:46.030 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | 🔧 [DEBUG] table_type=active_employees, display_fields=24个字段
2025-08-13 13:30:46.030 | INFO     | src.modules.format_management.format_renderer:render_dataframe:210 | 🔧 [P1修复] 字段匹配完成: 成功匹配 24/24 个字段
2025-08-13 13:30:46.031 | INFO     | src.modules.format_management.format_renderer:render_dataframe:214 | 🎯 [格式渲染] 已按display_order排列字段: 24个字段
2025-08-13 13:30:46.032 | INFO     | src.modules.format_management.format_renderer:render_dataframe:234 | 🎨 [格式渲染] DataFrame格式化完成: active_employees, 行数: 50, 列数: 24
2025-08-13 13:30:46.032 | INFO     | src.modules.format_management.unified_format_manager:format_data:367 | 🎯 [统一格式管理] 数据格式化完成: active_employees, 行数: 50, 列数: 24
2025-08-13 13:30:46.040 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 50行数据
2025-08-13 13:30:46.041 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:52 | [数据流追踪] 开始小数据集渲染: 50行 x 24列
2025-08-13 13:30:46.045 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:112 | [数据流追踪] 小数据集渲染完成: 耗时4.0ms, 平均每行0.08ms
2025-08-13 13:30:46.045 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=50, 渲染时间=4.0ms, 策略=small_dataset
2025-08-13 13:30:46.045 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:279 | [数据流追踪] 选择小数据集渲染策略
2025-08-13 13:30:46.046 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 20191757
2025-08-13 13:30:46.046 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20181667
2025-08-13 13:30:46.046 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20251009
2025-08-13 13:30:46.047 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20021324
2025-08-13 13:30:46.047 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20201010
2025-08-13 13:30:46.047 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 50
2025-08-13 13:30:46.048 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[0]: 工号=20191757, 薪资=1251.00
2025-08-13 13:30:46.048 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[1]: 工号=20181667, 薪资=1251.00
2025-08-13 13:30:46.048 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[2]: 工号=20251009, 薪资=1251.00
2025-08-13 13:30:46.048 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[3]: 工号=20021324, 薪资=1309.00
2025-08-13 13:30:46.049 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[4]: 工号=20201010, 薪资=1339.00
2025-08-13 13:30:46.049 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:479 | 可见行数据顺序: ['20191757', '20181667', '20251009', '20021324', '20201010']
2025-08-13 13:30:46.049 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:442 | 表格数据已设置: 50 行, 24 列
2025-08-13 13:30:46.051 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 50 行, 耗时: 37.2ms
2025-08-13 13:30:46.052 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7922 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-13 13:30:46.052 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7932 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-08-13 13:30:46.052 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7933 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 升序
2025-08-13 13:30:46.053 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: salary_data_2025_08_active_employees (24/24 列)
2025-08-13 13:30:46.053 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-13 13:30:46.054 | INFO     | src.gui.prototype.prototype_main_window:set_data:972 | 📊[pagination-write] 跳过写入当前页数据量(total=50)，保持 total=1396，等待数据事件纠正
2025-08-13 13:30:46.054 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3859 | 🔧 [P1-1修复] 数据已成功设置到UI，使用新映射表头: 50行, 24列
2025-08-13 13:30:46.054 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 24 个, 行号起始 1, 共 50 行
2025-08-13 13:30:46.055 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3875 | 🔧 [P0-新5修复] 表头强制刷新完成: salary_data_2025_08_active_employees, 传递参数: 24个表头
2025-08-13 13:30:46.055 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3879 | 🔧 [统一数据设置] 数据已成功设置到UI: 50行, 24列
2025-08-13 13:30:46.055 | INFO     | src.gui.widgets.pagination_widget:batch_update:472 | 📊[pagination-write] batch_update | src=_on_new_data_updated/initial_load | rid=SV-1755063046004-82d9ede8 | total: 1396->1396, page: 5->5, size: 50->50, pages: 28->28
2025-08-13 13:30:46.056 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3913 | ✅[pagination-state] now | page=5 / pages=28 / total=1396 | rid=SV-1755063046004-82d9ede8
2025-08-13 13:30:46.056 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3926 | 🔍 [FINAL修复] 最终调用set_pagination_state: 第5页, 记录201-250 | rid=SV-1755063046004-82d9ede8
2025-08-13 13:30:46.056 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4166 | 🔍 [表格调试] ================== set_pagination_state 开始 ==================
2025-08-13 13:30:46.057 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4167 | 🔍 [表格调试] 收到分页状态参数: {'current_page': 5, 'page_size': 50, 'total_records': 1396, 'start_record': 201, 'end_record': 250}
2025-08-13 13:30:46.057 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4168 | 🔍 [表格调试] 当前表格行数: 50
2025-08-13 13:30:46.057 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4169 | 🔍 [表格调试] 当前表格列数: 24
2025-08-13 13:30:46.057 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4171 | 🔧 [分页行号修复] 接收分页状态: 第5页, 记录201-250
2025-08-13 13:30:46.058 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: salary_data_2025_08_active_employees (24/24 列)
2025-08-13 13:30:46.058 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4198 | 🔧 [P0-3修复] 分页时已恢复列宽设置: salary_data_2025_08_active_employees
2025-08-13 13:30:46.058 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4206 | 🔧 [分页行号修复] 表格状态: 当前行数=50, 期望行数=50
2025-08-13 13:30:46.059 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 24 个, 行号起始 201, 共 50 行
2025-08-13 13:30:46.059 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_force_update_pagination_row_numbers:4286 | 🔧 [分页行号修复] 强制更新完成: 起始记录201, 共50行
2025-08-13 13:30:46.060 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3930 | 🔍 [FINAL修复] set_pagination_state最终调用完成
2025-08-13 13:30:46.060 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3944 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-08-13 13:30:46.060 | INFO     | src.services.table_data_service:load_table_data:539 | 📨[data_event] published
2025-08-13 13:30:46.060 | INFO     | src.gui.prototype.prototype_main_window:_refresh_data_layer:8387 | 🔧 [P1-3] 表格数据刷新成功: salary_data_2025_08_active_employees
2025-08-13 13:30:46.061 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1786 | 开始强制刷新工资数据导航... (尝试 1/3)
2025-08-13 13:30:46.061 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1794 | 找到工资表节点: 💰 工资表
2025-08-13 13:30:46.062 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年 > 8月 > A岗职工', '工资表 > 2025年', '工资表 > 2025年 > 8月', '工资表 > 2025年 > 8月 > 全部在职人员', '工资表 > 2025年 > 8月 > 退休人员']
2025-08-13 13:30:46.063 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:6904 | 导航变化: 工资表 > 2025年 > 8月 > 全部在职人员
2025-08-13 13:30:46.063 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:9957 | 🚫 [用户要求] 表格切换不显示加载条: salary_data_2025_08_active_employees -> None
2025-08-13 13:30:46.063 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:9973 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-08-13 13:30:46.064 | INFO     | src.gui.prototype.prototype_main_window:clear_field_processing_cache:6782 | 🔧 [缓存清理] 清理了所有 0 个字段处理缓存条目
2025-08-13 13:30:46.064 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:7778 | 🔧 [表名生成] 从完整路径生成表名: ['工资表', '2025年', '8月', '全部在职人员'] -> salary_data_2025_08_active_employees
2025-08-13 13:30:46.064 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-13 13:30:46.065 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:362 | 已清理表 salary_data_2025_08_active_employees 的缓存
2025-08-13 13:30:46.065 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:10643 | 已注册 2 个表格到表头管理器
2025-08-13 13:30:46.065 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1188 | 🔧 [P1-3] 开始智能表头重影检测，共 2 个表格
2025-08-13 13:30:46.066 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1261 | 🔧 [P1-3] 智能表头重影检测完成，耗时 1.01ms
2025-08-13 13:30:46.066 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1262 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-08-13 13:30:46.066 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7120 | 🆕 使用新架构加载数据: salary_data_2025_08_active_employees（通过事件系统）
2025-08-13 13:30:46.067 | INFO     | src.services.table_data_service:load_table_data:390 | [缓存命中] 使用缓存数据: 第1页
2025-08-13 13:30:46.067 | INFO     | src.services.table_data_service:load_table_data:401 | 📨[data_event] publish(cache) | table=salary_data_2025_08_active_employees | rows=50 | page=1 | size=50 | total=1396 | request_id=SV-1755063046067-8537ac8b-C
2025-08-13 13:30:46.067 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3757 | 📥[data_event] received | table=salary_data_2025_08_active_employees | request_id=SV-1755063046067-8537ac8b-C
2025-08-13 13:30:46.068 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3789 | 数据内容: 50行 x 24列
2025-08-13 13:30:46.068 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3815 | 🔧 [数据更新] 接收到cached_load操作的数据更新事件: salary_data_2025_08_active_employees, 50行
2025-08-13 13:30:46.069 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:6861 | 表 salary_data_2025_08_active_employees 没有用户偏好设置，显示所有可见字段
2025-08-13 13:30:46.070 | INFO     | src.gui.prototype.prototype_main_window:set_data:848 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-08-13 13:30:46.072 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:6861 | 表 salary_data_2025_08_active_employees 没有用户偏好设置，显示所有可见字段
2025-08-13 13:30:46.073 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 50行，表名: salary_data_2025_08_active_employees
2025-08-13 13:30:46.074 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=19990089, 薪资=2375.00
2025-08-13 13:30:46.075 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=20161565, 薪资=1696.00
2025-08-13 13:30:46.075 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:5271 | 最大可见行数已更新: 50 -> 50
2025-08-13 13:30:46.075 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5324 | 根据数据量(50)自动调整最大可见行数为: 50
2025-08-13 13:30:46.077 | INFO     | src.modules.format_management.format_renderer:render_dataframe:107 | 🎯 [格式渲染] 隐藏字段配置: ['sequence', 'id', 'row_number', 'created_at', 'updated_at']
2025-08-13 13:30:46.077 | INFO     | src.modules.format_management.format_renderer:render_dataframe:115 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-08-13 13:30:46.083 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-08-13 13:30:46.084 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-08-13 13:30:46.085 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 月份 -> 英文名: month -> 类型: month_string
2025-08-13 13:30:46.086 | INFO     | src.modules.format_management.field_registry:get_display_fields:1551 | 🔧 [P0-2修复] display_order转换完成: 24个字段，原始字段数: 24
2025-08-13 13:30:46.086 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | 🔧 [DEBUG] table_type=active_employees, display_fields=24个字段
2025-08-13 13:30:46.086 | INFO     | src.modules.format_management.format_renderer:render_dataframe:210 | 🔧 [P1修复] 字段匹配完成: 成功匹配 24/24 个字段
2025-08-13 13:30:46.087 | INFO     | src.modules.format_management.format_renderer:render_dataframe:214 | 🎯 [格式渲染] 已按display_order排列字段: 24个字段
2025-08-13 13:30:46.087 | INFO     | src.modules.format_management.format_renderer:render_dataframe:234 | 🎨 [格式渲染] DataFrame格式化完成: active_employees, 行数: 50, 列数: 24
2025-08-13 13:30:46.088 | INFO     | src.modules.format_management.unified_format_manager:format_data:367 | 🎯 [统一格式管理] 数据格式化完成: active_employees, 行数: 50, 列数: 24
2025-08-13 13:30:46.096 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 50行数据
2025-08-13 13:30:46.096 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:52 | [数据流追踪] 开始小数据集渲染: 50行 x 24列
2025-08-13 13:30:46.101 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:112 | [数据流追踪] 小数据集渲染完成: 耗时5.0ms, 平均每行0.10ms
2025-08-13 13:30:46.101 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=50, 渲染时间=5.0ms, 策略=small_dataset
2025-08-13 13:30:46.102 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:279 | [数据流追踪] 选择小数据集渲染策略
2025-08-13 13:30:46.102 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-08-13 13:30:46.102 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-08-13 13:30:46.102 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-08-13 13:30:46.103 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-08-13 13:30:46.103 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-08-13 13:30:46.103 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 50
2025-08-13 13:30:46.104 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[0]: 工号=19990089, 薪资=2375.00
2025-08-13 13:30:46.104 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[1]: 工号=20161565, 薪资=1696.00
2025-08-13 13:30:46.104 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[2]: 工号=20191782, 薪资=1427.00
2025-08-13 13:30:46.105 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[3]: 工号=20151515, 薪资=2175.00
2025-08-13 13:30:46.105 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[4]: 工号=20181640, 薪资=2582.00
2025-08-13 13:30:46.105 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:479 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-08-13 13:30:46.106 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 50 行, 耗时: 33.5ms
2025-08-13 13:30:46.106 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7922 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-13 13:30:46.106 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7932 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-08-13 13:30:46.107 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7933 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 升序
2025-08-13 13:30:46.108 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: salary_data_2025_08_active_employees (24/24 列)
2025-08-13 13:30:46.108 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-13 13:30:46.109 | INFO     | src.gui.prototype.prototype_main_window:set_data:972 | 📊[pagination-write] 跳过写入当前页数据量(total=50)，保持 total=0，等待数据事件纠正
2025-08-13 13:30:46.109 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3859 | 🔧 [P1-1修复] 数据已成功设置到UI，使用新映射表头: 50行, 24列
2025-08-13 13:30:46.110 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 24 个, 行号起始 1, 共 50 行
2025-08-13 13:30:46.110 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3875 | 🔧 [P0-新5修复] 表头强制刷新完成: salary_data_2025_08_active_employees, 传递参数: 24个表头
2025-08-13 13:30:46.110 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3879 | 🔧 [统一数据设置] 数据已成功设置到UI: 50行, 24列
2025-08-13 13:30:46.111 | INFO     | src.gui.widgets.pagination_widget:batch_update:472 | 📊[pagination-write] batch_update | src=_on_new_data_updated/cached_load | rid=SV-1755063046067-8537ac8b-C | total: 0->1396, page: 1->1, size: 50->50, pages: 1->28
2025-08-13 13:30:46.111 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3913 | ✅[pagination-state] now | page=1 / pages=28 / total=1396 | rid=SV-1755063046067-8537ac8b-C
2025-08-13 13:30:46.111 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3926 | 🔍 [FINAL修复] 最终调用set_pagination_state: 第1页, 记录1-50 | rid=SV-1755063046067-8537ac8b-C
2025-08-13 13:30:46.112 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4166 | 🔍 [表格调试] ================== set_pagination_state 开始 ==================
2025-08-13 13:30:46.112 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4167 | 🔍 [表格调试] 收到分页状态参数: {'current_page': 1, 'page_size': 50, 'total_records': 1396, 'start_record': 1, 'end_record': 50}
2025-08-13 13:30:46.112 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4168 | 🔍 [表格调试] 当前表格行数: 50
2025-08-13 13:30:46.112 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4169 | 🔍 [表格调试] 当前表格列数: 24
2025-08-13 13:30:46.113 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4171 | 🔧 [分页行号修复] 接收分页状态: 第1页, 记录1-50
2025-08-13 13:30:46.113 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: salary_data_2025_08_active_employees (24/24 列)
2025-08-13 13:30:46.114 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4198 | 🔧 [P0-3修复] 分页时已恢复列宽设置: salary_data_2025_08_active_employees
2025-08-13 13:30:46.114 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4206 | 🔧 [分页行号修复] 表格状态: 当前行数=50, 期望行数=50
2025-08-13 13:30:46.114 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 24 个, 行号起始 1, 共 50 行
2025-08-13 13:30:46.115 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_force_update_pagination_row_numbers:4286 | 🔧 [分页行号修复] 强制更新完成: 起始记录1, 共50行
2025-08-13 13:30:46.115 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3930 | 🔍 [FINAL修复] set_pagination_state最终调用完成
2025-08-13 13:30:46.134 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:379 | 已清理所有缓存
2025-08-13 13:30:46.135 | INFO     | src.gui.prototype.prototype_main_window:_validate_component_state_consistency:10451 | ✅ [新架构] 组件状态一致性验证通过
2025-08-13 13:30:46.135 | INFO     | src.gui.prototype.prototype_main_window:complete_navigation_transition:10414 | 🆕 [新架构] 导航迁移完成
2025-08-13 13:30:46.135 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3944 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-08-13 13:30:46.136 | INFO     | src.services.table_data_service:load_table_data:426 | 📨[data_event] published(cache)
2025-08-13 13:30:46.136 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7125 | 🆕 新架构数据加载请求已发送，等待事件系统处理
2025-08-13 13:30:46.137 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1021 | 🔧 [P1修复] 找到 4 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-13 13:30:46.138 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1883 | 创建年份节点: 2025年，包含 1 个月份
2025-08-13 13:30:46.139 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_navigation_state:1695 | 🔧 [P2-2] 开始恢复导航状态...
2025-08-13 13:30:46.141 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年 > 8月 > A岗职工', '工资表 > 2025年', '工资表 > 2025年 > 8月', '工资表 > 2025年 > 8月 > 全部在职人员', '工资表 > 2025年 > 8月 > 退休人员']
2025-08-13 13:30:46.142 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_navigation_state:1726 | 🔧 [P2-2] 导航状态恢复完成: 3个展开项
2025-08-13 13:30:46.142 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:2022 | force_refresh_salary_data 执行完成
2025-08-13 13:30:46.143 | INFO     | src.gui.prototype.prototype_main_window:_refresh_data_layer:8401 | 🔧 [P1-3] 导航面板数据已刷新
2025-08-13 13:30:46.143 | INFO     | src.gui.prototype.prototype_main_window:_refresh_data_layer:8412 | 🔧 [P1-3] 数据层刷新完成
2025-08-13 13:30:46.143 | INFO     | src.gui.prototype.prototype_main_window:_refresh_ui_components:8426 | 🔧 [P1-3] 开始刷新UI组件状态
2025-08-13 13:30:46.144 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:refresh_navigation_state:1024 | 🔧 [P2-2] 开始刷新导航面板状态
2025-08-13 13:30:46.144 | INFO     | src.gui.prototype.widgets.smart_search_debounce:reset_statistics:657 | 统计信息已重置
2025-08-13 13:30:46.144 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:refresh_navigation_state:1058 | 🔧 [P2-2] 导航面板状态刷新完成
2025-08-13 13:30:46.144 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:sync_with_global_state:1071 | 🔧 [P2-2] 开始与全局状态同步
2025-08-13 13:30:46.145 | INFO     | src.gui.widgets.pagination_widget:refresh_pagination_state:594 | 🔧 [P2-1] 开始刷新分页组件状态
2025-08-13 13:30:46.145 | INFO     | src.gui.prototype.prototype_main_window:_refresh_ui_components:8492 | 🔧 [P1-3] UI组件状态刷新完成
2025-08-13 13:30:46.146 | INFO     | src.gui.prototype.prototype_main_window:_refresh_display_state:8506 | 🔧 [P1-3] 开始刷新显示状态
2025-08-13 13:30:46.197 | INFO     | src.gui.prototype.prototype_main_window:_force_global_redraw:8710 | 🔧 [P2-重影修复] 执行表头重影清理...
2025-08-13 13:30:46.197 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1786 | 开始强制刷新工资数据导航... (尝试 1/3)
2025-08-13 13:30:46.198 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1794 | 找到工资表节点: 💰 工资表
2025-08-13 13:30:46.199 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年 > 8月 > A岗职工', '工资表 > 2025年', '工资表 > 2025年 > 8月', '工资表 > 2025年 > 8月 > 全部在职人员', '工资表 > 2025年 > 8月 > 退休人员']
2025-08-13 13:30:46.200 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1021 | 🔧 [P1修复] 找到 4 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-13 13:30:46.201 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_navigation_state:1695 | 🔧 [P2-2] 开始恢复导航状态...
2025-08-13 13:30:46.204 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年 > 8月 > A岗职工', '工资表 > 2025年', '工资表 > 2025年 > 8月 > 全部在职人员', '工资表 > 2025年 > 8月', '工资表 > 2025年 > 8月 > 退休人员']
2025-08-13 13:30:46.204 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_navigation_state:1726 | 🔧 [P2-2] 导航状态恢复完成: 4个展开项
2025-08-13 13:30:46.205 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:2022 | force_refresh_salary_data 执行完成
2025-08-13 13:30:46.205 | INFO     | src.gui.prototype.prototype_main_window:_force_global_redraw:8725 | 🔧 [组件检查] 导航面板数据已刷新
2025-08-13 13:30:46.205 | INFO     | src.gui.prototype.prototype_main_window:_force_global_redraw:8730 | 🔧 [P2-重影修复] 使用新架构刷新表格数据: salary_data_2025_08_active_employees
2025-08-13 13:30:46.206 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:779 | [FIX] [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_08_active_employees' 中（类型: REAL）
2025-08-13 13:30:46.206 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:779 | [FIX] [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_08_active_employees' 中（类型: REAL）
2025-08-13 13:30:46.206 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:590 | 正在从表 salary_data_2025_08_active_employees 分页获取数据（支持排序）: 第5页, 每页50条, 排序=1列
2025-08-13 13:30:46.207 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:779 | [FIX] [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_08_active_employees' 中（类型: REAL）
2025-08-13 13:30:46.209 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:706 | 成功从表 salary_data_2025_08_active_employees 获取第5页数据（含排序）: 50 行，总计1396行
2025-08-13 13:30:46.210 | INFO     | src.modules.format_management.format_renderer:render_dataframe:107 | 🎯 [格式渲染] 隐藏字段配置: ['sequence', 'id', 'row_number', 'created_at', 'updated_at']
2025-08-13 13:30:46.210 | INFO     | src.modules.format_management.format_renderer:render_dataframe:115 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-08-13 13:30:46.215 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-08-13 13:30:46.216 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-08-13 13:30:46.217 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 月份 -> 英文名: month -> 类型: month_string
2025-08-13 13:30:46.219 | INFO     | src.modules.format_management.field_registry:get_display_fields:1551 | 🔧 [P0-2修复] display_order转换完成: 24个字段，原始字段数: 24
2025-08-13 13:30:46.219 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | 🔧 [DEBUG] table_type=active_employees, display_fields=24个字段
2025-08-13 13:30:46.219 | INFO     | src.modules.format_management.format_renderer:render_dataframe:210 | 🔧 [P1修复] 字段匹配完成: 成功匹配 24/24 个字段
2025-08-13 13:30:46.220 | INFO     | src.modules.format_management.format_renderer:render_dataframe:214 | 🎯 [格式渲染] 已按display_order排列字段: 24个字段
2025-08-13 13:30:46.221 | INFO     | src.modules.format_management.format_renderer:render_dataframe:234 | 🎨 [格式渲染] DataFrame格式化完成: active_employees, 行数: 50, 列数: 24
2025-08-13 13:30:46.221 | INFO     | src.modules.format_management.unified_format_manager:format_data:367 | 🎯 [统一格式管理] 数据格式化完成: active_employees, 行数: 50, 列数: 24
2025-08-13 13:30:46.222 | INFO     | src.services.table_data_service:load_table_data:516 | 📨[data_event] publish | table=salary_data_2025_08_active_employees | rows=50 | page=5 | size=50 | total=1396 | req_type=RequestType.INITIAL_LOAD | request_id=SV-1755063046222-bb59fd52
2025-08-13 13:30:46.222 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3757 | 📥[data_event] received | table=salary_data_2025_08_active_employees | request_id=SV-1755063046222-bb59fd52
2025-08-13 13:30:46.223 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3789 | 数据内容: 50行 x 24列
2025-08-13 13:30:46.223 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3815 | 🔧 [数据更新] 接收到initial_load操作的数据更新事件: salary_data_2025_08_active_employees, 50行
2025-08-13 13:30:46.224 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:6861 | 表 salary_data_2025_08_active_employees 没有用户偏好设置，显示所有可见字段
2025-08-13 13:30:46.225 | INFO     | src.gui.prototype.prototype_main_window:set_data:848 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-08-13 13:30:46.226 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:6861 | 表 salary_data_2025_08_active_employees 没有用户偏好设置，显示所有可见字段
2025-08-13 13:30:46.229 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 50行，表名: salary_data_2025_08_active_employees
2025-08-13 13:30:46.231 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=20191757, 薪资=1251.00
2025-08-13 13:30:46.231 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=20181667, 薪资=1251.00
2025-08-13 13:30:46.231 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:5271 | 最大可见行数已更新: 50 -> 50
2025-08-13 13:30:46.231 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5324 | 根据数据量(50)自动调整最大可见行数为: 50
2025-08-13 13:30:46.233 | INFO     | src.modules.format_management.format_renderer:render_dataframe:107 | 🎯 [格式渲染] 隐藏字段配置: ['sequence', 'id', 'row_number', 'created_at', 'updated_at']
2025-08-13 13:30:46.233 | INFO     | src.modules.format_management.format_renderer:render_dataframe:115 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-08-13 13:30:46.240 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-08-13 13:30:46.240 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-08-13 13:30:46.241 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 月份 -> 英文名: month -> 类型: month_string
2025-08-13 13:30:46.242 | INFO     | src.modules.format_management.field_registry:get_display_fields:1551 | 🔧 [P0-2修复] display_order转换完成: 24个字段，原始字段数: 24
2025-08-13 13:30:46.242 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | 🔧 [DEBUG] table_type=active_employees, display_fields=24个字段
2025-08-13 13:30:46.242 | INFO     | src.modules.format_management.format_renderer:render_dataframe:210 | 🔧 [P1修复] 字段匹配完成: 成功匹配 24/24 个字段
2025-08-13 13:30:46.243 | INFO     | src.modules.format_management.format_renderer:render_dataframe:214 | 🎯 [格式渲染] 已按display_order排列字段: 24个字段
2025-08-13 13:30:46.244 | INFO     | src.modules.format_management.format_renderer:render_dataframe:234 | 🎨 [格式渲染] DataFrame格式化完成: active_employees, 行数: 50, 列数: 24
2025-08-13 13:30:46.244 | INFO     | src.modules.format_management.unified_format_manager:format_data:367 | 🎯 [统一格式管理] 数据格式化完成: active_employees, 行数: 50, 列数: 24
2025-08-13 13:30:46.253 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 50行数据
2025-08-13 13:30:46.253 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:52 | [数据流追踪] 开始小数据集渲染: 50行 x 24列
2025-08-13 13:30:46.257 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:112 | [数据流追踪] 小数据集渲染完成: 耗时4.0ms, 平均每行0.08ms
2025-08-13 13:30:46.258 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=50, 渲染时间=4.0ms, 策略=small_dataset
2025-08-13 13:30:46.258 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:279 | [数据流追踪] 选择小数据集渲染策略
2025-08-13 13:30:46.258 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 20191757
2025-08-13 13:30:46.259 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20181667
2025-08-13 13:30:46.259 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20251009
2025-08-13 13:30:46.259 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20021324
2025-08-13 13:30:46.259 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20201010
2025-08-13 13:30:46.260 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 50
2025-08-13 13:30:46.260 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[0]: 工号=20191757, 薪资=1251.00
2025-08-13 13:30:46.260 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[1]: 工号=20181667, 薪资=1251.00
2025-08-13 13:30:46.261 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[2]: 工号=20251009, 薪资=1251.00
2025-08-13 13:30:46.261 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[3]: 工号=20021324, 薪资=1309.00
2025-08-13 13:30:46.261 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[4]: 工号=20201010, 薪资=1339.00
2025-08-13 13:30:46.262 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:479 | 可见行数据顺序: ['20191757', '20181667', '20251009', '20021324', '20201010']
2025-08-13 13:30:46.262 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 50 行, 耗时: 33.0ms
2025-08-13 13:30:46.263 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7922 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-13 13:30:46.263 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7932 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-08-13 13:30:46.263 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7933 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 升序
2025-08-13 13:30:46.264 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: salary_data_2025_08_active_employees (24/24 列)
2025-08-13 13:30:46.264 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-13 13:30:46.265 | INFO     | src.gui.prototype.prototype_main_window:set_data:972 | 📊[pagination-write] 跳过写入当前页数据量(total=50)，保持 total=1396，等待数据事件纠正
2025-08-13 13:30:46.265 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3859 | 🔧 [P1-1修复] 数据已成功设置到UI，使用新映射表头: 50行, 24列
2025-08-13 13:30:46.265 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 24 个, 行号起始 1, 共 50 行
2025-08-13 13:30:46.266 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3875 | 🔧 [P0-新5修复] 表头强制刷新完成: salary_data_2025_08_active_employees, 传递参数: 24个表头
2025-08-13 13:30:46.266 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3879 | 🔧 [统一数据设置] 数据已成功设置到UI: 50行, 24列
2025-08-13 13:30:46.266 | INFO     | src.gui.widgets.pagination_widget:batch_update:472 | 📊[pagination-write] batch_update | src=_on_new_data_updated/initial_load | rid=SV-1755063046222-bb59fd52 | total: 1396->1396, page: 1->5, size: 50->50, pages: 28->28
2025-08-13 13:30:46.267 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3913 | ✅[pagination-state] now | page=5 / pages=28 / total=1396 | rid=SV-1755063046222-bb59fd52
2025-08-13 13:30:46.267 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3926 | 🔍 [FINAL修复] 最终调用set_pagination_state: 第5页, 记录201-250 | rid=SV-1755063046222-bb59fd52
2025-08-13 13:30:46.267 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4166 | 🔍 [表格调试] ================== set_pagination_state 开始 ==================
2025-08-13 13:30:46.267 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4167 | 🔍 [表格调试] 收到分页状态参数: {'current_page': 5, 'page_size': 50, 'total_records': 1396, 'start_record': 201, 'end_record': 250}
2025-08-13 13:30:46.267 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4168 | 🔍 [表格调试] 当前表格行数: 50
2025-08-13 13:30:46.268 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4169 | 🔍 [表格调试] 当前表格列数: 24
2025-08-13 13:30:46.268 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4171 | 🔧 [分页行号修复] 接收分页状态: 第5页, 记录201-250
2025-08-13 13:30:46.268 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: salary_data_2025_08_active_employees (24/24 列)
2025-08-13 13:30:46.269 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4198 | 🔧 [P0-3修复] 分页时已恢复列宽设置: salary_data_2025_08_active_employees
2025-08-13 13:30:46.269 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4206 | 🔧 [分页行号修复] 表格状态: 当前行数=50, 期望行数=50
2025-08-13 13:30:46.269 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 24 个, 行号起始 201, 共 50 行
2025-08-13 13:30:46.270 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_force_update_pagination_row_numbers:4286 | 🔧 [分页行号修复] 强制更新完成: 起始记录201, 共50行
2025-08-13 13:30:46.270 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3930 | 🔍 [FINAL修复] set_pagination_state最终调用完成
2025-08-13 13:30:46.270 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3944 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-08-13 13:30:46.271 | INFO     | src.services.table_data_service:load_table_data:539 | 📨[data_event] published
2025-08-13 13:30:46.271 | INFO     | src.gui.prototype.prototype_main_window:_force_global_redraw:8735 | 🔧 [P2-重影修复] 数据刷新成功
2025-08-13 13:30:46.271 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:8778 | 🔧 [P1-2] 开始智能显示亮度修复（增强防护）
2025-08-13 13:30:46.272 | INFO     | src.gui.prototype.prototype_main_window:_refresh_display_state:8531 | 🔧 [P1-3] 显示状态刷新完成
2025-08-13 13:30:46.272 | INFO     | src.gui.prototype.prototype_main_window:_verify_refresh_results:8545 | 🔧 [P1-3] 开始验证刷新结果
2025-08-13 13:30:46.272 | INFO     | src.gui.prototype.prototype_main_window:_verify_refresh_results:8572 | 🔧 [P1-3] 全局状态刷新成功: {'success': True, 'elapsed_time': 0.29775142669677734, 'total_stages': 10, 'expected_stages': 5, 'completed_stages': 5, 'error_count': 0, 'errors': []}
2025-08-13 13:30:46.275 | INFO     | src.gui.prototype.prototype_main_window:_restore_table_ui_state:10110 | 🔧 [P0-新4修复] 通过StateManager恢复状态成功: salary_data_2025_08_active_employees
2025-08-13 13:30:46.275 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7957 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: salary_data_2025_08_active_employees
2025-08-13 13:30:46.295 | INFO     | src.gui.prototype.prototype_main_window:_restore_table_ui_state:10110 | 🔧 [P0-新4修复] 通过StateManager恢复状态成功: salary_data_2025_08_active_employees
2025-08-13 13:30:46.295 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7957 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: salary_data_2025_08_active_employees
2025-08-13 13:30:46.364 | INFO     | src.gui.prototype.prototype_main_window:_restore_table_ui_state:10110 | 🔧 [P0-新4修复] 通过StateManager恢复状态成功: salary_data_2025_08_active_employees
2025-08-13 13:30:46.364 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7957 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: salary_data_2025_08_active_employees
2025-08-13 13:30:50.662 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年 > 8月 > A岗职工', '工资表 > 2025年 > 8月 > 全部在职人员', '工资表 > 2025年', '工资表 > 2025年 > 8月', '工资表 > 2025年 > 8月 > 退休人员']
2025-08-13 13:30:50.663 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:6904 | 导航变化: 工资表 > 2025年 > 8月 > A岗职工
2025-08-13 13:30:50.664 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:9957 | 🚫 [用户要求] 表格切换不显示加载条: salary_data_2025_08_active_employees -> None
2025-08-13 13:30:50.665 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:9973 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-08-13 13:30:50.666 | INFO     | src.gui.prototype.prototype_main_window:clear_field_processing_cache:6782 | 🔧 [缓存清理] 清理了所有 0 个字段处理缓存条目
2025-08-13 13:30:50.667 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:7778 | 🔧 [表名生成] 从完整路径生成表名: ['工资表', '2025年', '8月', 'A岗职工'] -> salary_data_2025_08_a_grade_employees
2025-08-13 13:30:50.668 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-13 13:30:50.669 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:362 | 已清理表 salary_data_2025_08_a_grade_employees 的缓存
2025-08-13 13:30:50.670 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:10643 | 已注册 2 个表格到表头管理器
2025-08-13 13:30:50.671 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1188 | 🔧 [P1-3] 开始智能表头重影检测，共 2 个表格
2025-08-13 13:30:50.672 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1261 | 🔧 [P1-3] 智能表头重影检测完成，耗时 1.00ms
2025-08-13 13:30:50.673 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1262 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-08-13 13:30:50.674 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7120 | 🆕 使用新架构加载数据: salary_data_2025_08_a_grade_employees（通过事件系统）
2025-08-13 13:30:50.675 | INFO     | src.services.table_data_service:load_table_data:366 | 加载表格数据: 页码: 1
2025-08-13 13:30:50.675 | INFO     | src.services.table_data_service:load_table_data:390 | [缓存命中] 使用缓存数据: 第1页
2025-08-13 13:30:50.676 | INFO     | src.services.table_data_service:load_table_data:401 | 📨[data_event] publish(cache) | table=salary_data_2025_08_a_grade_employees | rows=50 | page=1 | size=50 | total=62 | request_id=SV-1755063050676-3d75d178-C
2025-08-13 13:30:50.677 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3757 | 📥[data_event] received | table=salary_data_2025_08_a_grade_employees | request_id=SV-1755063050676-3d75d178-C
2025-08-13 13:30:50.678 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3789 | 数据内容: 50行 x 22列
2025-08-13 13:30:50.678 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3815 | 🔧 [数据更新] 接收到cached_load操作的数据更新事件: salary_data_2025_08_a_grade_employees, 50行
2025-08-13 13:30:50.681 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:6861 | 表 salary_data_2025_08_a_grade_employees 没有用户偏好设置，显示所有可见字段
2025-08-13 13:30:50.682 | INFO     | src.gui.prototype.prototype_main_window:set_data:848 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-08-13 13:30:50.686 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:6861 | 表 salary_data_2025_08_a_grade_employees 没有用户偏好设置，显示所有可见字段
2025-08-13 13:30:50.690 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 50行，表名: salary_data_2025_08_a_grade_employees
2025-08-13 13:30:50.693 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=34660024, 薪资=N/A
2025-08-13 13:30:50.694 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=20222002, 薪资=N/A
2025-08-13 13:30:50.695 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:5271 | 最大可见行数已更新: 50 -> 50
2025-08-13 13:30:50.696 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5324 | 根据数据量(50)自动调整最大可见行数为: 50
2025-08-13 13:30:50.699 | INFO     | src.modules.format_management.format_renderer:render_dataframe:107 | 🎯 [格式渲染] 隐藏字段配置: ['id', 'sequence_number', 'created_at', 'updated_at']
2025-08-13 13:30:50.700 | INFO     | src.modules.format_management.format_renderer:render_dataframe:115 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-08-13 13:30:50.709 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-08-13 13:30:50.710 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-08-13 13:30:50.714 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 月份 -> 英文名: month -> 类型: month_string
2025-08-13 13:30:50.714 | INFO     | src.modules.format_management.field_registry:get_display_fields:1551 | 🔧 [P0-2修复] display_order转换完成: 22个字段，原始字段数: 22
2025-08-13 13:30:50.715 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | 🔧 [DEBUG] table_type=a_grade_employees, display_fields=22个字段
2025-08-13 13:30:50.715 | INFO     | src.modules.format_management.format_renderer:render_dataframe:210 | 🔧 [P1修复] 字段匹配完成: 成功匹配 22/22 个字段
2025-08-13 13:30:50.716 | INFO     | src.modules.format_management.format_renderer:render_dataframe:214 | 🎯 [格式渲染] 已按display_order排列字段: 22个字段
2025-08-13 13:30:50.717 | INFO     | src.modules.format_management.format_renderer:render_dataframe:234 | 🎨 [格式渲染] DataFrame格式化完成: a_grade_employees, 行数: 50, 列数: 22
2025-08-13 13:30:50.717 | INFO     | src.modules.format_management.unified_format_manager:format_data:367 | 🎯 [统一格式管理] 数据格式化完成: a_grade_employees, 行数: 50, 列数: 22
2025-08-13 13:30:50.729 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 50行数据
2025-08-13 13:30:50.730 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:52 | [数据流追踪] 开始小数据集渲染: 50行 x 22列
2025-08-13 13:30:50.736 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:112 | [数据流追踪] 小数据集渲染完成: 耗时6.0ms, 平均每行0.12ms
2025-08-13 13:30:50.736 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=50, 渲染时间=6.0ms, 策略=small_dataset
2025-08-13 13:30:50.736 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:279 | [数据流追踪] 选择小数据集渲染策略
2025-08-13 13:30:50.737 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 34660024
2025-08-13 13:30:50.737 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20222002
2025-08-13 13:30:50.737 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 14660141
2025-08-13 13:30:50.738 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 34660002
2025-08-13 13:30:50.738 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 34660010
2025-08-13 13:30:50.738 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 50
2025-08-13 13:30:50.739 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[0]: 工号=34660024, 薪资=N/A
2025-08-13 13:30:50.739 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[1]: 工号=20222002, 薪资=N/A
2025-08-13 13:30:50.739 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[2]: 工号=14660141, 薪资=N/A
2025-08-13 13:30:50.739 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[3]: 工号=34660002, 薪资=N/A
2025-08-13 13:30:50.740 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[4]: 工号=34660010, 薪资=N/A
2025-08-13 13:30:50.740 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:479 | 可见行数据顺序: ['34660024', '20222002', '14660141', '34660002', '34660010']
2025-08-13 13:30:50.740 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:442 | 表格数据已设置: 50 行, 22 列
2025-08-13 13:30:50.741 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:417 | 🔧 [新架构] 排序管理器切换到表格: salary_data_2025_08_a_grade_employees
2025-08-13 13:30:50.742 | INFO     | src.gui.prototype.widgets.column_sort_manager:_reload_field_mapping_for_table:448 | 🔧 [新架构] 为表格 salary_data_2025_08_a_grade_employees 重新加载 26 个字段映射
2025-08-13 13:30:50.742 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_state_changed:7421 | 🆕 [新架构多列排序] 排序状态变化: 0 列
2025-08-13 13:30:50.742 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_state_changed:7429 | 🆕 [新架构多列排序] 当前排序: 无排序
2025-08-13 13:30:50.743 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_cleared:7466 | 🆕 [新架构多列排序] 排序已清除
2025-08-13 13:30:50.743 | INFO     | src.gui.prototype.prototype_main_window:_handle_sort_cleared:4526 | 🆕 [新架构排序] 处理排序清除
2025-08-13 13:30:50.743 | INFO     | src.gui.prototype.prototype_main_window:_handle_sort_cleared:4537 | 排序状态已从管理器中清除
2025-08-13 13:30:50.743 | INFO     | src.gui.prototype.prototype_main_window:_handle_sort_cleared:4543 | 清除排序，当前页码: 1
2025-08-13 13:30:50.744 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:4174 | 🔧 [排序调试] 准备发布排序请求: salary_data_2025_08_a_grade_employees, []
2025-08-13 13:30:50.744 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:4185 | 🔧 [P3修复] 排序列为空，尝试从状态管理器恢复
2025-08-13 13:30:50.744 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:4211 | [数据流追踪] 开始处理排序请求: 表=salary_data_2025_08_a_grade_employees, 排序列数=0, 页码=1
2025-08-13 13:30:50.745 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:4257 | 🔧 [排序] 使用实际页码: 1 (传入页码: 1)
2025-08-13 13:30:50.745 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:4283 | 🔧 [P1-修复] 转换后排序列为空，将发布清空排序事件
2025-08-13 13:30:50.745 | INFO     | src.services.table_data_service:_handle_sort_request:154 | [排序调试] 接收到排序请求
2025-08-13 13:30:50.745 | INFO     | src.services.table_data_service:_handle_sort_request:166 | [排序调试] 收到清空排序请求，将清理排序状态
2025-08-13 13:30:50.746 | INFO     | src.core.unified_state_manager:update_table_state:232 | 表状态已更新: salary_data_2025_08_a_grade_employees, 变更类型: StateChangeType.SORT_CHANGED
2025-08-13 13:30:50.747 | INFO     | src.core.unified_data_request_manager:request_table_data:212 | 开始处理数据请求
2025-08-13 13:30:50.747 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:590 | 正在从表 salary_data_2025_08_a_grade_employees 分页获取数据（支持排序）: 第1页, 每页50条, 排序=0列
2025-08-13 13:30:50.748 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:706 | 成功从表 salary_data_2025_08_a_grade_employees 获取第1页数据（含排序）: 50 行，总计62行
2025-08-13 13:30:50.749 | INFO     | src.core.unified_data_request_manager:request_table_data:262 | 数据请求处理完成: 字段=26, 行数=50, 耗时=2.0ms
2025-08-13 13:30:50.749 | INFO     | src.services.table_data_service:_handle_sort_request:205 | [修复排序] 排序操作成功，发布数据更新事件: salary_data_2025_08_a_grade_employees, 50行
2025-08-13 13:30:50.750 | INFO     | src.services.table_data_service:_handle_sort_request:225 | [修复排序] 数据更新事件已发布
2025-08-13 13:30:50.750 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:4296 | [数据流追踪] 排序事件已发布: 表=salary_data_2025_08_a_grade_employees, 排序列数=0, 页码=1
2025-08-13 13:30:50.750 | INFO     | src.gui.prototype.widgets.column_sort_manager:clear_all_sorts:268 | 🆕 [多列排序] 已清除所有排序
2025-08-13 13:30:50.750 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 50 行, 耗时: 50.1ms
2025-08-13 13:30:50.751 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7922 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-13 13:30:50.751 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7935 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-13 13:30:50.752 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: salary_data_2025_08_a_grade_employees (22/22 列)
2025-08-13 13:30:50.752 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-13 13:30:50.753 | INFO     | src.gui.prototype.prototype_main_window:set_data:972 | 📊[pagination-write] 跳过写入当前页数据量(total=50)，保持 total=0，等待数据事件纠正
2025-08-13 13:30:50.753 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3859 | 🔧 [P1-1修复] 数据已成功设置到UI，使用新映射表头: 50行, 22列
2025-08-13 13:30:50.753 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 22 个, 行号起始 1, 共 50 行
2025-08-13 13:30:50.754 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3875 | 🔧 [P0-新5修复] 表头强制刷新完成: salary_data_2025_08_a_grade_employees, 传递参数: 22个表头
2025-08-13 13:30:50.754 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3879 | 🔧 [统一数据设置] 数据已成功设置到UI: 50行, 22列
2025-08-13 13:30:50.754 | INFO     | src.gui.widgets.pagination_widget:batch_update:472 | 📊[pagination-write] batch_update | src=_on_new_data_updated/cached_load | rid=SV-1755063050676-3d75d178-C | total: 0->62, page: 1->1, size: 50->50, pages: 1->2
2025-08-13 13:30:50.755 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3913 | ✅[pagination-state] now | page=1 / pages=2 / total=62 | rid=SV-1755063050676-3d75d178-C
2025-08-13 13:30:50.755 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3926 | 🔍 [FINAL修复] 最终调用set_pagination_state: 第1页, 记录1-50 | rid=SV-1755063050676-3d75d178-C
2025-08-13 13:30:50.755 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4166 | 🔍 [表格调试] ================== set_pagination_state 开始 ==================
2025-08-13 13:30:50.756 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4167 | 🔍 [表格调试] 收到分页状态参数: {'current_page': 1, 'page_size': 50, 'total_records': 62, 'start_record': 1, 'end_record': 50}
2025-08-13 13:30:50.756 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4168 | 🔍 [表格调试] 当前表格行数: 50
2025-08-13 13:30:50.756 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4169 | 🔍 [表格调试] 当前表格列数: 22
2025-08-13 13:30:50.757 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4171 | 🔧 [分页行号修复] 接收分页状态: 第1页, 记录1-50
2025-08-13 13:30:50.757 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: salary_data_2025_08_a_grade_employees (22/22 列)
2025-08-13 13:30:50.757 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4198 | 🔧 [P0-3修复] 分页时已恢复列宽设置: salary_data_2025_08_a_grade_employees
2025-08-13 13:30:50.758 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4206 | 🔧 [分页行号修复] 表格状态: 当前行数=50, 期望行数=50
2025-08-13 13:30:50.758 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 22 个, 行号起始 1, 共 50 行
2025-08-13 13:30:50.758 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_force_update_pagination_row_numbers:4286 | 🔧 [分页行号修复] 强制更新完成: 起始记录1, 共50行
2025-08-13 13:30:50.758 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3930 | 🔍 [FINAL修复] set_pagination_state最终调用完成
2025-08-13 13:30:50.779 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:379 | 已清理所有缓存
2025-08-13 13:30:50.779 | INFO     | src.gui.prototype.prototype_main_window:_validate_component_state_consistency:10451 | ✅ [新架构] 组件状态一致性验证通过
2025-08-13 13:30:50.780 | INFO     | src.gui.prototype.prototype_main_window:complete_navigation_transition:10414 | 🆕 [新架构] 导航迁移完成
2025-08-13 13:30:50.780 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3944 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-08-13 13:30:50.780 | INFO     | src.services.table_data_service:load_table_data:426 | 📨[data_event] published(cache)
2025-08-13 13:30:50.781 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7125 | 🆕 新架构数据加载请求已发送，等待事件系统处理
2025-08-13 13:30:50.781 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:900 | 导航选择: 工资表 > 2025年 > 8月 > A岗职工
2025-08-13 13:30:50.852 | INFO     | src.gui.prototype.prototype_main_window:_restore_table_ui_state:10110 | 🔧 [P0-新4修复] 通过StateManager恢复状态成功: salary_data_2025_08_a_grade_employees
2025-08-13 13:30:50.852 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7957 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: salary_data_2025_08_a_grade_employees
2025-08-13 13:30:53.596 | INFO     | src.gui.prototype.widgets.column_sort_manager:handle_header_click:138 | 🆕 [多列排序] 新增列5(2025年岗位工资)排序: 升序
2025-08-13 13:30:53.597 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_state_changed:7421 | 🆕 [新架构多列排序] 排序状态变化: 1 列
2025-08-13 13:30:53.597 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_state_changed:7429 | 🆕 [新架构多列排序] 当前排序: 2025年岗位工资: 升序
2025-08-13 13:30:53.598 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_request:7444 | 🆕 [新架构多列排序] 排序请求: salary_data_2025_08_a_grade_employees.position_salary_2025 -> ascending
2025-08-13 13:30:53.599 | INFO     | src.gui.prototype.prototype_main_window:_handle_sort_applied_impl:4499 | 🔧 [P0-CRITICAL] 简化排序处理: 列5, position_salary_2025, ascending
2025-08-13 13:30:53.600 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:4174 | 🔧 [排序调试] 准备发布排序请求: salary_data_2025_08_a_grade_employees, [{'column_name': 'position_salary_2025', 'order': 'ascending', 'priority': 0}]
2025-08-13 13:30:53.600 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:4211 | [数据流追踪] 开始处理排序请求: 表=salary_data_2025_08_a_grade_employees, 排序列数=1, 页码=1
2025-08-13 13:30:53.600 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:4257 | 🔧 [排序] 使用实际页码: 1 (传入页码: 1)
2025-08-13 13:30:53.601 | INFO     | src.services.table_data_service:_handle_sort_request:154 | [排序调试] 接收到排序请求
2025-08-13 13:30:53.602 | INFO     | src.core.unified_state_manager:update_table_state:232 | 表状态已更新: salary_data_2025_08_a_grade_employees, 变更类型: StateChangeType.SORT_CHANGED
2025-08-13 13:30:53.603 | INFO     | src.core.unified_data_request_manager:request_table_data:212 | 开始处理数据请求
2025-08-13 13:30:53.603 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:779 | [FIX] [排序修复] 列 'position_salary_2025' 存在于表 'salary_data_2025_08_a_grade_employees' 中（类型: REAL）
2025-08-13 13:30:53.604 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:779 | [FIX] [排序修复] 列 'position_salary_2025' 存在于表 'salary_data_2025_08_a_grade_employees' 中（类型: REAL）
2025-08-13 13:30:53.605 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:590 | 正在从表 salary_data_2025_08_a_grade_employees 分页获取数据（支持排序）: 第1页, 每页50条, 排序=1列
2025-08-13 13:30:53.605 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:779 | [FIX] [排序修复] 列 'position_salary_2025' 存在于表 'salary_data_2025_08_a_grade_employees' 中（类型: REAL）
2025-08-13 13:30:53.607 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:706 | 成功从表 salary_data_2025_08_a_grade_employees 获取第1页数据（含排序）: 50 行，总计62行
2025-08-13 13:30:53.608 | INFO     | src.core.unified_data_request_manager:request_table_data:262 | 数据请求处理完成: 字段=26, 行数=50, 耗时=4.5ms
2025-08-13 13:30:53.608 | INFO     | src.services.table_data_service:_handle_sort_request:205 | [修复排序] 排序操作成功，发布数据更新事件: salary_data_2025_08_a_grade_employees, 50行
2025-08-13 13:30:53.609 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3757 | 📥[data_event] received | table=salary_data_2025_08_a_grade_employees | request_id=None
2025-08-13 13:30:53.609 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3789 | 数据内容: 50行 x 26列
2025-08-13 13:30:53.610 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3812 | 🔧 [关键修复] 接收到sort_change操作的数据更新事件，开始UI更新: salary_data_2025_08_a_grade_employees, 50行
2025-08-13 13:30:53.611 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:6861 | 表 salary_data_2025_08_a_grade_employees 没有用户偏好设置，显示所有可见字段
2025-08-13 13:30:53.612 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3841 | 🚀 [排序优化] 检测到排序操作，标记为高优先级同步更新
2025-08-13 13:30:53.613 | INFO     | src.gui.prototype.prototype_main_window:set_data:848 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-08-13 13:30:53.614 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:6861 | 表 salary_data_2025_08_a_grade_employees 没有用户偏好设置，显示所有可见字段
2025-08-13 13:30:53.616 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 50行，表名: salary_data_2025_08_a_grade_employees
2025-08-13 13:30:53.618 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=24660020.0, 薪资=N/A
2025-08-13 13:30:53.618 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=20242002.0, 薪资=N/A
2025-08-13 13:30:53.619 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:5271 | 最大可见行数已更新: 50 -> 50
2025-08-13 13:30:53.619 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5324 | 根据数据量(50)自动调整最大可见行数为: 50
2025-08-13 13:30:53.621 | INFO     | src.modules.format_management.format_renderer:render_dataframe:107 | 🎯 [格式渲染] 隐藏字段配置: ['id', 'sequence_number', 'created_at', 'updated_at']
2025-08-13 13:30:53.622 | INFO     | src.modules.format_management.format_renderer:render_dataframe:115 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-08-13 13:30:53.627 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-08-13 13:30:53.627 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-08-13 13:30:53.630 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 月份 -> 英文名: month -> 类型: month_string
2025-08-13 13:30:53.631 | INFO     | src.modules.format_management.field_registry:get_display_fields:1551 | 🔧 [P0-2修复] display_order转换完成: 22个字段，原始字段数: 22
2025-08-13 13:30:53.631 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | 🔧 [DEBUG] table_type=a_grade_employees, display_fields=22个字段
2025-08-13 13:30:53.631 | INFO     | src.modules.format_management.format_renderer:render_dataframe:210 | 🔧 [P1修复] 字段匹配完成: 成功匹配 22/22 个字段
2025-08-13 13:30:53.633 | INFO     | src.modules.format_management.format_renderer:render_dataframe:214 | 🎯 [格式渲染] 已按display_order排列字段: 22个字段
2025-08-13 13:30:53.633 | INFO     | src.modules.format_management.format_renderer:render_dataframe:234 | 🎨 [格式渲染] DataFrame格式化完成: a_grade_employees, 行数: 50, 列数: 22
2025-08-13 13:30:53.633 | INFO     | src.modules.format_management.unified_format_manager:format_data:367 | 🎯 [统一格式管理] 数据格式化完成: a_grade_employees, 行数: 50, 列数: 22
2025-08-13 13:30:53.644 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 50行数据
2025-08-13 13:30:53.645 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:52 | [数据流追踪] 开始小数据集渲染: 50行 x 22列
2025-08-13 13:30:53.649 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:112 | [数据流追踪] 小数据集渲染完成: 耗时3.5ms, 平均每行0.07ms
2025-08-13 13:30:53.650 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=50, 渲染时间=3.5ms, 策略=small_dataset
2025-08-13 13:30:53.650 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:279 | [数据流追踪] 选择小数据集渲染策略
2025-08-13 13:30:53.650 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 24660020
2025-08-13 13:30:53.651 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20242002
2025-08-13 13:30:53.651 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 34660024
2025-08-13 13:30:53.651 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20222002
2025-08-13 13:30:53.651 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 14660141
2025-08-13 13:30:53.652 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 50
2025-08-13 13:30:53.652 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[0]: 工号=24660020, 薪资=N/A
2025-08-13 13:30:53.652 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[1]: 工号=20242002, 薪资=N/A
2025-08-13 13:30:53.652 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[2]: 工号=34660024, 薪资=N/A
2025-08-13 13:30:53.653 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[3]: 工号=20222002, 薪资=N/A
2025-08-13 13:30:53.653 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[4]: 工号=14660141, 薪资=N/A
2025-08-13 13:30:53.653 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:479 | 可见行数据顺序: ['24660020', '20242002', '34660024', '20222002', '14660141']
2025-08-13 13:30:53.654 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:442 | 表格数据已设置: 50 行, 22 列
2025-08-13 13:30:53.654 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 50 行, 耗时: 37.1ms
2025-08-13 13:30:53.655 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7922 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-13 13:30:53.655 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7932 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-08-13 13:30:53.655 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7933 | 🔧 [P0-排序修复] 排序描述: 2025年岗位工资: 升序
2025-08-13 13:30:53.656 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: salary_data_2025_08_a_grade_employees (22/22 列)
2025-08-13 13:30:53.656 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-13 13:30:53.657 | INFO     | src.gui.prototype.prototype_main_window:set_data:972 | 📊[pagination-write] 跳过写入当前页数据量(total=50)，保持 total=62，等待数据事件纠正
2025-08-13 13:30:53.657 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3859 | 🔧 [P1-1修复] 数据已成功设置到UI，使用新映射表头: 50行, 22列
2025-08-13 13:30:53.657 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 22 个, 行号起始 1, 共 50 行
2025-08-13 13:30:53.658 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3875 | 🔧 [P0-新5修复] 表头强制刷新完成: salary_data_2025_08_a_grade_employees, 传递参数: 22个表头
2025-08-13 13:30:53.658 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3879 | 🔧 [统一数据设置] 数据已成功设置到UI: 50行, 22列
2025-08-13 13:30:53.658 | INFO     | src.gui.widgets.pagination_widget:batch_update:472 | 📊[pagination-write] batch_update | src=_on_new_data_updated/sort_change | rid=None | total: 62->62, page: 1->1, size: 50->50, pages: 2->2
2025-08-13 13:30:53.659 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3913 | ✅[pagination-state] now | page=1 / pages=2 / total=62 | rid=None
2025-08-13 13:30:53.659 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3926 | 🔍 [FINAL修复] 最终调用set_pagination_state: 第1页, 记录1-50 | rid=None
2025-08-13 13:30:53.659 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4166 | 🔍 [表格调试] ================== set_pagination_state 开始 ==================
2025-08-13 13:30:53.659 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4167 | 🔍 [表格调试] 收到分页状态参数: {'current_page': 1, 'page_size': 50, 'total_records': 62, 'start_record': 1, 'end_record': 50}
2025-08-13 13:30:53.660 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4168 | 🔍 [表格调试] 当前表格行数: 50
2025-08-13 13:30:53.660 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4169 | 🔍 [表格调试] 当前表格列数: 22
2025-08-13 13:30:53.660 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4171 | 🔧 [分页行号修复] 接收分页状态: 第1页, 记录1-50
2025-08-13 13:30:53.661 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: salary_data_2025_08_a_grade_employees (22/22 列)
2025-08-13 13:30:53.661 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4198 | 🔧 [P0-3修复] 分页时已恢复列宽设置: salary_data_2025_08_a_grade_employees
2025-08-13 13:30:53.662 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4206 | 🔧 [分页行号修复] 表格状态: 当前行数=50, 期望行数=50
2025-08-13 13:30:53.662 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 22 个, 行号起始 1, 共 50 行
2025-08-13 13:30:53.662 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_force_update_pagination_row_numbers:4286 | 🔧 [分页行号修复] 强制更新完成: 起始记录1, 共50行
2025-08-13 13:30:53.662 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3930 | 🔍 [FINAL修复] set_pagination_state最终调用完成
2025-08-13 13:30:53.663 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3944 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-08-13 13:30:53.663 | INFO     | src.services.table_data_service:_handle_sort_request:225 | [修复排序] 数据更新事件已发布
2025-08-13 13:30:53.663 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:4296 | [数据流追踪] 排序事件已发布: 表=salary_data_2025_08_a_grade_employees, 排序列数=1, 页码=1
2025-08-13 13:30:53.663 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_header_clicked_impl:8171 | 🆕 [新架构多列排序] 成功处理列5(2025年岗位工资)点击
2025-08-13 13:30:53.756 | INFO     | src.gui.prototype.prototype_main_window:_restore_table_ui_state:10110 | 🔧 [P0-新4修复] 通过StateManager恢复状态成功: salary_data_2025_08_a_grade_employees
2025-08-13 13:30:53.756 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7957 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: salary_data_2025_08_a_grade_employees
2025-08-13 13:30:57.069 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4740 | 📍[page-change] 入口 | table=salary_data_2025_08_a_grade_employees | target_page=2 | request_id=PG-1755063057069-e619c58d
2025-08-13 13:30:57.070 | INFO     | src.core.pagination_state_manager:can_start_pagination:113 | ✅ [允许] 分页请求可以开始: salary_data_2025_08_a_grade_employees, 页2
2025-08-13 13:30:57.071 | INFO     | src.gui.prototype.prototype_main_window:_save_current_table_ui_state:10036 | 🔧 [P0-2修复] 通过StateManager保存状态成功: salary_data_2025_08_a_grade_employees
2025-08-13 13:30:57.072 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4819 | 🔧 [分页请求] 第2页, 表: salary_data_2025_08_a_grade_employees, 上下文已设置
2025-08-13 13:30:57.072 | INFO     | src.core.request_deduplication_manager:__init__:65 | 请求去重管理器初始化完成，默认TTL: 0.3秒
2025-08-13 13:30:57.072 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4836 | 🔧 [立即修复] 分页请求通过强化去重检查: salary_data_2025_08_a_grade_employees, 第2页
2025-08-13 13:30:57.073 | INFO     | src.services.table_data_service:load_table_data:366 | 加载表格数据: 页码: 2
2025-08-13 13:30:57.073 | INFO     | src.core.unified_data_request_manager:request_table_data:212 | 开始处理数据请求
2025-08-13 13:30:57.074 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:779 | [FIX] [排序修复] 列 'position_salary_2025' 存在于表 'salary_data_2025_08_a_grade_employees' 中（类型: REAL）
2025-08-13 13:30:57.074 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:779 | [FIX] [排序修复] 列 'position_salary_2025' 存在于表 'salary_data_2025_08_a_grade_employees' 中（类型: REAL）
2025-08-13 13:30:57.075 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:590 | 正在从表 salary_data_2025_08_a_grade_employees 分页获取数据（支持排序）: 第2页, 每页50条, 排序=1列
2025-08-13 13:30:57.075 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:779 | [FIX] [排序修复] 列 'position_salary_2025' 存在于表 'salary_data_2025_08_a_grade_employees' 中（类型: REAL）
2025-08-13 13:30:57.077 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:706 | 成功从表 salary_data_2025_08_a_grade_employees 获取第2页数据（含排序）: 12 行，总计62行
2025-08-13 13:30:57.078 | INFO     | src.core.unified_data_request_manager:request_table_data:262 | 数据请求处理完成: 字段=26, 行数=12, 耗时=5.0ms
2025-08-13 13:30:57.078 | INFO     | src.services.table_data_service:load_table_data:462 | [新架构] 使用统一格式管理器（单例优化）
2025-08-13 13:30:57.079 | INFO     | src.modules.format_management.format_renderer:render_dataframe:107 | 🎯 [格式渲染] 隐藏字段配置: ['id', 'sequence_number', 'created_at', 'updated_at']
2025-08-13 13:30:57.079 | INFO     | src.modules.format_management.format_renderer:render_dataframe:115 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-08-13 13:30:57.084 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-08-13 13:30:57.085 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-08-13 13:30:57.087 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 月份 -> 英文名: month -> 类型: month_string
2025-08-13 13:30:57.089 | INFO     | src.modules.format_management.field_registry:get_display_fields:1551 | 🔧 [P0-2修复] display_order转换完成: 22个字段，原始字段数: 22
2025-08-13 13:30:57.089 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | 🔧 [DEBUG] table_type=a_grade_employees, display_fields=22个字段
2025-08-13 13:30:57.090 | INFO     | src.modules.format_management.format_renderer:render_dataframe:210 | 🔧 [P1修复] 字段匹配完成: 成功匹配 22/22 个字段
2025-08-13 13:30:57.091 | INFO     | src.modules.format_management.format_renderer:render_dataframe:214 | 🎯 [格式渲染] 已按display_order排列字段: 22个字段
2025-08-13 13:30:57.091 | INFO     | src.modules.format_management.format_renderer:render_dataframe:234 | 🎨 [格式渲染] DataFrame格式化完成: a_grade_employees, 行数: 12, 列数: 22
2025-08-13 13:30:57.092 | INFO     | src.modules.format_management.unified_format_manager:format_data:367 | 🎯 [统一格式管理] 数据格式化完成: a_grade_employees, 行数: 12, 列数: 22
2025-08-13 13:30:57.093 | INFO     | src.core.unified_state_manager:update_table_state:232 | 表状态已更新: salary_data_2025_08_a_grade_employees, 变更类型: None
2025-08-13 13:30:57.093 | INFO     | src.services.table_data_service:load_table_data:512 | [修复数据发布] 数据加载成功，发布更新事件: 12行
2025-08-13 13:30:57.094 | INFO     | src.services.table_data_service:load_table_data:516 | 📨[data_event] publish | table=salary_data_2025_08_a_grade_employees | rows=12 | page=2 | size=50 | total=62 | req_type=RequestType.INITIAL_LOAD | request_id=PG-1755063057069-e619c58d
2025-08-13 13:30:57.094 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3757 | 📥[data_event] received | table=salary_data_2025_08_a_grade_employees | request_id=PG-1755063057069-e619c58d
2025-08-13 13:30:57.095 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3789 | 数据内容: 12行 x 22列
2025-08-13 13:30:57.095 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3815 | 🔧 [数据更新] 接收到initial_load操作的数据更新事件: salary_data_2025_08_a_grade_employees, 12行
2025-08-13 13:30:57.096 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:6861 | 表 salary_data_2025_08_a_grade_employees 没有用户偏好设置，显示所有可见字段
2025-08-13 13:30:57.097 | INFO     | src.gui.prototype.prototype_main_window:set_data:848 | 通过公共接口设置新的表格数据(DataFrame): 12 行
2025-08-13 13:30:57.098 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:6861 | 表 salary_data_2025_08_a_grade_employees 没有用户偏好设置，显示所有可见字段
2025-08-13 13:30:57.100 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 12行，表名: salary_data_2025_08_a_grade_employees
2025-08-13 13:30:57.101 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=24660035, 薪资=N/A
2025-08-13 13:30:57.102 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=14660162, 薪资=N/A
2025-08-13 13:30:57.105 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:5271 | 最大可见行数已更新: 50 -> 12
2025-08-13 13:30:57.106 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5324 | 根据数据量(12)自动调整最大可见行数为: 12
2025-08-13 13:30:57.107 | INFO     | src.modules.format_management.format_renderer:render_dataframe:107 | 🎯 [格式渲染] 隐藏字段配置: ['id', 'sequence_number', 'created_at', 'updated_at']
2025-08-13 13:30:57.107 | INFO     | src.modules.format_management.format_renderer:render_dataframe:115 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-08-13 13:30:57.110 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-08-13 13:30:57.111 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-08-13 13:30:57.113 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 月份 -> 英文名: month -> 类型: month_string
2025-08-13 13:30:57.114 | INFO     | src.modules.format_management.field_registry:get_display_fields:1551 | 🔧 [P0-2修复] display_order转换完成: 22个字段，原始字段数: 22
2025-08-13 13:30:57.114 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | 🔧 [DEBUG] table_type=a_grade_employees, display_fields=22个字段
2025-08-13 13:30:57.114 | INFO     | src.modules.format_management.format_renderer:render_dataframe:210 | 🔧 [P1修复] 字段匹配完成: 成功匹配 22/22 个字段
2025-08-13 13:30:57.115 | INFO     | src.modules.format_management.format_renderer:render_dataframe:214 | 🎯 [格式渲染] 已按display_order排列字段: 22个字段
2025-08-13 13:30:57.115 | INFO     | src.modules.format_management.format_renderer:render_dataframe:234 | 🎨 [格式渲染] DataFrame格式化完成: a_grade_employees, 行数: 12, 列数: 22
2025-08-13 13:30:57.115 | INFO     | src.modules.format_management.unified_format_manager:format_data:367 | 🎯 [统一格式管理] 数据格式化完成: a_grade_employees, 行数: 12, 列数: 22
2025-08-13 13:30:57.118 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 12行数据
2025-08-13 13:30:57.119 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:52 | [数据流追踪] 开始小数据集渲染: 12行 x 22列
2025-08-13 13:30:57.120 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:112 | [数据流追踪] 小数据集渲染完成: 耗时1.0ms, 平均每行0.09ms
2025-08-13 13:30:57.121 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=12, 渲染时间=1.0ms, 策略=small_dataset
2025-08-13 13:30:57.121 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:279 | [数据流追踪] 选择小数据集渲染策略
2025-08-13 13:30:57.121 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 24660035
2025-08-13 13:30:57.122 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 14660162
2025-08-13 13:30:57.122 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 14660154
2025-08-13 13:30:57.122 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 14660126
2025-08-13 13:30:57.122 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 14660123
2025-08-13 13:30:57.122 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 12
2025-08-13 13:30:57.123 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[0]: 工号=24660035, 薪资=N/A
2025-08-13 13:30:57.124 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[1]: 工号=14660162, 薪资=N/A
2025-08-13 13:30:57.124 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[2]: 工号=14660154, 薪资=N/A
2025-08-13 13:30:57.124 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[3]: 工号=14660126, 薪资=N/A
2025-08-13 13:30:57.125 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[4]: 工号=14660123, 薪资=N/A
2025-08-13 13:30:57.125 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:479 | 可见行数据顺序: ['24660035', '14660162', '14660154', '14660126', '14660123']
2025-08-13 13:30:57.125 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:442 | 表格数据已设置: 12 行, 22 列
2025-08-13 13:30:57.126 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 12 行, 耗时: 25.7ms
2025-08-13 13:30:57.126 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7922 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-13 13:30:57.126 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7932 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-08-13 13:30:57.127 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7933 | 🔧 [P0-排序修复] 排序描述: 2025年岗位工资: 升序
2025-08-13 13:30:57.127 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: salary_data_2025_08_a_grade_employees (22/22 列)
2025-08-13 13:30:57.128 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-13 13:30:57.128 | INFO     | src.gui.prototype.prototype_main_window:set_data:972 | 📊[pagination-write] 跳过写入当前页数据量(total=12)，保持 total=62，等待数据事件纠正
2025-08-13 13:30:57.129 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3859 | 🔧 [P1-1修复] 数据已成功设置到UI，使用新映射表头: 12行, 22列
2025-08-13 13:30:57.129 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 22 个, 行号起始 1, 共 12 行
2025-08-13 13:30:57.129 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3875 | 🔧 [P0-新5修复] 表头强制刷新完成: salary_data_2025_08_a_grade_employees, 传递参数: 22个表头
2025-08-13 13:30:57.130 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3879 | 🔧 [统一数据设置] 数据已成功设置到UI: 12行, 22列
2025-08-13 13:30:57.130 | INFO     | src.gui.widgets.pagination_widget:batch_update:472 | 📊[pagination-write] batch_update | src=_on_new_data_updated/initial_load | rid=PG-1755063057069-e619c58d | total: 62->62, page: 2->2, size: 50->50, pages: 2->2
2025-08-13 13:30:57.130 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3913 | ✅[pagination-state] now | page=2 / pages=2 / total=62 | rid=PG-1755063057069-e619c58d
2025-08-13 13:30:57.131 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3926 | 🔍 [FINAL修复] 最终调用set_pagination_state: 第2页, 记录51-62 | rid=PG-1755063057069-e619c58d
2025-08-13 13:30:57.131 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4166 | 🔍 [表格调试] ================== set_pagination_state 开始 ==================
2025-08-13 13:30:57.131 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4167 | 🔍 [表格调试] 收到分页状态参数: {'current_page': 2, 'page_size': 50, 'total_records': 62, 'start_record': 51, 'end_record': 62}
2025-08-13 13:30:57.131 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4168 | 🔍 [表格调试] 当前表格行数: 12
2025-08-13 13:30:57.132 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4169 | 🔍 [表格调试] 当前表格列数: 22
2025-08-13 13:30:57.132 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4171 | 🔧 [分页行号修复] 接收分页状态: 第2页, 记录51-62
2025-08-13 13:30:57.132 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: salary_data_2025_08_a_grade_employees (22/22 列)
2025-08-13 13:30:57.133 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4198 | 🔧 [P0-3修复] 分页时已恢复列宽设置: salary_data_2025_08_a_grade_employees
2025-08-13 13:30:57.133 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4206 | 🔧 [分页行号修复] 表格状态: 当前行数=12, 期望行数=12
2025-08-13 13:30:57.134 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 22 个, 行号起始 51, 共 12 行
2025-08-13 13:30:57.134 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_force_update_pagination_row_numbers:4286 | 🔧 [分页行号修复] 强制更新完成: 起始记录51, 共12行
2025-08-13 13:30:57.134 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3930 | 🔍 [FINAL修复] set_pagination_state最终调用完成
2025-08-13 13:30:57.135 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3944 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-08-13 13:30:57.135 | INFO     | src.services.table_data_service:load_table_data:539 | 📨[data_event] published
2025-08-13 13:30:57.135 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4852 | 🚫 [用户要求] 不显示加载条，直接处理数据: salary_data_2025_08_a_grade_employees 第2页
2025-08-13 13:30:57.136 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4855 | 🔧 [根本修复] 分页数据获取成功: 第2页, 12行
2025-08-13 13:30:57.136 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4861 | 🔧 [根本修复] 获取数据的前3个工号: ['无工号']
2025-08-13 13:30:57.136 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4869 | [数据流追踪] 分页数据请求成功: 第2页, 12行
2025-08-13 13:30:57.137 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4878 | 🔧 [立即修复] 分页处理标志已清除，允许UI更新: 第2页
2025-08-13 13:30:57.137 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4883 | 🧹 [智能修复] 数据返回完成，缓存状态: False, 第2页
2025-08-13 13:30:57.137 | INFO     | src.gui.widgets.pagination_widget:batch_update:472 | 📊[pagination-write] batch_update | src=_on_pagination_data_loaded | rid=PL-1755063057137-f90d4734 | total: 62->62, page: 2->2, size: 50->50, pages: 2->2
2025-08-13 13:30:57.138 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4896 | ✅[pagination-state] now | page=2 / pages=2 / total=62 | rid=PL-1755063057137-f90d4734
2025-08-13 13:30:57.138 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4905 | 🔧 [P0-2修复] 字段映射应用完成: 22列
2025-08-13 13:30:57.139 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4914 | 🔧 [P0-2修复] 分页UI更新成功: 第2页, 12行数据
2025-08-13 13:30:57.139 | INFO     | src.gui.prototype.prototype_main_window:_restore_table_ui_state:10110 | 🔧 [P0-新4修复] 通过StateManager恢复状态成功: salary_data_2025_08_a_grade_employees
2025-08-13 13:30:57.139 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 22 个, 行号起始 51, 共 12 行
2025-08-13 13:30:57.140 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4932 | 🔧 [P0-1修复] 强制刷新表头完成: 22个中文表头, 记录51-62
2025-08-13 13:30:57.140 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4943 | 🔧 [P0-2修复] 分页处理完成，UI已更新: 第2页
2025-08-13 13:30:57.140 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4979 | 🔧 [立即修复] 分页处理标志已原子清除: 第2页
2025-08-13 13:30:57.141 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:5004 | 🔧 [立即修复] 分页处理完成，标志已清除: 第2页
2025-08-13 13:30:57.141 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4740 | 📍[page-change] 入口 | table=salary_data_2025_08_a_grade_employees | target_page=2 | request_id=PG-1755063057141-c5d28675
2025-08-13 13:30:57.141 | INFO     | src.core.pagination_state_manager:can_start_pagination:113 | ✅ [允许] 分页请求可以开始: salary_data_2025_08_a_grade_employees, 页2
2025-08-13 13:30:57.142 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4797 | 🔧 [立即修复] 分页事件被去重优化: 第2页
2025-08-13 13:30:57.142 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4979 | 🔧 [立即修复] 分页处理标志已原子清除: 第2页
2025-08-13 13:30:57.142 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:5004 | 🔧 [立即修复] 分页处理完成，标志已清除: 第2页
2025-08-13 13:30:57.143 | INFO     | src.gui.widgets.pagination_widget:set_current_page:533 | 页码切换到: 2
2025-08-13 13:30:57.181 | INFO     | src.core.pagination_state_manager:start_pagination:152 | 🔧 [开始] 分页操作已开始: salary_data_2025_08_a_grade_employees, 页2, ID=page_1755063057181716_24336
2025-08-13 13:30:57.181 | INFO     | src.gui.prototype.prototype_main_window:_execute_pagination_data_load:5010 | 🔧 [事件优化] 开始执行分页数据加载: 第2页
2025-08-13 13:30:57.182 | INFO     | src.core.request_deduplication_manager:__init__:65 | 请求去重管理器初始化完成，默认TTL: 0.3秒
2025-08-13 13:30:57.182 | INFO     | src.services.table_data_service:get_paginated_data:872 | 🔧 [P1修复] 获取分页数据: salary_data_2025_08_a_grade_employees, 页码: 2, 页大小: 50
2025-08-13 13:30:57.182 | INFO     | src.services.table_data_service:load_table_data:390 | [缓存命中] 使用缓存数据: 第2页
2025-08-13 13:30:57.183 | INFO     | src.services.table_data_service:load_table_data:401 | 📨[data_event] publish(cache) | table=salary_data_2025_08_a_grade_employees | rows=12 | page=2 | size=50 | total=62 | request_id=SV-1755063057183-62eb487c-C
2025-08-13 13:30:57.183 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3757 | 📥[data_event] received | table=salary_data_2025_08_a_grade_employees | request_id=SV-1755063057183-62eb487c-C
2025-08-13 13:30:57.183 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3789 | 数据内容: 12行 x 22列
2025-08-13 13:30:57.184 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3815 | 🔧 [数据更新] 接收到cached_load操作的数据更新事件: salary_data_2025_08_a_grade_employees, 12行
2025-08-13 13:30:57.185 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:6861 | 表 salary_data_2025_08_a_grade_employees 没有用户偏好设置，显示所有可见字段
2025-08-13 13:30:57.185 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3859 | 🔧 [P1-1修复] 数据已成功设置到UI，使用新映射表头: 12行, 22列
2025-08-13 13:30:57.186 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 22 个, 行号起始 1, 共 12 行
2025-08-13 13:30:57.186 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3875 | 🔧 [P0-新5修复] 表头强制刷新完成: salary_data_2025_08_a_grade_employees, 传递参数: 22个表头
2025-08-13 13:30:57.186 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3879 | 🔧 [统一数据设置] 数据已成功设置到UI: 12行, 22列
2025-08-13 13:30:57.187 | INFO     | src.gui.widgets.pagination_widget:batch_update:472 | 📊[pagination-write] batch_update | src=_on_new_data_updated/cached_load | rid=SV-1755063057183-62eb487c-C | total: 62->62, page: 2->2, size: 50->50, pages: 2->2
2025-08-13 13:30:57.187 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3913 | ✅[pagination-state] now | page=2 / pages=2 / total=62 | rid=SV-1755063057183-62eb487c-C
2025-08-13 13:30:57.188 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3926 | 🔍 [FINAL修复] 最终调用set_pagination_state: 第2页, 记录51-62 | rid=SV-1755063057183-62eb487c-C
2025-08-13 13:30:57.188 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4166 | 🔍 [表格调试] ================== set_pagination_state 开始 ==================
2025-08-13 13:30:57.188 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4167 | 🔍 [表格调试] 收到分页状态参数: {'current_page': 2, 'page_size': 50, 'total_records': 62, 'start_record': 51, 'end_record': 62}
2025-08-13 13:30:57.189 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4168 | 🔍 [表格调试] 当前表格行数: 12
2025-08-13 13:30:57.189 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4169 | 🔍 [表格调试] 当前表格列数: 22
2025-08-13 13:30:57.189 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4171 | 🔧 [分页行号修复] 接收分页状态: 第2页, 记录51-62
2025-08-13 13:30:57.190 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: salary_data_2025_08_a_grade_employees (22/22 列)
2025-08-13 13:30:57.190 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4198 | 🔧 [P0-3修复] 分页时已恢复列宽设置: salary_data_2025_08_a_grade_employees
2025-08-13 13:30:57.190 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4206 | 🔧 [分页行号修复] 表格状态: 当前行数=12, 期望行数=12
2025-08-13 13:30:57.191 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 22 个, 行号起始 51, 共 12 行
2025-08-13 13:30:57.191 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_force_update_pagination_row_numbers:4286 | 🔧 [分页行号修复] 强制更新完成: 起始记录51, 共12行
2025-08-13 13:30:57.191 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3930 | 🔍 [FINAL修复] set_pagination_state最终调用完成
2025-08-13 13:30:57.192 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3944 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-08-13 13:30:57.192 | INFO     | src.services.table_data_service:load_table_data:426 | 📨[data_event] published(cache)
2025-08-13 13:30:57.192 | INFO     | src.services.table_data_service:get_paginated_data:884 | 🔧 [P1修复] 分页数据获取成功: salary_data_2025_08_a_grade_employees, 返回 12 行
2025-08-13 13:30:57.193 | INFO     | src.gui.prototype.prototype_main_window:_execute_pagination_data_load:5050 | 🔧 [事件优化] 分页数据加载成功，UI更新已加入队列: 第2页
2025-08-13 13:30:57.193 | INFO     | src.core.pagination_state_manager:complete_pagination:189 | 🔧 [P1修复] 分页操作已完成: salary_data_2025_08_a_grade_employees, 页2, 成功=True, 耗时=0.012s
2025-08-13 13:30:57.194 | INFO     | src.core.pagination_event_optimizer:process_events:167 | 🔧 [事件处理] 批量处理完成: 1个事件
2025-08-13 13:30:57.228 | INFO     | src.gui.prototype.prototype_main_window:_restore_table_ui_state:10110 | 🔧 [P0-新4修复] 通过StateManager恢复状态成功: salary_data_2025_08_a_grade_employees
2025-08-13 13:30:57.229 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7957 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: salary_data_2025_08_a_grade_employees
2025-08-13 13:30:57.294 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 12行，表名: None
2025-08-13 13:30:57.294 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=24660035, 薪资=N/A
2025-08-13 13:30:57.295 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=14660162, 薪资=N/A
2025-08-13 13:30:57.295 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:5271 | 最大可见行数已更新: 12 -> 12
2025-08-13 13:30:57.295 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5324 | 根据数据量(12)自动调整最大可见行数为: 12
2025-08-13 13:30:57.297 | INFO     | src.modules.format_management.format_renderer:render_dataframe:107 | 🎯 [格式渲染] 隐藏字段配置: ['id', 'sequence_number', 'created_at', 'updated_at']
2025-08-13 13:30:57.297 | INFO     | src.modules.format_management.format_renderer:render_dataframe:115 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-08-13 13:30:57.301 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-08-13 13:30:57.301 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-08-13 13:30:57.303 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 月份 -> 英文名: month -> 类型: month_string
2025-08-13 13:30:57.303 | INFO     | src.modules.format_management.field_registry:get_display_fields:1551 | 🔧 [P0-2修复] display_order转换完成: 22个字段，原始字段数: 22
2025-08-13 13:30:57.303 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | 🔧 [DEBUG] table_type=a_grade_employees, display_fields=22个字段
2025-08-13 13:30:57.304 | INFO     | src.modules.format_management.format_renderer:render_dataframe:210 | 🔧 [P1修复] 字段匹配完成: 成功匹配 22/22 个字段
2025-08-13 13:30:57.305 | INFO     | src.modules.format_management.format_renderer:render_dataframe:214 | 🎯 [格式渲染] 已按display_order排列字段: 22个字段
2025-08-13 13:30:57.305 | INFO     | src.modules.format_management.format_renderer:render_dataframe:234 | 🎨 [格式渲染] DataFrame格式化完成: a_grade_employees, 行数: 12, 列数: 22
2025-08-13 13:30:57.305 | INFO     | src.modules.format_management.unified_format_manager:format_data:367 | 🎯 [统一格式管理] 数据格式化完成: a_grade_employees, 行数: 12, 列数: 22
2025-08-13 13:30:57.308 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 12行数据
2025-08-13 13:30:57.308 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:52 | [数据流追踪] 开始小数据集渲染: 12行 x 22列
2025-08-13 13:30:57.309 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:112 | [数据流追踪] 小数据集渲染完成: 耗时1.0ms, 平均每行0.08ms
2025-08-13 13:30:57.310 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=12, 渲染时间=1.0ms, 策略=small_dataset
2025-08-13 13:30:57.310 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:279 | [数据流追踪] 选择小数据集渲染策略
2025-08-13 13:30:57.310 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 24660035
2025-08-13 13:30:57.311 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 14660162
2025-08-13 13:30:57.311 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 14660154
2025-08-13 13:30:57.311 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 14660126
2025-08-13 13:30:57.312 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 14660123
2025-08-13 13:30:57.312 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 12
2025-08-13 13:30:57.312 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[0]: 工号=24660035, 薪资=N/A
2025-08-13 13:30:57.312 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[1]: 工号=14660162, 薪资=N/A
2025-08-13 13:30:57.313 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[2]: 工号=14660154, 薪资=N/A
2025-08-13 13:30:57.313 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[3]: 工号=14660126, 薪资=N/A
2025-08-13 13:30:57.314 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[4]: 工号=14660123, 薪资=N/A
2025-08-13 13:30:57.314 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:479 | 可见行数据顺序: ['24660035', '14660162', '14660154', '14660126', '14660123']
2025-08-13 13:30:57.314 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 12 行, 耗时: 19.7ms
2025-08-13 13:30:57.315 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3092 | 🔧 [重影修复] 数据设置完成，行号已由HeaderUpdateManager统一管理
2025-08-13 13:30:57.315 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7922 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-13 13:30:57.315 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7932 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-08-13 13:30:57.316 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7933 | 🔧 [P0-排序修复] 排序描述: 2025年岗位工资: 升序
2025-08-13 13:30:57.316 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: salary_data_2025_08_a_grade_employees (22/22 列)
2025-08-13 13:30:57.316 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3157 | 🔧 [重影修复] 行号已由HeaderUpdateManager统一管理
2025-08-13 13:30:57.317 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-13 13:30:57.317 | INFO     | src.gui.prototype.prototype_main_window:_restore_table_ui_state:10110 | 🔧 [P0-新4修复] 通过StateManager恢复状态成功: salary_data_2025_08_a_grade_employees
2025-08-13 13:30:57.317 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 22 个, 行号起始 51, 共 12 行
2025-08-13 13:30:57.318 | INFO     | src.gui.prototype.prototype_main_window:_update_pagination_ui:5103 | 🔧 [事件优化] UI更新完成: 第2页，12条记录
2025-08-13 13:30:57.318 | INFO     | src.core.pagination_event_optimizer:process_events:167 | 🔧 [事件处理] 批量处理完成: 1个事件
2025-08-13 13:30:57.364 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: salary_data_2025_08_a_grade_employees (22/22 列)
2025-08-13 13:30:57.364 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_adjust_column_widths_delayed_with_restore:4730 | 🔧 [P0-3修复] 用户列宽设置已恢复: salary_data_2025_08_a_grade_employees
2025-08-13 13:30:57.365 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_adjust_column_widths_delayed_with_restore:4752 | 🔧 [列宽修复] 使用用户保存的列宽，跳过默认调整
2025-08-13 13:30:57.416 | INFO     | src.gui.prototype.prototype_main_window:_restore_table_ui_state:10110 | 🔧 [P0-新4修复] 通过StateManager恢复状态成功: salary_data_2025_08_a_grade_employees
2025-08-13 13:30:57.416 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7957 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: salary_data_2025_08_a_grade_employees
2025-08-13 13:30:59.759 | INFO     | src.gui.style_manager:update_responsive_style:227 | 响应式样式更新: large -> medium
2025-08-13 13:30:59.760 | INFO     | src.gui.style_manager:update_responsive_style:238 | 响应式样式更新成功: medium
2025-08-13 13:31:00.063 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1280px)
2025-08-13 13:31:00.063 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2067 | MainWorkspaceArea 响应式适配: sm
2025-08-13 13:31:00.483 | INFO     | src.gui.table_header_manager:start_shadow_monitoring:892 | 🔧 [P1-1] 表头重影实时监控已启动，间隔: 2000ms
2025-08-13 13:31:01.814 | INFO     | src.gui.style_manager:update_responsive_style:227 | 响应式样式更新: medium -> large
2025-08-13 13:31:01.814 | INFO     | src.gui.style_manager:update_responsive_style:238 | 响应式样式更新成功: large
2025-08-13 13:31:02.123 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: xl (宽度: 1920px)
2025-08-13 13:31:02.124 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2067 | MainWorkspaceArea 响应式适配: xl
2025-08-13 13:31:02.543 | INFO     | src.gui.table_header_manager:start_shadow_monitoring:892 | 🔧 [P1-1] 表头重影实时监控已启动，间隔: 2000ms
2025-08-13 13:31:02.847 | INFO     | src.gui.style_manager:update_responsive_style:227 | 响应式样式更新: large -> medium
2025-08-13 13:31:02.847 | INFO     | src.gui.style_manager:update_responsive_style:238 | 响应式样式更新成功: medium
2025-08-13 13:31:03.151 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1280px)
2025-08-13 13:31:03.151 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2067 | MainWorkspaceArea 响应式适配: sm
2025-08-13 13:31:03.570 | INFO     | src.gui.table_header_manager:start_shadow_monitoring:892 | 🔧 [P1-1] 表头重影实时监控已启动，间隔: 2000ms
2025-08-13 13:31:04.417 | INFO     | __main__:main:523 | 应用程序正常退出
2025-08-13 13:31:04.419 | INFO     | src.gui.table_header_manager:cleanup_callback:162 | 🔧 [P1-1] 表格 table_0_3203488081392 已自动清理（弱引用回调）
