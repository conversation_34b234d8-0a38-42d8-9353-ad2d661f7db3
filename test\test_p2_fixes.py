#!/usr/bin/env python3
"""
P2级问题修复验证测试

验证：
1. 分页处理中的total_records变量未定义问题是否已修复
2. 数据刷新失败的错误处理是否已优化
3. 主窗口引用获取机制是否已改进
4. 缓存系统初始化是否已优化
5. 格式渲染器的降级处理是否已改进
"""

import sys
import os
import time
import re
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_total_records_variable_fix():
    """测试分页处理中的total_records变量未定义问题修复"""
    print("🔍 测试分页处理中的total_records变量未定义问题修复...")
    
    try:
        main_window_file = project_root / "src" / "gui" / "prototype" / "prototype_main_window.py"
        
        with open(main_window_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        improvements_found = []
        
        # 检查1: 是否有安全的返回值处理
        if "🔧 [P2修复] 安全的返回值解包" in content:
            improvements_found.append("安全返回值解包")
        if "total_records = 0  # 🔧 [P2修复] 预先定义默认值" in content:
            improvements_found.append("预先定义默认值")
        if "isinstance(result, tuple) and len(result) == 2" in content:
            improvements_found.append("元组类型检查")
        if "分页查询失败: {query_error}" in content:
            improvements_found.append("查询异常处理")
            
        if len(improvements_found) >= 3:
            print(f"✅ total_records变量问题已修复: {', '.join(improvements_found)}")
            return True
        else:
            print(f"❌ total_records变量修复不完整: 只找到 {improvements_found}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_data_refresh_error_handling_fix():
    """测试数据刷新失败的错误处理优化"""
    print("\n🔍 测试数据刷新失败的错误处理优化...")
    
    try:
        unified_request_manager_file = project_root / "src" / "core" / "unified_data_request_manager.py"
        
        with open(unified_request_manager_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        improvements_found = []
        
        # 检查1: 是否有default_table映射处理
        if "检测到占位符表名 'default_table'" in content:
            improvements_found.append("default_table映射处理")
        if "_get_available_salary_tables" in content:
            improvements_found.append("可用表列表获取")
        if "🔧 [P2修复] 安全的表存在性检查" in content:
            improvements_found.append("安全表存在性检查")
        if "表 {request.table_name} 不存在，且未找到相似表" in content:
            improvements_found.append("详细错误信息")
            
        if len(improvements_found) >= 3:
            print(f"✅ 数据刷新错误处理已优化: {', '.join(improvements_found)}")
            return True
        else:
            print(f"❌ 数据刷新错误处理优化不完整: 只找到 {improvements_found}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_main_window_reference_fix():
    """测试主窗口引用获取机制改进"""
    print("\n🔍 测试主窗口引用获取机制改进...")
    
    try:
        main_window_file = project_root / "src" / "gui" / "prototype" / "prototype_main_window.py"
        
        with open(main_window_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        improvements_found = []
        
        # 检查1: 是否有多种获取方式
        if "🔧 [P2修复] 多种方式尝试获取主窗口引用" in content:
            improvements_found.append("多种获取方式")
        if "通过QApplication获取主窗口" in content:
            improvements_found.append("QApplication方式")
        if "扩展主窗口识别条件" in content:
            improvements_found.append("扩展识别条件")
        if "🔧 [P2修复] 优先使用本地处理，减少对主窗口的依赖" in content:
            improvements_found.append("减少主窗口依赖")
            
        if len(improvements_found) >= 3:
            print(f"✅ 主窗口引用获取机制已改进: {', '.join(improvements_found)}")
            return True
        else:
            print(f"❌ 主窗口引用获取机制改进不完整: 只找到 {improvements_found}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_cache_system_initialization_fix():
    """测试缓存系统初始化优化"""
    print("\n🔍 测试缓存系统初始化优化...")
    
    try:
        main_window_file = project_root / "src" / "gui" / "prototype" / "prototype_main_window.py"
        
        with open(main_window_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        improvements_found = []
        
        # 检查1: 是否有精确异常处理
        if "🔧 [P2修复] 创建或获取缓存加载器实例（添加精确异常处理）" in content:
            improvements_found.append("精确异常处理")
        if "缓存加载器初始化失败" in content:
            improvements_found.append("初始化失败处理")
        if "🔧 [P2修复] 使用优化缓存加载数据（添加安全检查）" in content:
            improvements_found.append("安全检查")
        if "优化缓存查询失败" in content:
            improvements_found.append("查询失败处理")
            
        if len(improvements_found) >= 3:
            print(f"✅ 缓存系统初始化已优化: {', '.join(improvements_found)}")
            return True
        else:
            print(f"❌ 缓存系统初始化优化不完整: 只找到 {improvements_found}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_format_renderer_fallback_fix():
    """测试格式渲染器的降级处理改进"""
    print("\n🔍 测试格式渲染器的降级处理改进...")
    
    try:
        format_renderer_file = project_root / "src" / "modules" / "format_management" / "format_renderer.py"
        
        with open(format_renderer_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        improvements_found = []
        
        # 检查1: 是否有智能字段匹配
        if "🔧 [P2修复] 智能字段匹配，支持中英文字段名映射" in content:
            improvements_found.append("智能字段匹配")
        if "_find_mapped_field_name" in content:
            improvements_found.append("字段名映射方法")
        if "字段映射成功" in content:
            improvements_found.append("映射成功日志")
        if "🔧 [P2修复] 改进降级处理：提供更详细的诊断信息" in content:
            improvements_found.append("详细诊断信息")
        if "common_field_mappings" in content:
            improvements_found.append("通用字段映射表")
            
        if len(improvements_found) >= 4:
            print(f"✅ 格式渲染器降级处理已改进: {', '.join(improvements_found)}")
            return True
        else:
            print(f"❌ 格式渲染器降级处理改进不完整: 只找到 {improvements_found}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_code_syntax_after_p2_fixes():
    """测试P2修复后的代码语法"""
    print("\n🔍 测试P2修复后的代码语法...")
    
    try:
        # 测试主要修改的文件
        files_to_check = [
            "src/gui/prototype/prototype_main_window.py",
            "src/core/unified_data_request_manager.py",
            "src/modules/format_management/format_renderer.py"
        ]
        
        for file_path in files_to_check:
            full_path = project_root / file_path
            if not full_path.exists():
                print(f"❌ 文件不存在: {file_path}")
                return False
                
            with open(full_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 检查语法
            import ast
            try:
                ast.parse(content)
                print(f"✅ {file_path} 语法检查通过")
            except SyntaxError as e:
                print(f"❌ {file_path} 语法错误: {e}")
                return False
        
        print("✅ 所有P2修复文件语法检查通过")
        return True
        
    except Exception as e:
        print(f"❌ 语法检查失败: {e}")
        return False

def generate_p2_test_report(results):
    """生成P2测试报告"""
    print("\n" + "="*60)
    print("📊 P2级问题修复验证报告")
    print("="*60)
    
    total_tests = len(results)
    passed_tests = sum(results.values())
    
    print(f"总测试数: {total_tests}")
    print(f"通过测试: {passed_tests}")
    print(f"失败测试: {total_tests - passed_tests}")
    print(f"成功率: {passed_tests/total_tests*100:.1f}%")
    
    print("\n📋 详细结果:")
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    if passed_tests == total_tests:
        print("\n🎉 所有P2级问题修复验证通过！")
        print("\n📝 修复总结:")
        print("1. ✅ 分页处理中的total_records变量未定义问题已修复")
        print("   - 添加了安全的返回值解包和默认值设置")
        print("   - 改善了分页数据加载的稳定性")
        print("\n2. ✅ 数据刷新失败的错误处理已优化")
        print("   - 改进了default_table映射和表存在性检查")
        print("   - 提供了更详细的错误信息和恢复机制")
        print("\n3. ✅ 主窗口引用获取机制已改进")
        print("   - 支持多种获取方式，减少对单一方法的依赖")
        print("   - 优先使用本地处理，提高组件独立性")
        print("\n4. ✅ 缓存系统初始化已优化")
        print("   - 添加了精确的异常处理和安全检查")
        print("   - 改善了缓存系统的健壮性")
        print("\n5. ✅ 格式渲染器的降级处理已改进")
        print("   - 支持智能字段匹配和中英文字段名映射")
        print("   - 提供了更详细的诊断信息")
        print("\n🎯 建议: 可以进行实际运行测试验证系统稳定性提升")
    else:
        print("\n❌ 部分测试失败，需要进一步检查和修复")
    
    return passed_tests == total_tests

def main():
    """主测试函数"""
    print("🚀 开始P2级问题修复验证测试\n")
    
    # 运行所有测试
    results = {
        "total_records变量修复": test_total_records_variable_fix(),
        "数据刷新错误处理优化": test_data_refresh_error_handling_fix(),
        "主窗口引用获取改进": test_main_window_reference_fix(),
        "缓存系统初始化优化": test_cache_system_initialization_fix(),
        "格式渲染器降级处理改进": test_format_renderer_fallback_fix(),
        "代码语法检查": test_code_syntax_after_p2_fixes()
    }
    
    # 生成报告
    success = generate_p2_test_report(results)
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
