# Tab 导航与配置说明

## 配置项（config.json / ui）

- enable_tabbed_navigation: 是否启用Tab式导航（默认 true）。
- nav_default_tab: 默认Tab，`wage` 或 `change`（默认 `wage`）。
- nav_expand_latest_on_start: 启动时是否自动展开“最新期”（默认 true）。
- nav_expand_change_latest_on_start: 启动时是否为“异动表”也预展开“最新期”（默认 true）。

## 使用说明

- 窄屏 xs/sm：左侧为抽屉模式，点击 Header 的“☰ 导航”或按 Ctrl+B 展开；选中后自动收起。
- 宽屏 md+：左侧固定显示；Tab 文本显示“工资表/异动表”。
- 导入完成后：系统自动刷新两个Tab的导航树，确保新数据可见。

## 状态持久化

- 全局：当前Tab会持久化到 `state/unified_state.json` 中，下次启动自动恢复。
- 导航：两个Tab的导航状态分别保存到：
  - `state/user/navigation_state_wage.json`
  - `state/user/navigation_state_change.json`


