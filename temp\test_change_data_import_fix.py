#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
异动表导入修复验证测试

模拟完整的异动表导入流程，验证P0修复是否解决了导入失败问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_change_data_table_creation():
    """测试异动表创建流程"""
    print("🔧 [P0修复] 测试异动表创建流程")
    print("-" * 60)
    
    try:
        from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
        from src.modules.data_storage.database_manager import get_database_manager
        from src.modules.system_config.config_manager import ConfigManager
        
        # 🔧 [P0修复] 初始化管理器 - 修正参数顺序
        config_manager = ConfigManager()
        db_manager = get_database_manager()
        table_manager = DynamicTableManager(db_manager, config_manager)
        
        # 模拟用户Excel数据
        test_table_name = "change_data_2025_12_test_employees"
        test_columns = [
            "工号", "姓名", "部门名称", "2025年岗位工资", "2025年薪级工资", 
            "津贴", "应发工资", "data_source", "import_time"
        ]
        
        print(f"   测试表名: {test_table_name}")
        print(f"   用户字段数: {len(test_columns)}")
        print(f"   用户字段: {test_columns}")
        
        # 尝试创建灵活异动表
        success = table_manager.create_flexible_change_data_table(
            table_name=test_table_name,
            columns=test_columns,
            user_selected=True
        )
        
        if success:
            print(f"   ✅ 异动表创建成功: {test_table_name}")
            
            # 验证表是否真的存在
            if db_manager.table_exists(test_table_name):
                print(f"   ✅ 表确实存在于数据库中")
                
                # 获取表结构信息
                table_info = db_manager.get_table_info(test_table_name)
                print(f"   ✅ 表结构信息获取成功，字段数: {len(table_info)}")
                
                print(f"   表字段详情:")
                for i, field in enumerate(table_info):
                    field_name = field.get('name', 'unknown')
                    field_type = field.get('type', 'unknown')
                    is_nullable = "可空" if field.get('notnull', 0) == 0 else "非空"
                    print(f"     {i+1}. {field_name} ({field_type}) - {is_nullable}")
                
                return True
            else:
                print(f"   ❌ 表创建成功但在数据库中不存在")
                return False
        else:
            print(f"   ❌ 异动表创建失败")
            return False
            
    except Exception as e:
        print(f"   ❌ 异动表创建测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_get_all_table_names():
    """测试获取所有表名功能"""
    print("\n🔧 [P0修复] 测试获取所有表名功能")
    print("-" * 60)
    
    try:
        from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
        from src.modules.data_storage.database_manager import get_database_manager
        from src.modules.system_config.config_manager import ConfigManager
        
        # 初始化管理器
        config_manager = ConfigManager()
        db_manager = get_database_manager()
        table_manager = DynamicTableManager(db_manager, config_manager)
        
        # 测试get_all_table_names方法
        all_tables = table_manager.get_all_table_names()
        
        print(f"   数据库中的表数量: {len(all_tables)}")
        print(f"   表列表:")
        
        change_tables = []
        salary_tables = []
        system_tables = []
        
        for table in all_tables:
            if table.startswith('change_data_'):
                change_tables.append(table)
                print(f"     🔄 {table} (异动表)")
            elif table.startswith('salary_data_'):
                salary_tables.append(table)
                print(f"     💰 {table} (工资表)")
            else:
                system_tables.append(table)
                print(f"     ⚙️  {table} (系统表)")
        
        print(f"   统计:")
        print(f"     异动表: {len(change_tables)} 个")
        print(f"     工资表: {len(salary_tables)} 个")
        print(f"     系统表: {len(system_tables)} 个")
        
        # 验证是否包含我们刚创建的测试表
        test_table = "change_data_2025_12_test_employees"
        if test_table in all_tables:
            print(f"   ✅ 找到测试异动表: {test_table}")
        else:
            print(f"   ⚠️  未找到测试异动表: {test_table}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 获取所有表名测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_table_metadata_recording():
    """测试表元数据记录功能"""
    print("\n🔧 [P0修复] 测试表元数据记录功能")
    print("-" * 60)
    
    try:
        from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
        from src.modules.data_storage.database_manager import get_database_manager
        from src.modules.system_config.config_manager import ConfigManager
        
        # 初始化管理器
        config_manager = ConfigManager()
        db_manager = get_database_manager()
        table_manager = DynamicTableManager(db_manager, config_manager)
        
        # 获取异动表元数据
        change_metadata = table_manager.get_table_list(table_type='change_data')
        
        print(f"   异动表元数据数量: {len(change_metadata)}")
        
        if change_metadata:
            print(f"   异动表元数据详情:")
            for i, metadata in enumerate(change_metadata):
                table_name = metadata.get('table_name', 'unknown')
                table_type = metadata.get('table_type', 'unknown')
                created_at = metadata.get('created_at', 'unknown')
                print(f"     {i+1}. {table_name}")
                print(f"        类型: {table_type}")
                print(f"        创建时间: {created_at}")
                
                # 检查是否有额外的元数据
                if 'metadata' in metadata:
                    extra_meta = metadata['metadata']
                    if isinstance(extra_meta, dict):
                        print(f"        额外信息: {extra_meta}")
            
            return True
        else:
            print(f"   ⚠️  未找到任何异动表元数据")
            return True  # 这不算失败，可能是第一次运行
            
    except Exception as e:
        print(f"   ❌ 表元数据记录测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def cleanup_test_table():
    """清理测试表"""
    print("\n🧹 清理测试数据")
    print("-" * 60)
    
    try:
        from src.modules.data_storage.database_manager import get_database_manager
        
        db_manager = get_database_manager()
        test_table = "change_data_2025_12_test_employees"
        
        if db_manager.table_exists(test_table):
            # 删除测试表
            drop_sql = f"DROP TABLE IF EXISTS {test_table}"
            db_manager.execute_query(drop_sql)
            print(f"   ✅ 测试表已删除: {test_table}")
        else:
            print(f"   ℹ️  测试表不存在，无需删除: {test_table}")
            
        return True
        
    except Exception as e:
        print(f"   ❌ 清理测试表失败: {e}")
        return False

if __name__ == "__main__":
    print("🔧 [P0修复] 开始异动表导入修复验证测试...")
    print("=" * 80)
    
    success1 = test_change_data_table_creation()
    success2 = test_get_all_table_names()
    success3 = test_table_metadata_recording()
    cleanup_success = cleanup_test_table()
    
    print("\n" + "=" * 80)
    if success1 and success2 and success3:
        print("✅ 所有异动表导入修复验证测试通过！")
        print("\n🎯 P0修复验证结果：")
        print("   ✅ 异动表创建流程正常")
        print("   ✅ 获取所有表名功能正常")
        print("   ✅ 表元数据记录功能正常")
        print(f"   {'✅' if cleanup_success else '⚠️'} 测试数据清理{'成功' if cleanup_success else '部分成功'}")
        print("\n🚀 异动表导入功能已完全恢复！用户现在可以正常导入异动表数据。")
    else:
        print("❌ 部分异动表导入修复验证测试失败")
        print(f"   异动表创建: {'✅' if success1 else '❌'}")
        print(f"   获取表名功能: {'✅' if success2 else '❌'}")
        print(f"   元数据记录: {'✅' if success3 else '❌'}")
        print(f"   测试数据清理: {'✅' if cleanup_success else '❌'}")
