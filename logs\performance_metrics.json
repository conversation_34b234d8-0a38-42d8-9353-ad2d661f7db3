{"session_start_time": "2025-08-15T19:44:06.177176", "last_updated": "2025-08-15T19:49:50.345335", "metrics": [{"operation_type": "render", "data_size": 50, "render_time_ms": 10.783195495605469, "strategy_used": "small_dataset", "user_wait_time_ms": 10.783195495605469, "timestamp": "2025-08-15T19:44:06.177721", "table_name": "", "additional_info": {"avg_ms_per_row": 0.21566390991210938}}, {"operation_type": "render", "data_size": 13, "render_time_ms": 9.868144989013672, "strategy_used": "small_dataset", "user_wait_time_ms": 9.868144989013672, "timestamp": "2025-08-15T19:44:06.870394", "table_name": "", "additional_info": {"avg_ms_per_row": 0.7590880760779748}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 12.940645217895508, "strategy_used": "small_dataset", "user_wait_time_ms": 12.940645217895508, "timestamp": "2025-08-15T19:44:07.301025", "table_name": "", "additional_info": {"avg_ms_per_row": 0.25881290435791016}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 12.056589126586914, "strategy_used": "small_dataset", "user_wait_time_ms": 12.056589126586914, "timestamp": "2025-08-15T19:44:08.448631", "table_name": "", "additional_info": {"avg_ms_per_row": 0.24113178253173828}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 14.963388442993164, "strategy_used": "small_dataset", "user_wait_time_ms": 14.963388442993164, "timestamp": "2025-08-15T19:44:09.431152", "table_name": "", "additional_info": {"avg_ms_per_row": 0.2992677688598633}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 17.04573631286621, "strategy_used": "small_dataset", "user_wait_time_ms": 17.04573631286621, "timestamp": "2025-08-15T19:44:29.484017", "table_name": "", "additional_info": {"avg_ms_per_row": 0.3409147262573242}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 16.176939010620117, "strategy_used": "small_dataset", "user_wait_time_ms": 16.176939010620117, "timestamp": "2025-08-15T19:44:32.942375", "table_name": "", "additional_info": {"avg_ms_per_row": 0.32353878021240234}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 14.015913009643555, "strategy_used": "small_dataset", "user_wait_time_ms": 14.015913009643555, "timestamp": "2025-08-15T19:44:42.932887", "table_name": "", "additional_info": {"avg_ms_per_row": 0.2803182601928711}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 19.906997680664062, "strategy_used": "small_dataset", "user_wait_time_ms": 19.906997680664062, "timestamp": "2025-08-15T19:44:51.021797", "table_name": "", "additional_info": {"avg_ms_per_row": 0.39813995361328125}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 15.301704406738281, "strategy_used": "small_dataset", "user_wait_time_ms": 15.301704406738281, "timestamp": "2025-08-15T19:45:01.458050", "table_name": "", "additional_info": {"avg_ms_per_row": 0.3060340881347656}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 16.215801239013672, "strategy_used": "small_dataset", "user_wait_time_ms": 16.215801239013672, "timestamp": "2025-08-15T19:45:01.698246", "table_name": "", "additional_info": {"avg_ms_per_row": 0.32431602478027344}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 15.829086303710938, "strategy_used": "small_dataset", "user_wait_time_ms": 15.829086303710938, "timestamp": "2025-08-15T19:45:02.173128", "table_name": "", "additional_info": {"avg_ms_per_row": 0.31658172607421875}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 13.570070266723633, "strategy_used": "small_dataset", "user_wait_time_ms": 13.570070266723633, "timestamp": "2025-08-15T19:45:02.512724", "table_name": "", "additional_info": {"avg_ms_per_row": 0.27140140533447266}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 10.786771774291992, "strategy_used": "small_dataset", "user_wait_time_ms": 10.786771774291992, "timestamp": "2025-08-15T19:45:06.873314", "table_name": "", "additional_info": {"avg_ms_per_row": 0.21573543548583984}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 13.500690460205078, "strategy_used": "small_dataset", "user_wait_time_ms": 13.500690460205078, "timestamp": "2025-08-15T19:45:11.480216", "table_name": "", "additional_info": {"avg_ms_per_row": 0.27001380920410156}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 9.766340255737305, "strategy_used": "small_dataset", "user_wait_time_ms": 9.766340255737305, "timestamp": "2025-08-15T19:45:18.577873", "table_name": "", "additional_info": {"avg_ms_per_row": 0.1953268051147461}}, {"operation_type": "render", "data_size": 2, "render_time_ms": 1.0557174682617188, "strategy_used": "small_dataset", "user_wait_time_ms": 1.0557174682617188, "timestamp": "2025-08-15T19:45:26.999914", "table_name": "", "additional_info": {"avg_ms_per_row": 0.5278587341308594}}, {"operation_type": "render", "data_size": 2, "render_time_ms": 1.306295394897461, "strategy_used": "small_dataset", "user_wait_time_ms": 1.306295394897461, "timestamp": "2025-08-15T19:45:42.043843", "table_name": "", "additional_info": {"avg_ms_per_row": 0.6531476974487305}}, {"operation_type": "render", "data_size": 2, "render_time_ms": 1.0287761688232422, "strategy_used": "small_dataset", "user_wait_time_ms": 1.0287761688232422, "timestamp": "2025-08-15T19:45:47.051327", "table_name": "", "additional_info": {"avg_ms_per_row": 0.5143880844116211}}, {"operation_type": "render", "data_size": 2, "render_time_ms": 1.0342597961425781, "strategy_used": "small_dataset", "user_wait_time_ms": 1.0342597961425781, "timestamp": "2025-08-15T19:45:48.145836", "table_name": "", "additional_info": {"avg_ms_per_row": 0.5171298980712891}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 15.2130126953125, "strategy_used": "small_dataset", "user_wait_time_ms": 15.2130126953125, "timestamp": "2025-08-15T19:46:44.756406", "table_name": "", "additional_info": {"avg_ms_per_row": 0.30426025390625}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 15.630245208740234, "strategy_used": "small_dataset", "user_wait_time_ms": 15.630245208740234, "timestamp": "2025-08-15T19:46:45.220447", "table_name": "", "additional_info": {"avg_ms_per_row": 0.3126049041748047}}, {"operation_type": "render", "data_size": 2, "render_time_ms": 1.0371208190917969, "strategy_used": "small_dataset", "user_wait_time_ms": 1.0371208190917969, "timestamp": "2025-08-15T19:46:45.494865", "table_name": "", "additional_info": {"avg_ms_per_row": 0.5185604095458984}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 20.550251007080078, "strategy_used": "small_dataset", "user_wait_time_ms": 20.550251007080078, "timestamp": "2025-08-15T19:46:45.816116", "table_name": "", "additional_info": {"avg_ms_per_row": 0.41100502014160156}}, {"operation_type": "render", "data_size": 2, "render_time_ms": 1.0428428649902344, "strategy_used": "small_dataset", "user_wait_time_ms": 1.0428428649902344, "timestamp": "2025-08-15T19:46:46.099366", "table_name": "", "additional_info": {"avg_ms_per_row": 0.5214214324951172}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 14.537334442138672, "strategy_used": "small_dataset", "user_wait_time_ms": 14.537334442138672, "timestamp": "2025-08-15T19:46:46.508596", "table_name": "", "additional_info": {"avg_ms_per_row": 0.29074668884277344}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 16.126632690429688, "strategy_used": "small_dataset", "user_wait_time_ms": 16.126632690429688, "timestamp": "2025-08-15T19:46:47.578394", "table_name": "", "additional_info": {"avg_ms_per_row": 0.32253265380859375}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 12.432575225830078, "strategy_used": "small_dataset", "user_wait_time_ms": 12.432575225830078, "timestamp": "2025-08-15T19:46:48.460943", "table_name": "", "additional_info": {"avg_ms_per_row": 0.24865150451660156}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 8.860588073730469, "strategy_used": "small_dataset", "user_wait_time_ms": 8.860588073730469, "timestamp": "2025-08-15T19:47:10.098574", "table_name": "", "additional_info": {"avg_ms_per_row": 0.17721176147460938}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 8.74185562133789, "strategy_used": "small_dataset", "user_wait_time_ms": 8.74185562133789, "timestamp": "2025-08-15T19:47:19.555315", "table_name": "", "additional_info": {"avg_ms_per_row": 0.1748371124267578}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 14.91999626159668, "strategy_used": "small_dataset", "user_wait_time_ms": 14.91999626159668, "timestamp": "2025-08-15T19:47:26.947223", "table_name": "", "additional_info": {"avg_ms_per_row": 0.2983999252319336}}, {"operation_type": "render", "data_size": 2, "render_time_ms": 1.0409355163574219, "strategy_used": "small_dataset", "user_wait_time_ms": 1.0409355163574219, "timestamp": "2025-08-15T19:47:27.154091", "table_name": "", "additional_info": {"avg_ms_per_row": 0.5204677581787109}}, {"operation_type": "render", "data_size": 2, "render_time_ms": 1.0607242584228516, "strategy_used": "small_dataset", "user_wait_time_ms": 1.0607242584228516, "timestamp": "2025-08-15T19:47:27.602301", "table_name": "", "additional_info": {"avg_ms_per_row": 0.5303621292114258}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 14.04428482055664, "strategy_used": "small_dataset", "user_wait_time_ms": 14.04428482055664, "timestamp": "2025-08-15T19:47:34.065065", "table_name": "", "additional_info": {"avg_ms_per_row": 0.2808856964111328}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 9.703397750854492, "strategy_used": "small_dataset", "user_wait_time_ms": 9.703397750854492, "timestamp": "2025-08-15T19:47:42.601210", "table_name": "", "additional_info": {"avg_ms_per_row": 0.19406795501708984}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 5.787849426269531, "strategy_used": "small_dataset", "user_wait_time_ms": 5.787849426269531, "timestamp": "2025-08-15T19:47:45.183382", "table_name": "", "additional_info": {"avg_ms_per_row": 0.11575698852539062}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 22.634267807006836, "strategy_used": "small_dataset", "user_wait_time_ms": 22.634267807006836, "timestamp": "2025-08-15T19:47:54.611844", "table_name": "", "additional_info": {"avg_ms_per_row": 0.4526853561401367}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 9.928226470947266, "strategy_used": "small_dataset", "user_wait_time_ms": 9.928226470947266, "timestamp": "2025-08-15T19:48:00.668322", "table_name": "", "additional_info": {"avg_ms_per_row": 0.1985645294189453}}, {"operation_type": "render", "data_size": 2, "render_time_ms": 0.0, "strategy_used": "small_dataset", "user_wait_time_ms": 0.0, "timestamp": "2025-08-15T19:48:13.195390", "table_name": "", "additional_info": {"avg_ms_per_row": 0.0}}, {"operation_type": "render", "data_size": 13, "render_time_ms": 3.1287670135498047, "strategy_used": "small_dataset", "user_wait_time_ms": 3.1287670135498047, "timestamp": "2025-08-15T19:48:17.358470", "table_name": "", "additional_info": {"avg_ms_per_row": 0.2406743856576773}}, {"operation_type": "render", "data_size": 13, "render_time_ms": 3.080606460571289, "strategy_used": "small_dataset", "user_wait_time_ms": 3.080606460571289, "timestamp": "2025-08-15T19:48:36.608019", "table_name": "", "additional_info": {"avg_ms_per_row": 0.236969727736253}}, {"operation_type": "render", "data_size": 13, "render_time_ms": 5.246162414550781, "strategy_used": "small_dataset", "user_wait_time_ms": 5.246162414550781, "timestamp": "2025-08-15T19:48:40.165635", "table_name": "", "additional_info": {"avg_ms_per_row": 0.4035509549654447}}, {"operation_type": "render", "data_size": 2, "render_time_ms": 1.0297298431396484, "strategy_used": "small_dataset", "user_wait_time_ms": 1.0297298431396484, "timestamp": "2025-08-15T19:49:03.901968", "table_name": "", "additional_info": {"avg_ms_per_row": 0.5148649215698242}}, {"operation_type": "render", "data_size": 2, "render_time_ms": 0.7615089416503906, "strategy_used": "small_dataset", "user_wait_time_ms": 0.7615089416503906, "timestamp": "2025-08-15T19:49:06.050627", "table_name": "", "additional_info": {"avg_ms_per_row": 0.3807544708251953}}, {"operation_type": "render", "data_size": 2, "render_time_ms": 1.0371208190917969, "strategy_used": "small_dataset", "user_wait_time_ms": 1.0371208190917969, "timestamp": "2025-08-15T19:49:11.034621", "table_name": "", "additional_info": {"avg_ms_per_row": 0.5185604095458984}}, {"operation_type": "render", "data_size": 13, "render_time_ms": 2.058267593383789, "strategy_used": "small_dataset", "user_wait_time_ms": 2.058267593383789, "timestamp": "2025-08-15T19:49:13.812651", "table_name": "", "additional_info": {"avg_ms_per_row": 0.15832827641413763}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 6.805181503295898, "strategy_used": "small_dataset", "user_wait_time_ms": 6.805181503295898, "timestamp": "2025-08-15T19:49:16.350828", "table_name": "", "additional_info": {"avg_ms_per_row": 0.13610363006591797}}, {"operation_type": "render", "data_size": 2, "render_time_ms": 0.0, "strategy_used": "small_dataset", "user_wait_time_ms": 0.0, "timestamp": "2025-08-15T19:49:19.331469", "table_name": "", "additional_info": {"avg_ms_per_row": 0.0}}, {"operation_type": "render", "data_size": 13, "render_time_ms": 1.0378360748291016, "strategy_used": "small_dataset", "user_wait_time_ms": 1.0378360748291016, "timestamp": "2025-08-15T19:49:41.750229", "table_name": "", "additional_info": {"avg_ms_per_row": 0.0798335442176232}}, {"operation_type": "render", "data_size": 50, "render_time_ms": 7.780313491821289, "strategy_used": "small_dataset", "user_wait_time_ms": 7.780313491821289, "timestamp": "2025-08-15T19:49:50.344061", "table_name": "", "additional_info": {"avg_ms_per_row": 0.15560626983642578}}], "stats": {"total_operations": 50, "avg_render_time": 9.04821395874024, "max_render_time": 22.634267807006836, "min_render_time": 0.0, "operations_by_type": {"render": {"count": 50, "avg_time": 9.048213958740234, "total_time": 452.4106979370117}}, "performance_improvements": {}}}