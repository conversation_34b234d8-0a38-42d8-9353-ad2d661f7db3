#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证兼容性修复效果
测试字段名兼容性和默认表选择功能
"""

import sys
import os
import sqlite3
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))

def test_database_state():
    """测试数据库状态"""
    print("🔍 测试数据库状态...")
    
    db_path = "data/db/salary_system.db"
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查工资表元数据
        cursor.execute("SELECT table_name, table_type FROM table_metadata WHERE table_type='salary_data';")
        salary_metadata = cursor.fetchall()
        print(f"📊 salary_data类型元数据: {len(salary_metadata)} 个")
        
        # 检查异动表元数据
        cursor.execute("SELECT table_name, table_type FROM table_metadata WHERE table_type='change_data';")
        change_metadata = cursor.fetchall()
        print(f"📊 change_data类型元数据: {len(change_metadata)} 个")
        
        conn.close()
        
        if len(salary_metadata) > 0 and len(change_metadata) > 0:
            print("✅ 数据库状态正常：有工资表和异动表数据")
            return True
        else:
            print(f"❌ 数据库状态异常：工资表{len(salary_metadata)}个，异动表{len(change_metadata)}个")
            return False
        
    except Exception as e:
        print(f"❌ 测试数据库状态失败: {e}")
        return False

def test_field_compatibility():
    """测试字段兼容性"""
    print("\n🔍 测试字段兼容性...")
    
    try:
        from modules.data_storage.database_manager import DatabaseManager
        from modules.data_storage.dynamic_table_manager import DynamicTableManager
        
        # 初始化管理器
        db_manager = DatabaseManager()
        table_manager = DynamicTableManager(db_manager)
        
        # 测试获取工资表
        salary_tables = table_manager.get_table_list(table_type='salary_data')
        print(f"📊 找到工资表: {len(salary_tables)} 个")
        
        if salary_tables:
            first_table = salary_tables[0]
            print(f"📄 第一个表信息:")
            print(f"  table_name: {first_table.get('table_name', 'N/A')}")
            print(f"  name: {first_table.get('name', 'N/A')}")
            print(f"  table_type: {first_table.get('table_type', 'N/A')}")
            
            # 验证兼容性
            if first_table.get('table_name') == first_table.get('name'):
                print("✅ 字段兼容性正常：table_name 和 name 字段一致")
                compatibility_ok = True
            else:
                print("❌ 字段兼容性异常：table_name 和 name 字段不一致")
                compatibility_ok = False
        else:
            print("❌ 未找到工资表")
            compatibility_ok = False
        
        # 测试获取异动表
        change_tables = table_manager.get_table_list(table_type='change_data')
        print(f"📊 找到异动表: {len(change_tables)} 个")
        
        if change_tables:
            first_change_table = change_tables[0]
            print(f"📄 第一个异动表信息:")
            print(f"  table_name: {first_change_table.get('table_name', 'N/A')}")
            print(f"  name: {first_change_table.get('name', 'N/A')}")
            print(f"  display_name: {first_change_table.get('display_name', 'N/A')}")
            
            # 验证兼容性
            if first_change_table.get('table_name') == first_change_table.get('name'):
                print("✅ 异动表字段兼容性正常")
                change_compatibility_ok = True
            else:
                print("❌ 异动表字段兼容性异常")
                change_compatibility_ok = False
        else:
            print("❌ 未找到异动表")
            change_compatibility_ok = False
        
        return compatibility_ok and change_compatibility_ok
        
    except Exception as e:
        print(f"❌ 测试字段兼容性失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_default_table_selection():
    """测试默认表选择功能"""
    print("\n🔍 测试默认表选择功能...")
    
    try:
        from modules.data_storage.database_manager import DatabaseManager
        from modules.data_storage.dynamic_table_manager import DynamicTableManager
        
        # 初始化管理器
        db_manager = DatabaseManager()
        table_manager = DynamicTableManager(db_manager)
        
        # 模拟 _get_default_table_name 的逻辑
        tables = table_manager.get_table_list(table_type='salary_data')
        if tables:
            # 优先选择包含"active_employees"的表
            default_table = None
            for table in tables:
                table_name = table.get('name', '')  # 使用兼容字段名
                if 'active_employees' in table_name:
                    default_table = table_name
                    print(f"✅ 找到默认表: {default_table}")
                    break
            
            if not default_table:
                # 如果没有找到active_employees表，返回第一个表
                first_table = tables[0].get('name', '')
                if first_table:
                    default_table = first_table
                    print(f"✅ 使用第一个可用表: {default_table}")
            
            if default_table:
                print("✅ 默认表选择功能正常")
                return True
            else:
                print("❌ 无法选择默认表")
                return False
        else:
            print("ℹ️  暂无工资表数据，默认表选择功能无法测试")
            return True  # 这是正常情况
        
    except Exception as e:
        print(f"❌ 测试默认表选择失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_table_name_pattern_matching():
    """测试表名模式匹配功能"""
    print("\n🔍 测试表名模式匹配功能...")
    
    try:
        from modules.data_storage.database_manager import DatabaseManager
        from modules.data_storage.dynamic_table_manager import DynamicTableManager
        
        # 初始化管理器
        db_manager = DatabaseManager()
        table_manager = DynamicTableManager(db_manager)
        
        # 获取所有工资表
        tables = table_manager.get_table_list(table_type='salary_data')
        if tables:
            # 模拟表名模式匹配逻辑
            all_tables = [table.get('name', '') for table in tables if table.get('name')]
            print(f"📋 所有表名: {all_tables}")
            
            # 测试模式匹配
            pattern = "active_employees"
            matching_tables = [table for table in all_tables if pattern in table]
            print(f"📋 匹配 '{pattern}' 的表: {matching_tables}")
            
            if matching_tables:
                print("✅ 表名模式匹配功能正常")
                return True
            else:
                print("❌ 表名模式匹配功能异常")
                return False
        else:
            print("ℹ️  暂无工资表数据，表名模式匹配功能无法测试")
            return True  # 这是正常情况
        
    except Exception as e:
        print(f"❌ 测试表名模式匹配失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始验证兼容性修复效果...\n")
    
    results = []
    
    # 执行各项测试
    results.append(("数据库状态", test_database_state()))
    results.append(("字段兼容性", test_field_compatibility()))
    results.append(("默认表选择", test_default_table_selection()))
    results.append(("表名模式匹配", test_table_name_pattern_matching()))
    
    # 汇总结果
    print("\n" + "="*60)
    print("📊 兼容性修复验证结果汇总:")
    print("="*60)
    
    success_count = 0
    for name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{name}: {status}")
        if result:
            success_count += 1
    
    print(f"\n总体结果: {success_count}/{len(results)} 项测试通过")
    
    if success_count == len(results):
        print("\n🎉 所有兼容性修复验证通过！")
        print("\n📋 修复成果总结:")
        print("1. ✅ 字段兼容性：同时提供 table_name 和 name 字段")
        print("2. ✅ 默认表选择：能正确使用 name 字段选择默认表")
        print("3. ✅ 表名模式匹配：能正确使用 name 字段进行匹配")
        print("4. ✅ 系统提示优化：改进启动时的日志信息")
        print("\n🔄 建议重启系统验证完整效果。")
        return True
    else:
        print(f"\n❌ 兼容性修复验证失败，有 {len(results) - success_count} 项未通过验证。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
