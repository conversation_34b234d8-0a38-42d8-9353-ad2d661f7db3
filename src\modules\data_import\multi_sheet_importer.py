"""
多Sheet Excel导入器

功能说明:
- 支持多个Sheet的Excel文件导入
- 自动字段映射和数据合并
- 支持动态表结构扩展
- 灵活的Sheet配置管理
- 字段变化的自动适应

创建时间: 2025-01-20
"""

import pandas as pd
from typing import Dict, List, Any, Optional, Tuple, Union
from pathlib import Path
from datetime import datetime
import json

from src.utils.log_config import setup_logger
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager, ColumnDefinition
from .excel_importer import ExcelImporter
from .field_mapper import FieldMapper
from .data_validator import DataValidator, ValidationRule, FieldType
from .auto_field_mapping_generator import AutoFieldMappingGenerator
from .config_sync_manager import ConfigSyncManager
from .specialized_field_mapping_generator import SpecializedFieldMappingGenerator

class MultiSheetImporter:
    """多Sheet Excel导入器
    
    支持复杂Excel文件的多Sheet数据导入，包括字段映射、数据验证和动态表创建
    """
    
    def __init__(self, dynamic_table_manager: DynamicTableManager, config_file: Optional[str] = None):
        """初始化多Sheet导入器
        
        Args:
            dynamic_table_manager: 动态表管理器实例
            config_file: 配置文件路径，包含Sheet映射规则
        """
        base_logger = setup_logger(__name__)
        # 绑定组件上下文，便于排查（不含敏感数据）
        from src.utils.logging_utils import bind_context
        self.logger = bind_context(base_logger, component="MultiSheetImporter")
        self.excel_importer = ExcelImporter()
        self.field_mapper = FieldMapper()
        self.data_validator = DataValidator()
        self.table_manager = dynamic_table_manager
        
        # 初始化自动映射组件
        self.auto_mapping_generator = AutoFieldMappingGenerator()
        self.specialized_mapping_generator = SpecializedFieldMappingGenerator()
        # 🆕 [新架构] 通过依赖注入获取ConfigSyncManager
        # 注意：这里需要通过架构工厂获取，暂时设为None，在使用时再获取
        self.config_sync_manager = None

        # 初始化专用模板管理器
        from src.modules.system_config.specialized_table_templates import SpecializedTableTemplates
        self.specialized_templates = SpecializedTableTemplates()
        
        # 加载配置
        self.sheet_configs = self._load_sheet_configs(config_file)
        self.global_config = self._load_global_config()
        
        # 导入统计
        self.import_stats = {
            "total_sheets": 0,
            "processed_sheets": 0,
            "total_records": 0,
            "failed_records": 0,
            "validation_errors": 0
        }
        
        self.logger.info("多Sheet导入器初始化完成")

    def set_config_sync_manager(self, config_sync_manager):
        """设置配置同步管理器

        Args:
            config_sync_manager: 配置同步管理器实例
        """
        self.config_sync_manager = config_sync_manager
        self.logger.info("🔧 [修复] ConfigSyncManager已设置到MultiSheetImporter")
    
    def _load_global_config(self) -> Dict[str, Any]:
        """加载全局配置
        
        Returns:
            全局配置字典
        """
        try:
            config_path = Path("config.json")
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    self.logger.debug("全局配置加载成功")
                    return config
        except Exception as e:
            self.logger.warning(f"全局配置加载失败，使用默认配置: {e}")
        
        # 返回默认全局配置
        return {
            "field_mapping": {
                "auto_generate_mappings": True,
                "force_mapping_for_unknown_sheets": True,
                "default_excel_header_as_display_name": True,
                "preserve_chinese_headers": True,
                "fuzzy_match_threshold": 0.7,
                "save_auto_generated_mappings": True
            }
        }
    
    def _load_sheet_configs(self, config_file: Optional[str]) -> Dict[str, Any]:
        """加载Sheet配置
        
        Args:
            config_file: 配置文件路径
            
        Returns:
            Sheet配置字典
        """
        if config_file and Path(config_file).exists():
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                self.logger.warning(f"配置文件加载失败，使用默认配置: {e}")
        
        # 默认配置
        return {
            "import_strategy": "merge_to_single_table",  # merge_to_single_table 或 separate_tables
            "target_table_template": "salary_data",
            "sheet_mappings": {
                "基本信息": {
                    "enabled": True,
                    "field_mappings": {
                        "工号": "employee_id",
                        "姓名": "employee_name",
                        "员工姓名": "employee_name",
                        "身份证号": "id_card",
                        "部门": "department",
                        "职位": "position",
                        "岗位": "position"
                    },
                    "required_fields": ["employee_id", "employee_name"],
                    "data_type": "salary_data"
                },
                "工资明细": {
                    "enabled": True,
                    "field_mappings": {
                        "工号": "employee_id",
                        "基本工资": "basic_salary",
                        "基础工资": "basic_salary",
                        "绩效奖金": "performance_bonus",
                        "绩效": "performance_bonus",
                        "加班费": "overtime_pay",
                        "总工资": "total_salary",
                        "应发工资": "total_salary"
                    },
                    "required_fields": ["employee_id"],
                    "data_type": "salary_detail"
                },
                "津贴补贴": {
                    "enabled": True,
                    "field_mappings": {
                        "工号": "employee_id",
                        "津贴": "allowance",
                        "补贴": "allowance",
                        "交通补贴": "transportation_allowance",
                        "餐补": "meal_allowance",
                        "通讯补贴": "communication_allowance"
                    },
                    "required_fields": ["employee_id"],
                    "data_type": "allowance_detail"
                },
                "扣款明细": {
                    "enabled": True,
                    "field_mappings": {
                        "工号": "employee_id",
                        "扣款": "deduction",
                        "社保扣款": "social_security_deduction",
                        "公积金扣款": "housing_fund_deduction",
                        "个税": "income_tax",
                        "个人所得税": "income_tax"
                    },
                    "required_fields": ["employee_id"],
                    "data_type": "deduction_detail"
                }
            },
            "validation_rules": {
                "employee_id": {"required": True, "min_length": 1},
                "employee_name": {"required": True, "min_length": 2},
                "basic_salary": {"min_value": 0, "max_value": 100000},
                "performance_bonus": {"min_value": 0, "max_value": 50000}
            }
        }
    
    def import_excel_file(self, file_path: Union[str, Path], 
                         year: int, month: int,
                         target_table: Optional[str] = None,
                         target_path: Optional[str] = None) -> Dict[str, Any]:
        """导入多Sheet Excel文件
        
        Args:
            file_path: Excel文件路径
            year: 年份
            month: 月份
            target_table: 目标表名，如果为None则使用默认命名规则
            target_path: 目标路径，用于确定导入类型（如"异动人员表"）
            
        Returns:
            导入结果字典
        """
        file_path = Path(file_path)
        self.logger.info(f"开始导入多Sheet Excel文件: {file_path.name}")
        
        # 确保month是整数类型
        if isinstance(month, str):
            month = int(month)
        if isinstance(year, str):
            year = int(year)
        
        try:
            # 重置统计
            self._reset_import_stats()
            
            # 1. 验证文件并获取Sheet列表
            file_info = self.excel_importer.validate_file(file_path)
            sheet_names = self.excel_importer.get_sheet_names(file_path)
            self.import_stats["total_sheets"] = len(sheet_names)
            
            self.logger.info(f"检测到 {len(sheet_names)} 个工作表: {sheet_names}")
            
            # 2. 根据策略处理导入
            strategy = self.sheet_configs.get("import_strategy", "merge_to_single_table")
            
            if strategy == "merge_to_single_table":
                result = self._import_merge_strategy(file_path, sheet_names, year, month, target_table, target_path)
            elif strategy == "separate_tables":
                result = self._import_separate_strategy(file_path, sheet_names, year, month, target_path)
            else:
                raise ValueError(f"不支持的导入策略: {strategy}")
            
            # 3. 更新导入统计
            result.update({
                "import_stats": self.import_stats,
                "file_info": file_info,
                "processed_sheets": sheet_names
            })
            
            self.logger.info(f"多Sheet导入完成: {result}")
            return result
            
        except Exception as e:
            self.logger.error(f"多Sheet导入失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "import_stats": self.import_stats
            }
    
    def _import_merge_strategy(self, file_path: Path, sheet_names: List[str], 
                              year: int, month: int, target_table: Optional[str],
                              target_path: Optional[str] = None) -> Dict[str, Any]:
        """合并导入策略：将所有Sheet数据合并到一个表
        
        Args:
            file_path: 文件路径
            sheet_names: Sheet名称列表
            year: 年份
            month: 月份
            target_table: 目标表名
            target_path: 目标路径，用于确定导入类型
            
        Returns:
            导入结果
        """
        # 1. 逐Sheet处理数据
        processed_data = {}
        all_columns = set()
        
        for sheet_name in sheet_names:
            if not self._is_sheet_enabled(sheet_name):
                self.logger.info(f"跳过禁用的工作表: {sheet_name}")
                continue
            
            try:
                # 读取Sheet数据
                sheet_df = self.excel_importer.import_data(file_path, sheet_name=sheet_name)
                
                if sheet_df.empty:
                    self.logger.warning(f"工作表 {sheet_name} 为空，跳过")
                    continue
                
                # 处理Sheet数据
                processed_df = self._process_sheet_data(sheet_df, sheet_name, year, month)
                
                if not processed_df.empty:
                    processed_data[sheet_name] = processed_df
                    all_columns.update(processed_df.columns)
                    self.import_stats["processed_sheets"] += 1
                    
                    self.logger.info(f"工作表 {sheet_name} 处理完成: {len(processed_df)} 行")
                
            except Exception as e:
                self.logger.error(f"处理工作表 {sheet_name} 失败: {e}")
                continue
        
        if not processed_data:
            return {"success": False, "error": "没有有效的Sheet数据"}
        
        # 2. 合并所有Sheet数据
        merged_df = self._merge_sheet_data(processed_data, all_columns)
        
        if merged_df.empty:
            return {"success": False, "error": "合并后的数据为空"}
        
        # 3. 创建目标表并导入数据
        if not target_table:
            # 根据target_path决定表名前缀
            if target_path and "异动人员表" in target_path:
                table_prefix = "change_data"
            else:
                table_prefix = "salary_data"
            target_table = f"{table_prefix}_{year}_{month:02d}"
        
        # 检查是否需要扩展表结构
        additional_columns = self._detect_additional_columns(merged_df, target_table)
        
        # 根据target_path决定创建哪种表
        if target_path and "异动人员表" in target_path:
            # 创建异动表
            success = self.table_manager.create_change_data_table(
                table_name=target_table,
                columns=list(merged_df.columns)
            )
            if not success:
                return {"success": False, "error": "创建异动表失败"}
        elif additional_columns:
            # 创建包含额外列的工资表
            success = self.table_manager.create_salary_table(
                f"{month:02d}", year, additional_columns
            )
            if not success:
                return {"success": False, "error": "创建扩展表结构失败"}
        
        # 保存数据
        success, message = self.table_manager.save_dataframe_to_table(merged_df, target_table)
        
        self.import_stats["total_records"] = len(merged_df)
        
        return {
            "success": success,
            "message": message,
            "target_table": target_table,
            "merged_records": len(merged_df),
            "sheets_data": {name: len(df) for name, df in processed_data.items()}
        }
    
    def _import_separate_strategy(self, file_path: Path, sheet_names: List[str],
                                 year: int, month: int, target_path: Optional[str] = None) -> Dict[str, Any]:
        """分离导入策略：每个Sheet创建独立的表
        
        Args:
            file_path: 文件路径  
            sheet_names: Sheet名称列表
            year: 年份
            month: 月份
            target_path: 目标路径，用于确定导入类型
            
        Returns:
            导入结果
        """
        results = {}
        total_records = 0
        
        for sheet_name in sheet_names:
            if not self._is_sheet_enabled(sheet_name):
                continue
            
            try:
                # 读取和处理Sheet数据
                sheet_df = self.excel_importer.import_data(file_path, sheet_name=sheet_name)
                
                if sheet_df.empty:
                    continue
                
                processed_df = self._process_sheet_data(sheet_df, sheet_name, year, month)
                
                if processed_df.empty:
                    continue
                
                # 【新增】使用专用模板检测和创建
                excel_headers = list(processed_df.columns)

                # 1. 检测专用模板类型
                template_key = self.specialized_templates.detect_template_by_headers(excel_headers)
                self.logger.info(f"Sheet '{sheet_name}' 检测到模板类型: {template_key}")

                # 2. 生成表名
                # 根据target_path决定表名前缀
                if target_path and "异动人员表" in target_path:
                    table_prefix = "change_data"
                else:
                    table_prefix = "salary_data"
                
                if template_key != "salary_data":
                    # 使用专用模板，生成对应的表名
                    employee_type_mapping = {
                        "retired_employees": "retired_employees",
                        "pension_employees": "pension_employees",
                        "active_employees": "active_employees",
                        "a_grade_employees": "a_grade_employees"
                    }
                    employee_type = employee_type_mapping.get(template_key, "unknown")
                    table_name = f"{table_prefix}_{year}_{month:02d}_{employee_type}"
                else:
                    # 回退到通用表名
                    table_name = f"{table_prefix}_{year}_{month:02d}"

                # 3. 创建专用表结构
                if target_path and "异动人员表" in target_path:
                    # 创建异动表
                    create_success = self.table_manager.create_change_data_table(
                        table_name=table_name,
                        columns=list(processed_df.columns)
                    )
                    if not create_success:
                        self.logger.error(f"异动表创建失败: {table_name}")
                        continue
                elif template_key != "salary_data":
                    # 使用专用模板创建表
                    employee_type = employee_type_mapping.get(template_key, "")
                    create_success = self.table_manager.create_specialized_salary_table(
                        template_key=template_key,
                        month=f"{month:02d}",
                        year=year,
                        employee_type=employee_type
                    )
                    if not create_success:
                        self.logger.warning(f"专用表创建失败，回退到通用表: {table_name}")
                        # 回退到通用表创建
                        self.table_manager.create_salary_table(f"{month:02d}", year)
                else:
                    # 使用通用模板创建表
                    self.table_manager.create_salary_table(f"{month:02d}", year)

                # 4. 保存数据到表
                success, message = self.table_manager.save_dataframe_to_table(processed_df, table_name)
                
                results[sheet_name] = {
                    "success": success,
                    "message": message,
                    "table_name": table_name,
                    "records": len(processed_df)
                }
                
                if success:
                    total_records += len(processed_df)
                    self.import_stats["processed_sheets"] += 1
                
            except Exception as e:
                results[sheet_name] = {
                    "success": False,
                    "error": str(e)
                }
        
        self.import_stats["total_records"] = total_records
        
        return {
            "success": len([r for r in results.values() if r.get("success", False)]) > 0,
            "results": results,
            "total_records": total_records
        }
    
    def _process_sheet_data(self, df: pd.DataFrame, sheet_name: str, year: int = None, month: int = None) -> pd.DataFrame:
        """处理单个Sheet的数据
        
        Args:
            df: 原始DataFrame
            sheet_name: Sheet名称
            year: 年份（用于生成一致的表名）
            month: 月份（用于生成一致的表名）
            
        Returns:
            处理后的DataFrame
        """
        if df.empty:
            return df
        
        try:
            processed_df = df.copy()
            
            # 1. 获取Sheet配置
            sheet_config = self._get_sheet_config(sheet_name)
            
            if not sheet_config:
                self.logger.info(f"工作表 {sheet_name} 使用智能默认配置")
                # 生成智能默认配置，而不是直接返回
                sheet_config = self._generate_smart_default_config(sheet_name, processed_df)
            
            # 2. 生成表名用于映射配置 - 修改为与实际数据库表名一致
            table_name = self._generate_consistent_table_name(sheet_name, year, month)

            # 3. 【新增】使用专用模板检测和映射生成
            excel_headers = list(processed_df.columns)

            # 检测专用模板类型
            template_key = self.specialized_templates.detect_template_by_headers(excel_headers)

            # 4. 生成字段映射：数据库字段名 → 显示名
            if template_key != "salary_data":
                # 使用专用映射生成器
                initial_mapping = self.specialized_mapping_generator.generate_mapping(excel_headers, template_key)
                self.logger.info(f"使用专用模板 {template_key} 生成字段映射: {len(initial_mapping)} 个字段")
                # 为专用模板生成db_fields
                db_fields = list(initial_mapping.keys())
            else:
                # 回退到原有逻辑
                db_fields = self._get_database_field_names(table_name, excel_headers)
                table_type = self._detect_table_type_from_headers(excel_headers, sheet_name)

                if table_type and self.config_sync_manager is not None and self.config_sync_manager.get_template_mapping(table_type):
                    # 使用模板映射
                    template_mapping = self.config_sync_manager.get_template_mapping(table_type)
                    initial_mapping = self._adapt_template_to_excel_headers(template_mapping, excel_headers)
                    self.logger.info(f"使用 {table_type} 模板生成字段映射")
                else:
                    # 使用自动映射生成器
                    initial_mapping = self.auto_mapping_generator.create_initial_field_mapping(excel_headers, db_fields)
                    self.logger.info(f"使用自动映射生成器生成字段映射")

            # 检查是否已有用户自定义映射
            existing_mapping = None
            if self.config_sync_manager is not None:
                existing_mapping = self.config_sync_manager.load_mapping(table_name)
            else:
                self.logger.warning(f"🔧 [修复] ConfigSyncManager未设置，跳过加载已有映射")

            if existing_mapping:
                # 合并映射：保留用户自定义，补充新字段
                final_mapping = self._merge_field_mappings(existing_mapping, initial_mapping)
            else:
                final_mapping = initial_mapping

            # 保存映射配置
            mapping_data = {
                "field_mappings": final_mapping,
                "original_excel_headers": dict(zip(db_fields, excel_headers)),
                "metadata": {
                    "source": "excel_import",
                    "auto_generated": True,
                    "user_modified": False,
                    "created_at": datetime.now().isoformat(),
                    "sheet_name": sheet_name,
                    "has_chinese_headers": self._has_chinese_headers(excel_headers)
                }
            }

            if self.config_sync_manager is not None:
                self.config_sync_manager.save_complete_mapping(table_name, mapping_data)
                self.logger.info(f"为表 {table_name} 生成标准化字段映射: {len(final_mapping)} 个字段")
            else:
                self.logger.warning(f"🔧 [修复] ConfigSyncManager未设置，跳过保存字段映射")

            # 5. 应用字段映射到DataFrame（注意：这里不应该重命名列，因为数据库存储需要原始列名）
            # 字段映射主要用于显示层，数据存储时保持原始Excel列名
            self.logger.debug(f"Sheet {sheet_name} 字段映射配置完成，数据保持原始列名用于存储")

            # 6. 数据验证
            validation_errors = self._validate_sheet_data(processed_df, sheet_config)
            if validation_errors:
                self.import_stats["validation_errors"] += len(validation_errors)
                self.logger.warning(f"Sheet {sheet_name} 存在 {len(validation_errors)} 个验证错误")

            # 7. 数据清理
            processed_df = self._clean_sheet_data(processed_df)

            # 8. 添加Sheet标识
            processed_df['data_source'] = sheet_name
            processed_df['import_time'] = datetime.now().isoformat()
            
            self.logger.info(f"Sheet {sheet_name} 数据处理完成: {len(processed_df)} 行")
            return processed_df
            
        except Exception as e:
            self.logger.error(f"处理Sheet {sheet_name} 数据失败: {e}")
            return pd.DataFrame()
    
    def _merge_sheet_data(self, sheet_data: Dict[str, pd.DataFrame], 
                         all_columns: set) -> pd.DataFrame:
        """合并多个Sheet的数据
        
        Args:
            sheet_data: Sheet数据字典
            all_columns: 所有列名集合
            
        Returns:
            合并后的DataFrame
        """
        try:
            if not sheet_data:
                return pd.DataFrame()
            
            # 1. 统一列结构
            unified_dfs = []
            for sheet_name, df in sheet_data.items():
                # 为每个DataFrame补齐缺失的列
                unified_df = df.copy()
                for col in all_columns:
                    if col not in unified_df.columns:
                        unified_df[col] = None
                
                # 确保列顺序一致
                unified_df = unified_df[sorted(all_columns)]
                unified_dfs.append(unified_df)
            
            # 2. 纵向合并
            merged_df = pd.concat(unified_dfs, ignore_index=True)
            
            # 3. 数据去重和清理
            # 基于employee_id去重，保留最后一次出现的记录
            if 'employee_id' in merged_df.columns:
                merged_df = merged_df.drop_duplicates(subset=['employee_id'], keep='last')
            
            # 4. 按employee_id排序
            if 'employee_id' in merged_df.columns:
                merged_df = merged_df.sort_values('employee_id').reset_index(drop=True)
            
            self.logger.info(f"数据合并完成: {len(merged_df)} 行")
            return merged_df
            
        except Exception as e:
            self.logger.error(f"数据合并失败: {e}")
            return pd.DataFrame()
    
    def _detect_additional_columns(self, df: pd.DataFrame, table_name: str) -> List[ColumnDefinition]:
        """检测需要添加的额外列
        
        Args:
            df: 数据DataFrame
            table_name: 目标表名
            
        Returns:
            额外列定义列表
        """
        additional_columns = []
        
        try:
            # 获取现有表结构
            if self.table_manager.table_exists(table_name):
                existing_columns = {col['name'] for col in self.table_manager.get_table_columns(table_name)}
            else:
                # 使用模板的默认列
                template = self.table_manager._table_templates.get("salary_data")
                existing_columns = {col.name for col in template.columns} if template else set()
            
            # 检测新列
            df_columns = set(df.columns)
            new_columns = df_columns - existing_columns
            
            # 为新列创建定义
            for col_name in new_columns:
                if col_name in ['data_source', 'import_time']:
                    col_type = "TEXT"
                elif 'allowance' in col_name.lower() or 'salary' in col_name.lower() or 'deduction' in col_name.lower():
                    col_type = "REAL"
                else:
                    col_type = "TEXT"
                
                additional_columns.append(ColumnDefinition(
                    name=col_name,
                    type=col_type,
                    nullable=True,
                    description=f"从多Sheet导入的动态字段: {col_name}"
                ))
            
            if additional_columns:
                self.logger.info(f"检测到 {len(additional_columns)} 个额外字段: {[col.name for col in additional_columns]}")
            
            return additional_columns
            
        except Exception as e:
            self.logger.error(f"检测额外列失败: {e}")
            return []
    
    def _is_sheet_enabled(self, sheet_name: str) -> bool:
        """检查Sheet是否启用"""
        sheet_config = self._get_sheet_config(sheet_name)
        return sheet_config.get("enabled", True) if sheet_config else True
    
    def _get_sheet_config(self, sheet_name: str) -> Optional[Dict[str, Any]]:
        """获取Sheet配置"""
        sheet_mappings = self.sheet_configs.get("sheet_mappings", {})
        
        # 精确匹配
        if sheet_name in sheet_mappings:
            return sheet_mappings[sheet_name]
        
        # 模糊匹配
        for config_name, config in sheet_mappings.items():
            if config_name in sheet_name or sheet_name in config_name:
                return config
        
        return None
    
    def _get_sheet_data_type(self, sheet_name: str) -> str:
        """获取Sheet数据类型"""
        sheet_config = self._get_sheet_config(sheet_name)
        if sheet_config:
            return sheet_config.get("data_type", "misc_data")
        
        # 如果没有配置，尝试根据Sheet名称智能识别
        sheet_name_lower = sheet_name.lower()
        
        # 具体人员类型识别 - 优先级最高
        if "离休" in sheet_name or "retired" in sheet_name_lower:
            return "salary_data_retired_employees"
        elif "退休" in sheet_name or "pension" in sheet_name_lower:
            return "salary_data_pension_employees"
        elif "a岗" in sheet_name_lower or "a_grade" in sheet_name_lower or "a级" in sheet_name_lower:
            return "salary_data_a_grade_employees"
        elif "全部在职" in sheet_name or "active" in sheet_name_lower or "在职" in sheet_name:
            return "salary_data_active_employees"
        elif "职工" in sheet_name and "工资" in sheet_name:
            # 对于包含"职工"的工资表，进一步细分
            if "a岗" in sheet_name_lower or "a级" in sheet_name_lower:
                return "salary_data_a_grade_employees"
            else:
                return "salary_data_employees"
        
        # 工资相关的通用关键词 - 次优先级
        salary_keywords = ["工资", "salary", "薪酬", "薪资"]
        if any(keyword in sheet_name_lower for keyword in salary_keywords):
            return "salary_data"
        
        # 其他特殊类型的识别
        if "异动" in sheet_name_lower or "change" in sheet_name_lower:
            return "salary_changes"
        
        # 默认为salary_data（而不是misc_data，因为大部分情况下都是工资相关数据）
        return "salary_data"
    
    def _generate_smart_default_config(self, sheet_name: str, df: pd.DataFrame) -> Dict[str, Any]:
        """为未配置的Sheet生成智能默认配置
        
        Args:
            sheet_name: Sheet名称
            df: 数据DataFrame
            
        Returns:
            智能默认配置字典
        """
        try:
            # 检测基本字段
            required_fields = []
            df_columns = list(df.columns)
            
            # 检测员工ID字段
            for col in df_columns:
                if any(keyword in col.lower() for keyword in ['工号', 'employee_id', 'id', '编号']):
                    required_fields.append('employee_id')
                    break
            
            # 检测姓名字段
            for col in df_columns:
                if any(keyword in col.lower() for keyword in ['姓名', 'name', '名字']):
                    required_fields.append('employee_name')
                    break
            
            # 生成智能默认配置
            config = {
                "enabled": True,
                "field_mappings": {},  # 空映射，后续自动生成
                "required_fields": required_fields,
                "data_type": self._get_sheet_data_type(sheet_name),
                "auto_generated": True,
                "sheet_characteristics": {
                    "column_count": len(df_columns),
                    "row_count": len(df),
                    "has_chinese_headers": self._has_chinese_headers(df_columns),
                    "detected_field_types": self._detect_field_types(df_columns)
                }
            }
            
            self.logger.info(f"为Sheet '{sheet_name}' 生成智能默认配置: {len(required_fields)} 个必需字段")
            return config
            
        except Exception as e:
            self.logger.error(f"生成智能默认配置失败: {e}")
            return {
                "enabled": True,
                "field_mappings": {},
                "required_fields": [],
                "data_type": "salary_data",
                "auto_generated": True
            }
    
    def _has_chinese_headers(self, columns: List[str]) -> bool:
        """检测是否包含中文表头"""
        chinese_count = 0
        for col in columns:
            if any('\u4e00' <= char <= '\u9fff' for char in col):
                chinese_count += 1
        return chinese_count > len(columns) * 0.3  # 超过30%为中文认为是中文表头
    
    def _detect_field_types(self, columns: List[str]) -> Dict[str, str]:
        """检测字段类型"""
        field_types = {}
        
        for col in columns:
            col_lower = col.lower()
            if any(keyword in col_lower for keyword in ['工号', 'id', '编号', 'employee_id']):
                field_types[col] = 'identifier'
            elif any(keyword in col_lower for keyword in ['姓名', 'name', '名字']):
                field_types[col] = 'name'
            elif any(keyword in col_lower for keyword in ['工资', 'salary', '薪资', '津贴', 'allowance', '奖金', '补贴']):
                field_types[col] = 'amount'
            elif any(keyword in col_lower for keyword in ['扣款', 'deduction', '代扣', '扣除']):
                field_types[col] = 'deduction'
            elif any(keyword in col_lower for keyword in ['日期', 'date', '时间', 'time']):
                field_types[col] = 'date'
            else:
                field_types[col] = 'text'
        
        return field_types
    
    def _validate_sheet_data(self, df: pd.DataFrame, sheet_config: Dict[str, Any]) -> List[str]:
        """验证Sheet数据
        
        Args:
            df: 要验证的DataFrame
            sheet_config: Sheet配置
            
        Returns:
            List[str]: 验证错误列表
        """
        errors = []
        
        try:
            # 检查必填字段
            required_fields = sheet_config.get("required_fields", [])
            
            if not required_fields:
                self.logger.debug("未配置必填字段，跳过验证")
                return errors
            
            for field in required_fields:
                # 🔧 [P4修复] 改进字段检查逻辑，支持中英文字段名
                actual_field = self._find_actual_field_name(field, df.columns)
                
                if not actual_field:
                    # 尝试模糊匹配
                    matched_column = self._find_similar_column(field, df.columns)
                    if matched_column:
                        # 🔧 [P4修复] 这不是错误，只是信息提示
                        self.logger.debug(f"🔧 [P4修复] 字段映射建议: '{field}' → '{matched_column}'")
                        actual_field = matched_column
                    else:
                        errors.append(f"缺少必填字段: {field} (数据列: {list(df.columns)})")
                        continue
                
                # 2. 🔧 [P4修复] 检查字段是否有空值（使用实际字段名）
                if df[actual_field].isnull().all():
                    errors.append(f"字段 '{actual_field}' 全部为空")
                elif field in ['employee_id', 'employee_name'] and df[actual_field].isnull().any():
                    null_count = df[actual_field].isnull().sum()
                    total_count = len(df)
                    null_percentage = (null_count / total_count) * 100 if total_count > 0 else 0
                    
                    # 🔧 [P4修复] 放宽空值检查标准，减少误报
                    if null_percentage > 80:  # 超过80%为空才认为是严重问题
                        errors.append(f"字段 '{actual_field}' 空值过多: {null_count}/{total_count} ({null_percentage:.1f}%)")
                    else:
                        # 轻微的空值问题，记录为调试信息
                        self.logger.debug(f"🔧 [P4修复] 字段 '{actual_field}' 存在少量空值: {null_count}/{total_count}")
        
        except Exception as e:
            self.logger.error(f"数据验证过程出错: {e}")
            errors.append(f"验证过程异常: {str(e)}")
        
        return errors
    
    def _find_actual_field_name(self, target_field: str, available_columns: List[str]) -> Optional[str]:
        """🔧 [P4修复] 查找实际的字段名（支持中英文）"""
        # 1. 直接匹配
        if target_field in available_columns:
            return target_field
        
        # 2. 英文字段名到中文字段名的映射
        field_mappings = {
            'employee_id': ['工号', '人员代码', '职工编号', '编号', '员工号'],
            'employee_name': ['姓名', '员工姓名', '职工姓名', '名字', 'name']
        }
        
        if target_field in field_mappings:
            for synonym in field_mappings[target_field]:
                if synonym in available_columns:
                    return synonym
        
        return None
    
    def _find_similar_column(self, target_field: str, available_columns: List[str]) -> Optional[str]:
        """查找相似的列名
        
        Args:
            target_field: 目标字段名
            available_columns: 可用的列名列表
            
        Returns:
            Optional[str]: 匹配的列名，如果没有找到返回None
        """
        # 字段映射关系
        field_mappings = {
            'employee_id': ['工号', '人员代码', '职工编号', '编号', '员工号'],
            'employee_name': ['姓名', '员工姓名', '职工姓名', '名字', 'name']
        }
        
        if target_field in field_mappings:
            for synonym in field_mappings[target_field]:
                for col in available_columns:
                    if synonym in str(col) or str(col) in synonym:
                        return col
        
        # 直接字符串匹配
        target_lower = target_field.lower()
        for col in available_columns:
            col_lower = str(col).lower()
            if target_lower in col_lower or col_lower in target_lower:
                return col
        
        return None
    
    def _clean_sheet_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """清理Sheet数据"""
        cleaned_df = df.copy()
        
        # 删除全空行
        cleaned_df = cleaned_df.dropna(how='all')
        
        # 清理字符串列的前后空白
        for col in cleaned_df.columns:
            if cleaned_df[col].dtype == 'object':
                cleaned_df[col] = cleaned_df[col].astype(str).str.strip()
                cleaned_df[col] = cleaned_df[col].replace('nan', pd.NA)
        
        return cleaned_df
    
    def _create_custom_columns_from_dataframe(self, df: pd.DataFrame) -> List[ColumnDefinition]:
        """从DataFrame创建自定义列定义"""
        custom_columns = []
        
        for col_name in df.columns:
            # 根据列名推断类型
            if any(keyword in col_name.lower() for keyword in ['工资', '奖金', '津贴', '扣款', '金额']):
                col_type = "REAL"
            elif any(keyword in col_name.lower() for keyword in ['日期', '时间']):
                col_type = "TEXT"
            else:
                col_type = "TEXT"
            
            custom_columns.append(ColumnDefinition(
                name=col_name,
                type=col_type,
                nullable=True,
                description=f"动态创建的字段: {col_name}"
            ))
        
        return custom_columns
    
    def _reset_import_stats(self):
        """重置导入统计"""
        self.import_stats = {
            "total_sheets": 0,
            "processed_sheets": 0,
            "total_records": 0,
            "failed_records": 0,
            "validation_errors": 0
        }
    
    def get_import_stats(self) -> Dict[str, Any]:
        """获取导入统计信息"""
        return self.import_stats.copy()
    
    def export_sheet_config_template(self, output_path: str) -> bool:
        """导出Sheet配置模板
        
        Args:
            output_path: 输出文件路径
            
        Returns:
            是否导出成功
        """
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(self.sheet_configs, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"Sheet配置模板导出成功: {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"配置模板导出失败: {e}")
            return False
    
    def _get_actual_db_fields(self, table_name: str) -> List[str]:
        """获取数据库表的实际字段名

        Args:
            table_name: 表名

        Returns:
            List[str]: 数据库字段名列表
        """
        try:
            # 检查表是否存在
            if not self.table_manager.table_exists(table_name):
                self.logger.debug(f"表 {table_name} 不存在，无法获取字段信息")
                return []

            # 使用DynamicTableManager获取表结构
            table_columns = self.table_manager.get_table_columns(table_name)

            # 提取字段名
            db_fields = [col['name'] for col in table_columns if 'name' in col]

            self.logger.debug(f"表 {table_name} 字段: {db_fields}")
            return db_fields

        except Exception as e:
            self.logger.error(f"获取表 {table_name} 字段结构失败: {e}")
            return []

    def _generate_friendly_display_name(self, db_field: str) -> str:
        """为数据库字段生成友好的显示名称

        Args:
            db_field: 数据库字段名

        Returns:
            str: 友好的显示名称
        """
        # 如果已经是中文，直接返回
        if any('\u4e00' <= char <= '\u9fff' for char in db_field):
            return db_field

        # 常见字段映射
        field_display_map = {
            'employee_id': '工号',
            'employee_name': '姓名',
            'department': '部门',
            'position': '职位',
            'basic_salary': '基本工资',
            'position_salary': '岗位工资',
            'grade_salary': '薪级工资',
            'performance': '绩效工资',
            'allowance': '津贴补贴',
            'total_salary': '应发合计',
            'social_insurance': '五险一金个人',
            'data_source': '数据来源',
            'import_time': '导入时间',
            'created_at': '创建时间',
            'updated_at': '更新时间'
        }

        # 检查直接映射
        if db_field in field_display_map:
            return field_display_map[db_field]

        # 处理下划线分隔的字段名
        if '_' in db_field:
            # 将下划线替换为空格，首字母大写
            words = db_field.split('_')
            return ' '.join(word.capitalize() for word in words)

        # 默认返回原字段名
        return db_field

    def _generate_consistent_table_name(self, sheet_name: str, year: int, month: int) -> str:
        """为字段映射生成一致的表名

        Args:
            sheet_name: Sheet名称
            year: 年份
            month: 月份

        Returns:
            生成的表名
        """
        try:
            # 【修改】使用专用模板检测逻辑生成表名
            # 注意：这里需要先读取Sheet数据来获取表头，但为了保持方法简洁，
            # 我们使用sheet_name进行初步判断，如果需要更精确的检测，
            # 调用方应该传递excel_headers参数

            # 使用默认年月如果未提供
            if year is None or month is None:
                from datetime import datetime
                now = datetime.now()
                year = year or now.year
                month = month or now.month
            
            # 确保month是整数
            if isinstance(month, str):
                month = int(month)

            # 尝试从sheet_name推断模板类型
            template_key = self._detect_template_from_sheet_name(sheet_name)

            if template_key != "salary_data":
                # 使用专用模板生成表名
                employee_type_mapping = {
                    "retired_employees": "retired_employees",
                    "pension_employees": "pension_employees",
                    "active_employees": "active_employees",
                    "a_grade_employees": "a_grade_employees"
                }
                employee_type = employee_type_mapping.get(template_key, "unknown")
                table_name = f"salary_data_{year}_{month:02d}_{employee_type}"
            else:
                # 回退到原有逻辑
                sheet_type = self._get_sheet_data_type(sheet_name)
                if sheet_type.startswith("salary_data_"):
                    employee_type = sheet_type.replace("salary_data_", "")
                    table_name = f"salary_data_{year}_{month:02d}_{employee_type}"
                elif sheet_type == "salary_data":
                    table_name = f"salary_data_{year}_{month:02d}"
                else:
                    table_name = f"{sheet_type}_{year}_{month:02d}"

            self.logger.debug(f"为Sheet '{sheet_name}' 生成映射表名: {table_name}")
            return table_name

        except Exception as e:
            self.logger.error(f"生成一致表名失败: {e}")
            # 回退到简单格式，确保month是整数
            if isinstance(month, str):
                month = int(month)
            return f"salary_data_{year}_{month:02d}_{sheet_name}"

    def _detect_template_from_sheet_name(self, sheet_name: str) -> str:
        """从Sheet名称推断模板类型

        Args:
            sheet_name: Sheet名称

        Returns:
            模板键名
        """
        sheet_name_lower = sheet_name.lower()

        # 离休人员
        if any(keyword in sheet_name_lower for keyword in ["离休", "离退休"]):
            return "retired_employees"

        # 退休人员
        if any(keyword in sheet_name_lower for keyword in ["退休", "退职"]):
            return "pension_employees"

        # A岗职工
        if any(keyword in sheet_name_lower for keyword in ["a岗", "a级", "a类"]):
            return "a_grade_employees"

        # 在职人员
        if any(keyword in sheet_name_lower for keyword in ["在职", "全部在职", "职工", "员工"]):
            return "active_employees"

        return "salary_data"  # 默认通用模板
    
    def get_auto_mapping_statistics(self) -> Dict[str, Any]:
        """获取自动映射统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        return {
            "mapping_generator_stats": self.auto_mapping_generator.get_mapping_statistics(),
            "config_sync_stats": self.config_sync_manager.get_cache_statistics() if self.config_sync_manager is not None else {}
        }

    def _get_database_field_names(self, table_name: str, excel_headers: List[str]) -> List[str]:
        """获取数据库实际字段名（标准化后的）

        Args:
            table_name: 表名
            excel_headers: Excel原始表头

        Returns:
            List[str]: 数据库字段名列表
        """
        try:
            # 如果表已存在，获取实际字段名
            if self.table_manager.table_exists(table_name):
                db_fields = self._get_actual_db_fields(table_name)
                if db_fields:
                    return db_fields

            # 如果表不存在，根据Excel表头生成标准化字段名
            db_fields = []
            for header in excel_headers:
                # 简单的标准化：移除特殊字符，转换为下划线格式
                db_field = self._standardize_field_name(header)
                db_fields.append(db_field)

            return db_fields

        except Exception as e:
            self.logger.error(f"获取数据库字段名失败: {e}")
            # 回退到使用Excel表头
            return excel_headers

    def _standardize_field_name(self, field_name: str) -> str:
        """标准化字段名为数据库格式

        Args:
            field_name: 原始字段名

        Returns:
            str: 标准化后的字段名
        """
        import re

        # 如果已经是英文下划线格式，直接返回
        if re.match(r'^[a-z_][a-z0-9_]*$', field_name):
            return field_name

        # 常见中文字段映射
        field_mapping = {
            '工号': 'employee_id',
            '姓名': 'employee_name',
            '员工姓名': 'employee_name',
            '部门': 'department',
            '部门名称': 'department',
            '职位': 'position',
            '岗位': 'position',
            '基本工资': 'basic_salary',
            '岗位工资': 'position_salary',
            '薪级工资': 'grade_salary',
            '绩效工资': 'performance_bonus',
            '津贴': 'allowance',
            '津贴补贴': 'allowance',
            '应发工资': 'total_salary',
            '应发合计': 'total_salary',
            '五险一金': 'social_insurance',
            '公积金': 'housing_fund',
            '个税': 'income_tax',
            '实发工资': 'net_salary'
        }

        # 检查直接映射
        if field_name in field_mapping:
            return field_mapping[field_name]

        # 如果没有直接映射，保持原字段名（用于显示）
        return field_name

    def _merge_field_mappings(self, existing_mapping: Dict[str, str], initial_mapping: Dict[str, str]) -> Dict[str, str]:
        """合并字段映射：保留用户自定义，补充新字段

        Args:
            existing_mapping: 现有映射
            initial_mapping: 初始映射

        Returns:
            Dict[str, str]: 合并后的映射
        """
        final_mapping = initial_mapping.copy()

        # 用户自定义的映射优先
        for db_field, display_name in existing_mapping.items():
            final_mapping[db_field] = display_name

        return final_mapping

    def _detect_table_type_from_headers(self, excel_headers: List[str], sheet_name: str) -> Optional[str]:
        """根据Excel表头和Sheet名称智能检测表类型

        Args:
            excel_headers: Excel表头列表
            sheet_name: Sheet名称

        Returns:
            Optional[str]: 检测到的表类型，如果无法确定返回None
        """
        try:
            # 1. 根据Sheet名称进行初步判断
            sheet_name_lower = sheet_name.lower()

            if "离休" in sheet_name or "离退休" in sheet_name:
                return "离休人员工资表"
            elif "退休" in sheet_name and "离休" not in sheet_name:
                return "退休人员工资表"
            elif "a岗" in sheet_name_lower or "a类" in sheet_name_lower:
                return "A岗职工"
            elif "在职" in sheet_name or "职工" in sheet_name:
                return "全部在职人员工资表"

            # 2. 根据表头特征进行判断
            header_set = set(excel_headers)

            # 离休人员特征字段
            retirement_features = {"基本离休费", "离休补贴", "增发一次性生活补贴"}
            if retirement_features.intersection(header_set):
                return "离休人员工资表"

            # 退休人员特征字段
            retired_features = {"基本退休费", "离退休生活补贴", "2016待遇调整", "2017待遇调整"}
            if retired_features.intersection(header_set):
                return "退休人员工资表"

            # A岗职工特征字段
            a_grade_features = {"校龄工资", "2025年校龄工资"}
            if a_grade_features.intersection(header_set):
                return "A岗职工"

            # 在职人员特征字段
            active_features = {"岗位工资", "薪级工资", "基础性绩效", "奖励性绩效"}
            if active_features.intersection(header_set):
                return "全部在职人员工资表"

            # 3. 如果无法确定，返回None
            self.logger.debug(f"无法确定Sheet '{sheet_name}' 的表类型")
            return None

        except Exception as e:
            self.logger.error(f"检测表类型失败: {e}")
            return None

    def _adapt_template_to_excel_headers(self, template_mapping: Dict[str, str], excel_headers: List[str]) -> Dict[str, str]:
        """将模板映射适配到实际的Excel表头

        Args:
            template_mapping: 模板映射（数据库字段名 → 标准显示名）
            excel_headers: 实际Excel表头列表

        Returns:
            Dict[str, str]: 适配后的映射（数据库字段名 → Excel表头或标准显示名）
        """
        adapted_mapping = {}

        try:
            # 1. 直接匹配：Excel表头与模板显示名完全相同
            for db_field, template_display_name in template_mapping.items():
                if template_display_name in excel_headers:
                    adapted_mapping[db_field] = template_display_name
                    continue

                # 2. 模糊匹配：查找相似的Excel表头
                best_match = self._find_best_header_match(template_display_name, excel_headers)
                if best_match:
                    adapted_mapping[db_field] = best_match
                else:
                    # 3. 如果没有匹配，保持模板显示名
                    adapted_mapping[db_field] = template_display_name

            self.logger.info(f"模板适配完成: {len(adapted_mapping)} 个字段")
            return adapted_mapping

        except Exception as e:
            self.logger.error(f"模板适配失败: {e}")
            return template_mapping

    def _find_best_header_match(self, template_name: str, excel_headers: List[str]) -> Optional[str]:
        """查找最佳匹配的Excel表头

        Args:
            template_name: 模板字段名
            excel_headers: Excel表头列表

        Returns:
            Optional[str]: 最佳匹配的表头，如果没有找到返回None
        """
        # 简单的包含匹配
        for header in excel_headers:
            if template_name in header or header in template_name:
                return header

        # 关键词匹配
        template_keywords = template_name.replace("年", "").replace("2025", "").strip()
        for header in excel_headers:
            if template_keywords in header:
                return header

        return None

    def _get_database_field_names(self, table_name: str, excel_headers: List[str]) -> List[str]:
        """获取数据库字段名列表

        Args:
            table_name: 表名
            excel_headers: Excel表头列表

        Returns:
            List[str]: 数据库字段名列表
        """
        try:
            # 如果表已存在，获取实际字段
            if hasattr(self, 'dynamic_table_manager') and self.dynamic_table_manager:
                actual_fields = self._get_actual_db_fields(table_name)
                if actual_fields:
                    return actual_fields

            # 否则，将Excel表头转换为标准化的数据库字段名
            db_fields = []
            for header in excel_headers:
                db_field = self._excel_column_to_db_field(header)
                db_fields.append(db_field)

            return db_fields

        except Exception as e:
            self.logger.error(f"获取数据库字段名失败: {e}")
            # 返回标准化的字段名作为备用
            return [self._normalize_field_name(header) for header in excel_headers]

    def _excel_column_to_db_field(self, excel_column: str) -> str:
        """将Excel列名转换为数据库字段名

        Args:
            excel_column: Excel列名

        Returns:
            str: 数据库字段名
        """
        # 预定义的映射规则
        field_mapping = {
            "序号": "sequence_number",
            "工号": "employee_id",
            "人员代码": "employee_id",
            "姓名": "employee_name",
            "部门名称": "department",
            "人员类别": "employee_type",
            "人员类别代码": "employee_type_code",
            "基本工资": "basic_salary",
            "岗位工资": "position_salary",
            "薪级工资": "grade_salary",
            "津贴": "allowance",
            "结余津贴": "balance_allowance",
            "应发工资": "total_salary",
            "基本离休费": "basic_retirement_salary",
            "基本退休费": "basic_retirement_salary",
            "生活补贴": "living_allowance",
            "住房补贴": "housing_allowance",
            "物业补贴": "property_allowance",
            "护理费": "nursing_fee",
            "补发": "supplement",
            "借支": "advance",
            "备注": "remarks"
        }

        # 1. 直接映射
        if excel_column in field_mapping:
            return field_mapping[excel_column]

        # 2. 处理带年份的字段
        for pattern, db_field in field_mapping.items():
            if pattern in excel_column:
                return db_field

        # 3. 标准化字段名
        return self._normalize_field_name(excel_column)

    def _normalize_field_name(self, field_name: str) -> str:
        """标准化字段名

        Args:
            field_name: 原始字段名

        Returns:
            str: 标准化后的字段名
        """
        import re

        # 移除特殊字符和年份
        normalized = re.sub(r'[^\w\u4e00-\u9fff]', '_', field_name)
        normalized = re.sub(r'\d{4}年?', '', normalized)
        normalized = normalized.strip('_').lower()

        # 转换为英文字段名
        chinese_to_english = {
            "工号": "employee_id",
            "姓名": "employee_name",
            "部门": "department",
            "工资": "salary",
            "津贴": "allowance",
            "补贴": "allowance",
            "奖金": "bonus",
            "绩效": "performance"
        }

        for chinese, english in chinese_to_english.items():
            if chinese in normalized:
                normalized = normalized.replace(chinese, english)

        return normalized or "unknown_field"