#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试排序功能是否正常工作
"""

import sys
import os
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from PyQt5.QtWidgets import QApplication, QHeaderView
from PyQt5.QtCore import Qt, QTimer

from src.modules.system_config.config_manager import ConfigManager
from src.modules.data_storage.database_manager import DatabaseManager
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.gui.prototype.prototype_main_window import PrototypeMainWindow

def test_sort_function():
    """测试排序功能"""
    print("=" * 80)
    print("开始测试排序功能")
    print("=" * 80)
    
    # 创建应用程序
    app = QApplication(sys.argv)
    
    # 初始化核心管理器
    config_manager = ConfigManager()
    db_manager = DatabaseManager(config_manager=config_manager)
    dynamic_table_manager = DynamicTableManager(db_manager=db_manager)
    
    # 创建主窗口
    main_window = PrototypeMainWindow(
        config_manager=config_manager,
        db_manager=db_manager,
        dynamic_table_manager=dynamic_table_manager
    )
    
    # 显示主窗口
    main_window.show()
    
    def test_sort_after_load():
        """数据加载后测试排序"""
        try:
            print("\n" + "=" * 80)
            print("测试1: 检查表格数据是否加载")
            print("=" * 80)
            
            # 获取表格组件
            table_widget = main_window.main_workspace.table_widget
            
            # 检查是否有数据
            row_count = table_widget.rowCount()
            col_count = table_widget.columnCount()
            print(f"表格行数: {row_count}")
            print(f"表格列数: {col_count}")
            
            if row_count > 0 and col_count > 0:
                print("✅ 表格数据已加载")
                
                # 测试表头是否显示中文
                print("\n" + "=" * 80)
                print("测试2: 检查表头是否显示中文")
                print("=" * 80)
                
                headers = []
                for i in range(min(5, col_count)):  # 显示前5个表头
                    header_item = table_widget.horizontalHeaderItem(i)
                    if header_item:
                        header_text = header_item.text()
                        headers.append(header_text)
                        print(f"列{i}: {header_text}")
                
                # 检查是否有中文字符
                has_chinese = any(any('\u4e00' <= c <= '\u9fff' for c in h) for h in headers)
                if has_chinese:
                    print("✅ 表头显示中文")
                else:
                    print("❌ 表头未显示中文，仍显示数字或英文")
                
                # 测试排序功能
                print("\n" + "=" * 80)
                print("测试3: 模拟点击表头进行排序")
                print("=" * 80)
                
                # 找到一个可排序的列（不是序号列）
                sort_column = -1
                for i in range(col_count):
                    header_item = table_widget.horizontalHeaderItem(i)
                    if header_item:
                        header_text = header_item.text()
                        if header_text != "序号":
                            sort_column = i
                            print(f"选择列{i} ({header_text}) 进行排序测试")
                            break
                
                if sort_column >= 0:
                    # 获取排序前的第一行数据
                    before_sort = []
                    for i in range(min(3, row_count)):
                        item = table_widget.item(i, sort_column)
                        if item:
                            before_sort.append(item.text())
                    print(f"排序前前3行数据: {before_sort}")
                    
                    # 获取水平表头
                    header = table_widget.horizontalHeader()
                    
                    # 记录当前排序状态
                    current_sort_indicator = header.sortIndicatorSection()
                    current_sort_order = header.sortIndicatorOrder()
                    print(f"当前排序指示器: 列{current_sort_indicator}, 顺序{current_sort_order}")
                    
                    # 模拟点击表头
                    print(f"模拟点击列{sort_column}的表头...")
                    header.sectionClicked.emit(sort_column)
                    
                    # 等待排序完成
                    QApplication.processEvents()
                    time.sleep(0.5)
                    QApplication.processEvents()
                    
                    # 检查排序指示器是否改变
                    new_sort_indicator = header.sortIndicatorSection()
                    new_sort_order = header.sortIndicatorOrder()
                    print(f"新的排序指示器: 列{new_sort_indicator}, 顺序{new_sort_order}")
                    
                    if new_sort_indicator == sort_column:
                        print("✅ 排序指示器已更新")
                    else:
                        print("❌ 排序指示器未更新")
                    
                    # 获取排序后的数据
                    after_sort = []
                    for i in range(min(3, row_count)):
                        item = table_widget.item(i, sort_column)
                        if item:
                            after_sort.append(item.text())
                    print(f"排序后前3行数据: {after_sort}")
                    
                    # 检查数据是否发生变化
                    if before_sort != after_sort:
                        print("✅ 数据顺序已改变，排序功能正常")
                    else:
                        print("⚠️ 数据顺序未改变，可能排序未生效或数据本身已有序")
                        
                        # 再次点击测试降序
                        print("\n再次点击测试降序排序...")
                        header.sectionClicked.emit(sort_column)
                        QApplication.processEvents()
                        time.sleep(0.5)
                        QApplication.processEvents()
                        
                        after_second_click = []
                        for i in range(min(3, row_count)):
                            item = table_widget.item(i, sort_column)
                            if item:
                                after_second_click.append(item.text())
                        print(f"第二次点击后前3行数据: {after_second_click}")
                        
                        if after_sort != after_second_click:
                            print("✅ 第二次点击后数据顺序改变，排序功能正常")
                        else:
                            print("❌ 第二次点击后数据顺序仍未改变，排序功能可能有问题")
                
            else:
                print("❌ 表格无数据，无法测试排序")
            
            print("\n" + "=" * 80)
            print("排序功能测试完成")
            print("=" * 80)
            
        except Exception as e:
            print(f"测试失败: {e}")
            import traceback
            traceback.print_exc()
        
        # 3秒后关闭
        QTimer.singleShot(3000, app.quit)
    
    # 延迟1秒后开始测试（等待数据加载）
    QTimer.singleShot(1000, test_sort_after_load)
    
    # 运行应用
    sys.exit(app.exec_())

if __name__ == "__main__":
    test_sort_function()