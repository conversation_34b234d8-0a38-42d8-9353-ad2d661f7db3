#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试P0修复：验证字段显示是否正常
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import pandas as pd
from src.modules.format_management.format_renderer import FormatRenderer
from src.modules.format_management.field_registry import FieldRegistry
from src.modules.format_management.format_config import FormatConfig

def test_field_display():
    """测试字段显示修复效果"""
    print("=" * 80)
    print("P0修复测试：验证字段显示")
    print("=" * 80)
    
    # 初始化组件
    mapping_path = project_root / "state" / "data" / "field_mappings.json"
    config_path = project_root / "src" / "modules" / "format_management" / "format_config.json"
    field_registry = FieldRegistry(mapping_path=str(mapping_path))
    format_config = FormatConfig(config_path=str(config_path))
    format_renderer = FormatRenderer(field_registry=field_registry, format_config=format_config)
    
    # 模拟原始数据（28个字段）
    test_data = {
        # 基本信息字段
        '工号': ['001', '002', '003'],
        '姓名': ['张三', '李四', '王五'],
        '部门名称': ['技术部', '财务部', '人事部'],
        '人员类别': ['正式', '正式', '正式'],
        '人员类别代码': ['01', '01', '01'],
        
        # 工资字段
        '2025年岗位工资': [5000, 5500, 6000],
        '2025年薪级工资': [2000, 2200, 2400],
        '津贴': [500, 550, 600],
        '结余津贴': [100, 110, 120],
        '2025年基础性绩效': [1000, 1100, 1200],
        
        # 补贴字段
        '卫生费': [50, 50, 50],
        '交通补贴': [200, 200, 200],
        '物业补贴': [100, 100, 100],
        '住房补贴': [800, 800, 800],
        '车补': [300, 300, 300],
        '通讯补贴': [100, 100, 100],
        
        # 绩效和其他
        '2025年奖励性绩效预发': [2000, 2200, 2400],
        '补发': [0, 100, 200],
        '借支': [0, 0, 100],
        '应发工资': [12000, 13000, 14000],
        
        # 扣款项
        '2025公积金': [1000, 1100, 1200],
        '保险扣款': [500, 550, 600],
        '代扣代存养老保险': [800, 880, 960],
        
        # 系统字段（应该被隐藏）
        'id': [1, 2, 3],
        'created_at': ['2025-01-01', '2025-01-01', '2025-01-01'],
        'updated_at': ['2025-01-01', '2025-01-01', '2025-01-01'],
        'sequence_number': [1, 2, 3],
        
        # 其他字段
        'year': ['2025', '2025', '2025']
    }
    
    df = pd.DataFrame(test_data)
    print(f"\n原始数据字段数: {len(df.columns)}")
    print(f"原始字段列表: {list(df.columns)[:10]}...")
    
    # 测试格式渲染
    print("\n" + "=" * 80)
    print("测试格式渲染器处理")
    print("=" * 80)
    
    # 测试active_employees表类型
    formatted_df = format_renderer.render_dataframe(df.copy(), table_type='active_employees')
    
    print(f"\n处理后字段数: {len(formatted_df.columns)}")
    print(f"处理后字段列表: {list(formatted_df.columns)}")
    
    # 检查系统字段是否被隐藏
    system_fields = ['id', 'created_at', 'updated_at', 'sequence_number']
    hidden_correctly = all(field not in formatted_df.columns for field in system_fields)
    
    # 检查业务字段是否保留
    business_fields = [
        '2025年基础性绩效', '卫生费', '交通补贴', '物业补贴',
        '住房补贴', '车补', '通讯补贴', '2025年奖励性绩效预发',
        '2025公积金', '保险扣款', '代扣代存养老保险'
    ]
    business_retained = sum(1 for field in business_fields if field in formatted_df.columns)
    
    print("\n" + "=" * 80)
    print("测试结果")
    print("=" * 80)
    
    print(f"✓ 系统字段隐藏: {'成功' if hidden_correctly else '失败'}")
    print(f"  - 被隐藏的字段: {[f for f in system_fields if f not in formatted_df.columns]}")
    
    print(f"\n✓ 业务字段保留: {business_retained}/{len(business_fields)}")
    if business_retained < len(business_fields):
        missing = [f for f in business_fields if f not in formatted_df.columns]
        print(f"  - 缺失的业务字段: {missing}")
    else:
        print("  - 所有业务字段都已保留")
    
    # 总体评估
    print("\n" + "=" * 80)
    if hidden_correctly and business_retained == len(business_fields):
        print("✅ P0修复成功！所有业务字段保留，系统字段正确隐藏")
    else:
        print("❌ P0修复存在问题，需要进一步调整")
        
    print("=" * 80)
    
    return hidden_correctly and business_retained == len(business_fields)

if __name__ == "__main__":
    success = test_field_display()
    sys.exit(0 if success else 1)