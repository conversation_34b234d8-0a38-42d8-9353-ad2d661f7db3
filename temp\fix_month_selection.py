"""
修复月份选择问题的方案

问题：用户在界面选择的月份与实际使用的月份不一致
原因：可能存在多个月份输入源，优先级不明确
"""

def proposed_fixes():
    """
    建议的修复方案
    """
    fixes = {
        "1. 明确月份来源优先级": """
        # 在 main_dialogs.py 的 _on_import_clicked 方法中
        def _get_final_data_period(self):
            '''获取最终的数据期间，明确优先级'''
            # 优先级1：用户手动输入的data_period_edit
            user_input = self.data_period_edit.text().strip()
            if user_input and self._validate_period_format(user_input):
                return user_input
            
            # 优先级2：从目标路径中提取
            target_path = self.target_selection_widget.get_target_path_string()
            if target_path:
                period = self._extract_period_from_path(target_path)
                if period:
                    return period
            
            # 优先级3：从文件名提取
            if hasattr(self, 'current_file_path'):
                period = self._extract_period_from_filename(self.current_file_path)
                if period:
                    return period
            
            # 优先级4：使用当前月份
            from datetime import datetime
            return datetime.now().strftime("%Y-%m")
        """,
        
        "2. 添加月份自动提取和提示": """
        # 在文件选择后自动提取月份并提示用户
        def _load_sheet_names(self, file_path: str):
            # ... 原有代码 ...
            
            # 尝试从文件名提取月份
            extracted_period = self._extract_period_from_filename(file_path)
            if extracted_period:
                # 提示用户确认
                reply = QMessageBox.question(
                    self, "确认数据期间",
                    f"从文件名检测到数据期间为：{extracted_period}\\n"
                    f"是否使用此期间？\\n\\n"
                    f"点击'是'使用检测到的期间\\n"
                    f"点击'否'手动输入期间",
                    QMessageBox.Yes | QMessageBox.No
                )
                if reply == QMessageBox.Yes:
                    self.data_period_edit.setText(extracted_period)
        """,
        
        "3. 添加期间提取方法": """
        def _extract_period_from_filename(self, file_path: str) -> str:
            '''从文件名提取年月'''
            import re
            import os
            
            filename = os.path.basename(file_path)
            
            # 匹配模式：2025年5月、2025年05月、2025-05等
            patterns = [
                r'(\d{4})年(\d{1,2})月',  # 2025年5月
                r'(\d{4})-(\d{2})',        # 2025-05
                r'(\d{4})(\d{2})',          # 202505
            ]
            
            for pattern in patterns:
                match = re.search(pattern, filename)
                if match:
                    year = match.group(1)
                    month = match.group(2).zfill(2)  # 确保月份是两位数
                    return f"{year}-{month}"
            
            return None
        """,
        
        "4. 导入前再次确认": """
        # 在实际导入前显示确认对话框
        def _on_import_clicked(self):
            # ... 验证代码 ...
            
            # 最终确认
            data_period = self.data_period_edit.text().strip()
            target_path = self.target_selection_widget.get_target_path_string()
            
            confirm_msg = f'''
            请确认导入信息：
            - 文件：{os.path.basename(self.salary_file_edit.text())}
            - 数据期间：{data_period}
            - 目标位置：{target_path}
            
            确认无误后开始导入？
            '''
            
            reply = QMessageBox.question(
                self, "确认导入信息",
                confirm_msg,
                QMessageBox.Yes | QMessageBox.No
            )
            
            if reply != QMessageBox.Yes:
                return
            
            # 继续导入...
        """
    }
    
    return fixes

def implementation_priority():
    """
    实施优先级
    """
    return """
    建议实施顺序：
    1. 首先添加期间提取方法（方案3）
    2. 然后在文件选择后自动提取并提示（方案2）
    3. 明确月份来源优先级（方案1）
    4. 最后添加导入前确认（方案4）
    
    这样可以：
    - 自动识别文件中的月份信息
    - 给用户确认和修改的机会
    - 避免混淆和错误
    """

print("修复方案已生成")