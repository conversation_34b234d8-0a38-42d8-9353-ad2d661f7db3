2025-08-15 21:38:41.166 | INFO     | src.utils.log_config:_log_initialization_info:289 | 日志系统初始化完成
2025-08-15 21:38:41.166 | INFO     | src.utils.log_config:_log_initialization_info:290 | 日志级别: INFO
2025-08-15 21:38:41.166 | INFO     | src.utils.log_config:_log_initialization_info:291 | 控制台输出: True
2025-08-15 21:38:41.166 | INFO     | src.utils.log_config:_log_initialization_info:292 | 文件输出: True
2025-08-15 21:38:41.166 | INFO     | src.utils.log_config:_log_initialization_info:298 | 日志文件路径: logs/salary_system.log
2025-08-15 21:38:41.166 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-08-15 21:38:46.011 | INFO     | src.gui.style_manager:__init__:68 | StyleManager初始化完成
2025-08-15 21:38:46.011 | INFO     | src.gui.style_manager:apply_global_style:93 | 开始应用全局Material Design样式...
2025-08-15 21:38:46.011 | INFO     | src.gui.style_manager:apply_global_style:105 | 全局样式应用成功
2025-08-15 21:38:46.011 | INFO     | src.gui.style_manager:enable_hot_reload:416 | 样式热重载未启用（非开发环境）
2025-08-15 21:38:46.011 | INFO     | __main__:setup_app_logging:427 | 月度工资异动处理系统 v2.0.0-refactored 启动
2025-08-15 21:38:46.011 | INFO     | __main__:main:491 | 初始化核心管理器...
2025-08-15 21:38:46.011 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-15 21:38:46.011 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-08-15 21:38:46.011 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-08-15 21:38:46.011 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-08-15 21:38:46.027 | INFO     | src.modules.data_storage.database_manager:_initialize_database:102 | 开始初始化数据库...
2025-08-15 21:38:46.074 | INFO     | src.modules.data_storage.database_manager:_initialize_database:157 | 数据库初始化完成
2025-08-15 21:38:46.074 | INFO     | src.modules.data_storage.database_manager:__init__:98 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-08-15 21:38:46.090 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-15 21:38:46.090 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:107 | 动态表管理器初始化完成
2025-08-15 21:38:46.105 | INFO     | __main__:main:496 | 核心管理器初始化完成。
2025-08-15 21:38:46.136 | INFO     | src.core.data_source_unification_manager:__init__:128 | 数据源统一管理器初始化完成
2025-08-15 21:38:46.136 | INFO     | src.core.table_sort_state_manager:__init__:177 | 表级排序状态管理器初始化完成
2025-08-15 21:38:46.136 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-15 21:38:46.136 | INFO     | src.core.error_handler_manager:__init__:111 | 🔧 [P2-3] 错误处理管理器初始化完成
2025-08-15 21:38:46.136 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: data_error_medium
2025-08-15 21:38:46.136 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: ui_error_medium
2025-08-15 21:38:46.136 | INFO     | src.core.error_handler_manager:register_recovery_strategy:372 | 🔧 [P2-3] 注册恢复策略: system_error_medium
2025-08-15 21:38:46.136 | INFO     | src.gui.prototype.prototype_main_window:_register_recovery_strategies:11764 | 🔧 [P2-3] 错误恢复策略注册完成
2025-08-15 21:38:46.136 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-15 21:38:46.136 | INFO     | src.gui.prototype.prototype_main_window:_setup_error_handling:11619 | 🔧 [P2-3] 错误处理机制设置完成
2025-08-15 21:38:46.136 | INFO     | src.gui.prototype.prototype_main_window:_init_recursive_call_protection:11657 | 🔧 [P1-2] 递归调用防护机制初始化完成
2025-08-15 21:38:46.199 | INFO     | src.core.architecture_factory:__init__:62 | 架构重构工厂初始化完成
2025-08-15 21:38:46.199 | INFO     | src.core.architecture_factory:initialize_architecture:72 | 开始初始化架构重构系统...
2025-08-15 21:38:46.199 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-15 21:38:46.214 | INFO     | src.modules.data_import.config_sync_manager:_do_config_initialization:149 | 🔧 [配置修复] 创建了完整的默认配置，包含基本字段映射和模板
2025-08-15 21:38:46.214 | INFO     | src.modules.data_import.config_sync_manager:__init__:81 | 🆕 [新架构] 配置同步管理器初始化完成（依赖注入）
2025-08-15 21:38:46.214 | INFO     | src.core.unified_state_manager:_load_state:560 | 状态文件不存在，使用默认状态
2025-08-15 21:38:46.214 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-15 21:38:46.214 | INFO     | src.core.unified_data_request_manager:__init__:192 | 统一数据请求管理器初始化完成
2025-08-15 21:38:46.214 | INFO     | src.core.unified_data_request_manager:__init__:192 | 统一数据请求管理器初始化完成
2025-08-15 21:38:46.214 | INFO     | src.core.unified_state_manager:_load_state:560 | 状态文件不存在，使用默认状态
2025-08-15 21:38:46.214 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-15 21:38:46.214 | INFO     | src.services.table_data_service:__init__:84 | 表格数据服务初始化完成
2025-08-15 21:38:46.214 | INFO     | src.core.architecture_factory:initialize_architecture:103 | 🎉 架构重构系统初始化成功！耗时: 15.7ms
2025-08-15 21:38:46.230 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:46 | 数据预加载缓存初始化完成 - 最大条目数: 100, TTL: 300秒
2025-08-15 21:38:46.230 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:232 | 表格状态缓存初始化完成
2025-08-15 21:38:46.246 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:32 | 表头配置缓存初始化完成
2025-08-15 21:38:46.246 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:211 | 字段映射缓存初始化完成
2025-08-15 21:38:46.246 | INFO     | src.gui.prototype.performance.performance_manager:__init__:46 | 🚀 性能管理器初始化完成
2025-08-15 21:38:46.246 | INFO     | src.gui.prototype.prototype_main_window:__init__:3613 | 🚀 性能管理器已集成
2025-08-15 21:38:46.246 | INFO     | src.gui.prototype.prototype_main_window:__init__:3615 | ✅ 新架构集成成功！
2025-08-15 21:38:46.262 | INFO     | src.gui.prototype.prototype_main_window:_inject_config_sync_manager_to_existing_tables:3728 | ConfigSyncManager重新注入完成，已更新0个表格实例
2025-08-15 21:38:46.262 | INFO     | src.gui.prototype.prototype_main_window:_setup_new_architecture_listeners:3693 | ✅ 新架构事件监听器设置完成
2025-08-15 21:38:46.262 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成 - 最大条目数: 50, 最大内存: 50MB
2025-08-15 21:38:46.262 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:121 | 响应式布局管理器初始化完成
2025-08-15 21:38:46.262 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-08-15 21:38:47.113 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:2689 | 菜单栏创建完成
2025-08-15 21:38:47.113 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-08-15 21:38:47.113 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设置: user_preferences.json
2025-08-15 21:38:47.113 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成功
2025-08-15 21:38:47.113 | INFO     | src.gui.prototype.prototype_main_window:__init__:2664 | 菜单栏管理器初始化完成
2025-08-15 21:38:47.113 | INFO     | src.gui.table_header_manager:__init__:105 | 🔧 [P1-3] 性能优化表头管理器初始化完成
2025-08-15 21:38:47.113 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:5583 | 管理器设置完成，包含增强版表头管理器
2025-08-15 21:38:47.113 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:5588 | 🔧 开始应用窗口级Material Design样式...
2025-08-15 21:38:47.113 | INFO     | src.gui.style_manager:apply_window_style:123 | 开始应用窗口级样式...
2025-08-15 21:38:47.128 | INFO     | src.gui.style_manager:apply_window_style:136 | 窗口级样式应用成功
2025-08-15 21:38:47.128 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:5595 | ✅ 窗口级样式应用成功
2025-08-15 21:38:47.128 | INFO     | src.gui.prototype.prototype_main_window:_setup_responsive_style:5636 | ✅ 响应式样式监听设置完成
2025-08-15 21:38:47.160 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-08-15 21:38:47.160 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-08-15 21:38:47.175 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-08-15 21:38:47.191 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-08-15 21:38:47.238 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:150 | 智能树形展开算法初始化完成
2025-08-15 21:38:47.253 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:1893 | 在 table_metadata 中未找到任何 'salary_data' 类型的表
2025-08-15 21:38:47.253 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_load_fallback_salary_data:1830 | 使用兜底数据加载导航
2025-08-15 21:38:47.253 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_change_navigation_tree_data:1956 | 在 table_metadata 中未找到任何 'change_data' 类型的表
2025-08-15 21:38:47.269 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:890 | 导航面板已重构：移除功能性导航，专注数据导航
2025-08-15 21:38:47.269 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:924 | 恢复导航状态: 0个展开项
2025-08-15 21:38:47.269 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表', '工资表 > 2025年', '工资表 > 2025年 > 05月', '工资表 > 2025年 > 05月 > 全部在职人员']
2025-08-15 21:38:47.290 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:643 | 增强导航面板初始化完成
2025-08-15 21:38:47.301 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-08-15 21:38:47.307 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:150 | 智能树形展开算法初始化完成
2025-08-15 21:38:47.312 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:1893 | 在 table_metadata 中未找到任何 'salary_data' 类型的表
2025-08-15 21:38:47.318 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_change_navigation_tree_data:1956 | 在 table_metadata 中未找到任何 'change_data' 类型的表
2025-08-15 21:38:47.320 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:890 | 导航面板已重构：移除功能性导航，专注数据导航
2025-08-15 21:38:47.322 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:643 | 增强导航面板初始化完成
2025-08-15 21:38:47.322 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1395 | 🔧 [P1-2修复] 开始自动选择最新数据...
2025-08-15 21:38:47.324 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2050 | 开始获取最新工资数据路径...
2025-08-15 21:38:47.337 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2055 | 未找到任何工资数据表
2025-08-15 21:38:47.338 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_get_latest_path_with_retry:1592 | 🔧 [P1-2修复] 检查表状态失败，1.0s后重试...
2025-08-15 21:38:47.339 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1400 | 🔧 [P1-2修复] 多次重试后仍未找到最新工资数据路径
2025-08-15 21:38:47.682 | INFO     | src.gui.prototype.widgets.header_update_manager:__init__:68 | HeaderUpdateManager 初始化完成，关联表格: VirtualizedExpandableTable
2025-08-15 21:38:47.684 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2138 | 🚨 [架构修复] 启用模型数据同步机制
2025-08-15 21:38:47.691 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1365 | 快捷键注册完成: 18/18 个
2025-08-15 21:38:47.691 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1805 | 拖拽排序管理器初始化完成
2025-08-15 21:38:47.720 | INFO     | src.modules.data_management.data_flow_validator:__init__:78 | 🔧 [数据验证器] 初始化完成，验证级别: moderate
2025-08-15 21:38:47.721 | INFO     | src.modules.state_management.table_state_manager:__init__:103 | 🔧 [状态管理] 仅使用内存缓存
2025-08-15 21:38:47.723 | INFO     | src.modules.state_management.table_state_manager:__init__:105 | 🔧 [状态管理] 初始化完成，最大缓存: 100
2025-08-15 21:38:47.724 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2173 | 🔧 [排序修复] 数据流验证器和状态管理器初始化成功
2025-08-15 21:38:47.726 | INFO     | src.modules.data_import.header_edit_manager:__init__:82 | 表头编辑管理器初始化完成
2025-08-15 21:38:47.728 | INFO     | src.core.unified_state_manager:_load_state:560 | 状态文件不存在，使用默认状态
2025-08-15 21:38:47.744 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-15 21:38:47.745 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2225 | 🎯 [统一格式管理] 使用统一格式管理器
2025-08-15 21:38:47.754 | INFO     | src.gui.prototype.widgets.column_sort_manager:_load_field_mappings_from_config:344 | 🔧 [新架构] 成功加载 46 个字段映射
2025-08-15 21:38:47.755 | INFO     | src.gui.prototype.widgets.column_sort_manager:__init__:102 | 🆕 [新架构排序] 多列排序管理器初始化完成，最大排序列数: 3
2025-08-15 21:38:47.758 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2272 | 🆕 [新架构多列排序] 初始化完成，支持最多3列排序
2025-08-15 21:38:47.762 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1552 | 🔧 [列宽保存修复] 列宽管理器初始化完成
2025-08-15 21:38:47.763 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1553 | 🔧 [列宽保存修复] 配置文件绝对路径: C:\test\salary_changes\salary_changes\state\column_widths.json
2025-08-15 21:38:47.764 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1554 | 🔧 [列宽保存修复] 配置文件存在: False
2025-08-15 21:38:47.771 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1555 | 🔧 [列宽保存修复] 父目录存在: True
2025-08-15 21:38:47.771 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1556 | 🔧 [列宽保存修复] 当前工作目录: C:\test\salary_changes\salary_changes
2025-08-15 21:38:47.772 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2279 | 列宽管理器初始化完成
2025-08-15 21:38:47.776 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:setup_ui:2406 | 🔧 [架构修复] 保持自定义排序功能，修复数据显示问题
2025-08-15 21:38:47.782 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2293 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-08-15 21:38:47.784 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2322 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-15 21:38:47.784 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-15 21:38:47.785 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4487 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-15 21:38:47.787 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5300 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-15 21:38:47.790 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2825 | 🔧 [P0修复] 检测到空表且表头为中文，跳过字段映射: 22个表头
2025-08-15 21:38:47.792 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2904 | 表格格式化完成: default_table, 类型: active_employees
2025-08-15 21:38:47.811 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-15 21:38:47.812 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-15 21:38:47.813 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-15 21:38:47.814 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-15 21:38:47.815 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:442 | 表格数据已设置: 0 行, 22 列
2025-08-15 21:38:47.830 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:417 | 🔧 [新架构] 排序管理器切换到表格: default_table
2025-08-15 21:38:47.832 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 0 行, 耗时: 43.2ms
2025-08-15 21:38:47.832 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7926 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-15 21:38:47.833 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7939 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-15 21:38:47.834 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1709 | 🔧 [列宽保存修复] 配置文件不存在: C:\test\salary_changes\salary_changes\state\column_widths.json，使用默认列宽
2025-08-15 21:38:47.839 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-15 21:38:47.839 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2346 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-15 21:38:47.862 | INFO     | src.gui.widgets.pagination_widget:_apply_styles:298 | 分页组件Material Design样式应用成功
2025-08-15 21:38:47.872 | INFO     | src.gui.widgets.pagination_widget:__init__:176 | ✅ [防抖升级] 智能防抖系统已启用
2025-08-15 21:38:47.873 | INFO     | src.gui.widgets.pagination_widget:__init__:184 | 分页组件初始化完成
2025-08-15 21:38:47.936 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:594 | 控制面板按钮信号连接完成
2025-08-15 21:38:47.988 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-15 21:38:47.988 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-08-15 21:38:47.992 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:5545 | 快捷键设置完成
2025-08-15 21:38:47.992 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:5502 | 主窗口UI设置完成。
2025-08-15 21:38:47.993 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5739 | 🔧 [全局排序] 全局排序开关连接成功
2025-08-15 21:38:47.994 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5771 | 🆕 [新架构排序] 使用表格组件内部的自定义排序循环，无需连接排序信号
2025-08-15 21:38:47.995 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5783 | ✅ 已连接分页刷新信号到主窗口
2025-08-15 21:38:47.996 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5784 | ✅ 已连接分页组件事件到新架构
2025-08-15 21:38:47.998 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5795 | ✅ 已连接概览标签页刷新按钮到主窗口
2025-08-15 21:38:47.998 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:5798 | 信号连接设置完成
2025-08-15 21:38:48.000 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:6975 | 已加载字段映射信息，共0个表的映射
2025-08-15 21:38:48.019 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2322 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-15 21:38:48.022 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-15 21:38:48.023 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4487 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-15 21:38:48.025 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5300 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-15 21:38:48.026 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2825 | 🔧 [P0修复] 检测到空表且表头为中文，跳过字段映射: 22个表头
2025-08-15 21:38:48.026 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2904 | 表格格式化完成: default_table, 类型: active_employees
2025-08-15 21:38:48.028 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-15 21:38:48.028 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-15 21:38:48.030 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-15 21:38:48.036 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-15 21:38:48.037 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 0 行, 耗时: 11.8ms
2025-08-15 21:38:48.038 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7926 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-15 21:38:48.039 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7939 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-15 21:38:48.040 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1709 | 🔧 [列宽保存修复] 配置文件不存在: C:\test\salary_changes\salary_changes\state\column_widths.json，使用默认列宽
2025-08-15 21:38:48.041 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-15 21:38:48.041 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2346 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-15 21:38:48.042 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-15 21:38:48.043 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8498 | 🔧 [数据流追踪] 无当前表名，使用默认在职人员表头: 22个字段
2025-08-15 21:38:48.044 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2322 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-15 21:38:48.051 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-15 21:38:48.051 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4487 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-15 21:38:48.053 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5300 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-15 21:38:48.054 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2825 | 🔧 [P0修复] 检测到空表且表头为中文，跳过字段映射: 22个表头
2025-08-15 21:38:48.055 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2904 | 表格格式化完成: default_table, 类型: active_employees
2025-08-15 21:38:48.056 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-15 21:38:48.057 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-15 21:38:48.059 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-15 21:38:48.068 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-15 21:38:48.069 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 0 行, 耗时: 15.9ms
2025-08-15 21:38:48.069 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7926 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-15 21:38:48.071 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7939 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-15 21:38:48.072 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1709 | 🔧 [列宽保存修复] 配置文件不存在: C:\test\salary_changes\salary_changes\state\column_widths.json，使用默认列宽
2025-08-15 21:38:48.073 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-15 21:38:48.073 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2346 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-15 21:38:48.074 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8516 | 已显示标准空表格，表头数量: 22
2025-08-15 21:38:48.075 | INFO     | src.gui.prototype.prototype_main_window:__init__:3667 | 原型主窗口初始化完成
2025-08-15 21:38:48.150 | INFO     | __main__:main:518 | 应用程序启动成功
2025-08-15 21:38:48.182 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7961 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-15 21:38:48.207 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1465 | 执行延迟的自动选择最新数据...
2025-08-15 21:38:48.232 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2050 | 开始获取最新工资数据路径...
2025-08-15 21:38:48.234 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2055 | 未找到任何工资数据表
2025-08-15 21:38:48.234 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1400 | 🔧 [P1-2修复] 多次重试后仍未找到最新工资数据路径
2025-08-15 21:38:48.235 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1488 | 延迟自动选择最新数据失败，可能没有可用数据
2025-08-15 21:38:48.236 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7961 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-15 21:38:48.237 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7961 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-15 21:38:48.352 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:181 | 断点切换: sm (宽度: 1280px)
2025-08-15 21:38:48.353 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2095 | MainWorkspaceArea 响应式适配: sm
2025-08-15 21:38:48.360 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2050 | 开始获取最新工资数据路径...
2025-08-15 21:38:48.361 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2055 | 未找到任何工资数据表
2025-08-15 21:38:48.362 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1400 | 🔧 [P1-2修复] 多次重试后仍未找到最新工资数据路径
2025-08-15 21:38:48.864 | INFO     | src.gui.prototype.prototype_main_window:delayed_heavy_fixes:9400 | UI亮度问题检测到并修复: ['table_opacity_in_stylesheet']
2025-08-15 21:38:48.864 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9310 | 🔧 [P1-2] 开始智能显示亮度修复（增强防护）
2025-08-15 21:38:48.868 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9324 | 🔧 [P0-1] 检测到亮度问题: ['table_opacity_in_stylesheet']
2025-08-15 21:38:48.868 | INFO     | src.gui.prototype.prototype_main_window:_apply_brightness_fixes:9858 | 🔧 [P1-2] 开始应用增强版亮度修复: ['table_opacity_in_stylesheet']
2025-08-15 21:38:48.888 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:9330 | 🔧 [P0-1] 智能显示亮度修复完成
2025-08-15 21:38:49.234 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2050 | 开始获取最新工资数据路径...
2025-08-15 21:38:49.236 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2055 | 未找到任何工资数据表
2025-08-15 21:38:49.239 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_get_latest_path_with_retry:1603 | 🔧 [P1-2修复] 3次重试已用尽，可能数据导入尚未完成
2025-08-15 21:38:49.240 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1400 | 🔧 [P1-2修复] 多次重试后仍未找到最新工资数据路径
2025-08-15 21:39:00.549 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data:654 | 数据导入功能被触发，发出 import_requested 信号。
2025-08-15 21:39:00.549 | INFO     | src.gui.prototype.prototype_main_window:_get_suggested_target_path:8321 | 检测到当前在工资表TAB，生成工资表默认路径
2025-08-15 21:39:00.552 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:6031 | 接收到数据导入请求，推断的目标路径: 工资表 > 2025年 > 08月 > 全部在职人员。打开导入对话框。
2025-08-15 21:39:00.555 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-08-15 21:39:00.556 | INFO     | src.modules.data_import.multi_sheet_importer:__init__:75 | 多Sheet导入器初始化完成
2025-08-15 21:39:00.558 | INFO     | src.gui.widgets.target_selection_widget:_save_config:285 | 配置保存成功
2025-08-15 21:39:00.576 | INFO     | src.gui.widgets.target_selection_widget:set_target_from_path:505 | 从路径设置目标: 工资表 > 2025年 > 08月 > 全部在职人员
2025-08-15 21:39:00.635 | INFO     | src.gui.main_dialogs:_get_template_fields:1880 | 🔧 [P3-修复] ConfigSyncManager未初始化，使用默认字段模板
2025-08-15 21:39:00.637 | INFO     | src.gui.main_dialogs:_init_field_mapping:1867 | 🔧 [P3-修复] 未找到字段模板，使用内置默认字段列表
2025-08-15 21:39:00.704 | INFO     | src.modules.data_import.import_defaults_manager:load_settings:86 | 未找到用户设置文件，使用默认设置
2025-08-15 21:39:00.705 | INFO     | src.modules.data_import.import_defaults_manager:get_smart_defaults_for_category:244 | 检测到工资表导入，设置table_template为salary_data
2025-08-15 21:39:00.708 | INFO     | src.gui.main_dialogs:_apply_default_settings:2232 | 已应用默认设置: {'start_row': 1, 'import_mode': 'multi_sheet', 'auto_match_sheet': True, 'include_header': True, 'skip_empty_rows': True, 'create_table_mode': 'sheet_name', 'import_strategy': 'separate_tables', 'table_template': 'salary_data'}
2025-08-15 21:39:00.709 | INFO     | src.gui.main_dialogs:_setup_tooltips:2487 | 工具提示设置完成
2025-08-15 21:39:00.710 | INFO     | src.gui.main_dialogs:_setup_shortcuts:2526 | 快捷键设置完成
2025-08-15 21:39:00.711 | INFO     | src.gui.main_dialogs:__init__:78 | 数据导入对话框初始化完成。
2025-08-15 21:39:00.711 | INFO     | src.modules.data_import.multi_sheet_importer:set_config_sync_manager:84 | 🔧 [修复] ConfigSyncManager已设置到MultiSheetImporter
2025-08-15 21:39:00.712 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:6042 | 🔧 [P0-修复] ConfigSyncManager已设置到数据导入对话框
2025-08-15 21:39:14.451 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:178 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-08-15 21:39:15.854 | INFO     | src.modules.data_import.smart_sheet_matcher:record_user_choice:372 | 记录用户选择: 全部在职人员 -> 全部在职人员工资表
2025-08-15 21:39:15.854 | INFO     | src.gui.main_dialogs:_auto_select_sheet_by_category:2267 | 根据人员类别 '全部在职人员' 自动选择工作表: 全部在职人员工资表 (匹配类型: fuzzy, 得分: 0.60)
2025-08-15 21:39:26.524 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:178 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-08-15 21:39:26.743 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-08-15 21:39:26.743 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:216 | 开始导入多Sheet Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-08-15 21:39:26.930 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:233 | 检测到 4 个工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-08-15 21:39:26.930 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-08-15 21:39:26.946 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-15 21:39:27.071 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 16列 (列过滤: 否)
2025-08-15 21:39:27.087 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-15 21:39:27.087 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 16 列 (原始 16 列)
2025-08-15 21:39:27.087 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 3行 x 16列
2025-08-15 21:39:27.118 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:599 | 数据质量检查: 已过滤1条姓名为空的记录
2025-08-15 21:39:27.118 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:642 | 数据过滤完成: 原始3条记录，过滤1条无效记录，有效记录2条
2025-08-15 21:39:27.118 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 2行 × 16列
2025-08-15 21:39:27.118 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:530 | 工作表 离休人员工资表 使用智能默认配置
2025-08-15 21:39:27.118 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:818 | 为Sheet '离休人员工资表' 生成智能默认配置: 1 个必需字段
2025-08-15 21:39:27.118 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:141 | 为模板 retired_employees 生成了 21 个字段映射
2025-08-15 21:39:27.118 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:547 | 使用专用模板 retired_employees 生成字段映射: 21 个字段
2025-08-15 21:39:27.137 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:665 | 🚀 [配置同步] 已发布配置变更事件: salary_data_2025_08_retired_employees
2025-08-15 21:39:27.137 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:669 | 完整字段映射保存成功: salary_data_2025_08_retired_employees
2025-08-15 21:39:27.137 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:594 | 为表 salary_data_2025_08_retired_employees 生成标准化字段映射: 21 个字段
2025-08-15 21:39:27.149 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:615 | Sheet 离休人员工资表 数据处理完成: 2 行
2025-08-15 21:39:27.149 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:417 | Sheet '离休人员工资表' 自动检测到模板类型: retired_employees
2025-08-15 21:39:27.149 | INFO     | src.modules.data_storage.dynamic_table_manager:_create_table_from_schema:1052 | 成功创建表: salary_data_2025_08_retired_employees
2025-08-15 21:39:27.165 | INFO     | src.modules.data_storage.dynamic_table_manager:create_specialized_salary_table:668 | 专用工资数据表创建成功: salary_data_2025_08_retired_employees (模板: retired_employees)
2025-08-15 21:39:27.196 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2149 | [FIX] [修复标识] 导入字段映射加载完成: 28 个映射规则
2025-08-15 21:39:27.196 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2157 | [FIX] [修复标识] 保留未映射列: ['data_source', 'import_time']
2025-08-15 21:39:27.196 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2166 | [FIX] [修复标识] 导入列名映射成功: 18 个字段已映射
2025-08-15 21:39:27.212 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2303 | 成功向表 salary_data_2025_08_retired_employees 保存 2 条数据。
2025-08-15 21:39:27.259 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-15 21:39:27.368 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-15 21:39:27.368 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 27 列 (原始 27 列)
2025-08-15 21:39:27.384 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:599 | 数据质量检查: 已过滤1条姓名为空的记录
2025-08-15 21:39:27.384 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:642 | 数据过滤完成: 原始14条记录，过滤1条无效记录，有效记录13条
2025-08-15 21:39:27.384 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:530 | 工作表 退休人员工资表 使用智能默认配置
2025-08-15 21:39:27.384 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:818 | 为Sheet '退休人员工资表' 生成智能默认配置: 1 个必需字段
2025-08-15 21:39:27.384 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:141 | 为模板 pension_employees 生成了 32 个字段映射
2025-08-15 21:39:27.384 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:547 | 使用专用模板 pension_employees 生成字段映射: 32 个字段
2025-08-15 21:39:27.384 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:665 | 🚀 [配置同步] 已发布配置变更事件: salary_data_2025_08_pension_employees
2025-08-15 21:39:27.384 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:669 | 完整字段映射保存成功: salary_data_2025_08_pension_employees
2025-08-15 21:39:27.384 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:594 | 为表 salary_data_2025_08_pension_employees 生成标准化字段映射: 32 个字段
2025-08-15 21:39:27.399 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:615 | Sheet 退休人员工资表 数据处理完成: 13 行
2025-08-15 21:39:27.399 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:417 | Sheet '退休人员工资表' 自动检测到模板类型: pension_employees
2025-08-15 21:39:27.415 | INFO     | src.modules.data_storage.dynamic_table_manager:_create_table_from_schema:1052 | 成功创建表: salary_data_2025_08_pension_employees
2025-08-15 21:39:27.415 | INFO     | src.modules.data_storage.dynamic_table_manager:create_specialized_salary_table:668 | 专用工资数据表创建成功: salary_data_2025_08_pension_employees (模板: pension_employees)
2025-08-15 21:39:27.430 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2149 | [FIX] [修复标识] 导入字段映射加载完成: 32 个映射规则
2025-08-15 21:39:27.430 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2157 | [FIX] [修复标识] 保留未映射列: ['data_source', 'import_time']
2025-08-15 21:39:27.430 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2166 | [FIX] [修复标识] 导入列名映射成功: 29 个字段已映射
2025-08-15 21:39:27.446 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2303 | 成功向表 salary_data_2025_08_pension_employees 保存 13 条数据。
2025-08-15 21:39:27.446 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-15 21:39:27.587 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-15 21:39:27.602 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 23 列 (原始 23 列)
2025-08-15 21:39:27.602 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:599 | 数据质量检查: 已过滤1条姓名为空的记录
2025-08-15 21:39:27.602 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:642 | 数据过滤完成: 原始1397条记录，过滤1条无效记录，有效记录1396条
2025-08-15 21:39:27.602 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:530 | 工作表 全部在职人员工资表 使用智能默认配置
2025-08-15 21:39:27.602 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:818 | 为Sheet '全部在职人员工资表' 生成智能默认配置: 2 个必需字段
2025-08-15 21:39:27.602 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:141 | 为模板 active_employees 生成了 28 个字段映射
2025-08-15 21:39:27.602 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:547 | 使用专用模板 active_employees 生成字段映射: 28 个字段
2025-08-15 21:39:27.618 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:665 | 🚀 [配置同步] 已发布配置变更事件: salary_data_2025_08_active_employees
2025-08-15 21:39:27.618 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:669 | 完整字段映射保存成功: salary_data_2025_08_active_employees
2025-08-15 21:39:27.618 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:594 | 为表 salary_data_2025_08_active_employees 生成标准化字段映射: 28 个字段
2025-08-15 21:39:27.634 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:615 | Sheet 全部在职人员工资表 数据处理完成: 1396 行
2025-08-15 21:39:27.634 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:417 | Sheet '全部在职人员工资表' 自动检测到模板类型: active_employees
2025-08-15 21:39:27.634 | INFO     | src.modules.data_storage.dynamic_table_manager:_create_table_from_schema:1052 | 成功创建表: salary_data_2025_08_active_employees
2025-08-15 21:39:27.634 | INFO     | src.modules.data_storage.dynamic_table_manager:create_specialized_salary_table:668 | 专用工资数据表创建成功: salary_data_2025_08_active_employees (模板: active_employees)
2025-08-15 21:39:27.665 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2149 | [FIX] [修复标识] 导入字段映射加载完成: 28 个映射规则
2025-08-15 21:39:27.665 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2157 | [FIX] [修复标识] 保留未映射列: ['data_source', 'import_time']
2025-08-15 21:39:27.665 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2166 | [FIX] [修复标识] 导入列名映射成功: 25 个字段已映射
2025-08-15 21:39:27.696 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2303 | 成功向表 salary_data_2025_08_active_employees 保存 1396 条数据。
2025-08-15 21:39:27.696 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-15 21:39:27.805 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-15 21:39:27.805 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 21 列 (原始 21 列)
2025-08-15 21:39:27.821 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:599 | 数据质量检查: 已过滤1条姓名为空的记录
2025-08-15 21:39:27.821 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:642 | 数据过滤完成: 原始63条记录，过滤1条无效记录，有效记录62条
2025-08-15 21:39:27.821 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:530 | 工作表 A岗职工 使用智能默认配置
2025-08-15 21:39:27.821 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:818 | 为Sheet 'A岗职工' 生成智能默认配置: 2 个必需字段
2025-08-15 21:39:27.821 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:141 | 为模板 a_grade_employees 生成了 26 个字段映射
2025-08-15 21:39:27.821 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:547 | 使用专用模板 a_grade_employees 生成字段映射: 26 个字段
2025-08-15 21:39:27.836 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:665 | 🚀 [配置同步] 已发布配置变更事件: salary_data_2025_08_a_grade_employees
2025-08-15 21:39:27.836 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:669 | 完整字段映射保存成功: salary_data_2025_08_a_grade_employees
2025-08-15 21:39:27.836 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:594 | 为表 salary_data_2025_08_a_grade_employees 生成标准化字段映射: 26 个字段
2025-08-15 21:39:27.836 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:615 | Sheet A岗职工 数据处理完成: 62 行
2025-08-15 21:39:27.852 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:417 | Sheet 'A岗职工' 自动检测到模板类型: a_grade_employees
2025-08-15 21:39:27.852 | INFO     | src.modules.data_storage.dynamic_table_manager:_create_table_from_schema:1052 | 成功创建表: salary_data_2025_08_a_grade_employees
2025-08-15 21:39:27.852 | INFO     | src.modules.data_storage.dynamic_table_manager:create_specialized_salary_table:668 | 专用工资数据表创建成功: salary_data_2025_08_a_grade_employees (模板: a_grade_employees)
2025-08-15 21:39:27.868 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2149 | [FIX] [修复标识] 导入字段映射加载完成: 26 个映射规则
2025-08-15 21:39:27.868 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2157 | [FIX] [修复标识] 保留未映射列: ['data_source', 'import_time']
2025-08-15 21:39:27.884 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2166 | [FIX] [修复标识] 导入列名映射成功: 23 个字段已映射
2025-08-15 21:39:27.899 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:2303 | 成功向表 salary_data_2025_08_a_grade_employees 保存 62 条数据。
2025-08-15 21:39:27.899 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:252 | 多Sheet导入完成: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_08_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2025_08_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_08_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2025_08_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_08_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2025_08_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2025_08_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2025_08_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 0}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']}
2025-08-15 21:39:27.899 | INFO     | src.gui.main_dialogs:_execute_multi_sheet_import:1487 | 多Sheet导入成功: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_08_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2025_08_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_08_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2025_08_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_08_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2025_08_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2025_08_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2025_08_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 0}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工'], 'import_mode': 'multi_sheet', 'strategy': 'separate_tables', 'data_period': '2025-08', 'data_description': '2025年8月工资数据', 'file_path': 'C:/test/salary_changes/salary_changes/data/工资表/2025年5月份正式工工资（报财务)  最终版.xls', 'target_path': '工资表 > 2025年 > 08月 > 全部在职人员'}
2025-08-15 21:39:27.899 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:6061 | 开始处理导入结果: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_08_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2025_08_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_08_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2025_08_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_08_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2025_08_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2025_08_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2025_08_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 0}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工'], 'import_mode': 'multi_sheet', 'strategy': 'separate_tables', 'data_period': '2025-08', 'data_description': '2025年8月工资数据', 'file_path': 'C:/test/salary_changes/salary_changes/data/工资表/2025年5月份正式工工资（报财务)  最终版.xls', 'target_path': '工资表 > 2025年 > 08月 > 全部在职人员'}
2025-08-15 21:39:27.915 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:6072 | 导入模式: multi_sheet, 目标路径: '工资表 > 2025年 > 08月 > 全部在职人员'
2025-08-15 21:39:27.931 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:6090 | 接收到导入数据, 来源: 未知来源, 目标路径: 工资表 > 2025年 > 08月 > 全部在职人员
2025-08-15 21:39:27.931 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:refresh_navigation_data:1845 | 🔧 [P2-2] 开始增强版导航数据刷新...
2025-08-15 21:39:27.931 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > 全部在职人员', '工资表', '工资表 > 2025年', '工资表 > 2025年 > 05月']
2025-08-15 21:39:27.931 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7370 | 导航变化: 工资表 > 2025年 > 05月 > 全部在职人员
2025-08-15 21:39:27.946 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10489 | 🚫 [用户要求] 表格切换不显示加载条:  -> None
2025-08-15 21:39:27.961 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10505 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-08-15 21:39:27.961 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8297 | 🔧 [表名生成] 从完整路径生成表名: ['工资表', '2025年', '05月', '全部在职人员'] -> salary_data_2025_05_active_employees
2025-08-15 21:39:27.961 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-15 21:39:27.978 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:362 | 已清理表 salary_data_2025_05_active_employees 的缓存
2025-08-15 21:39:27.978 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:11175 | 已注册 4 个表格到表头管理器
2025-08-15 21:39:27.978 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1188 | 🔧 [P1-3] 开始智能表头重影检测，共 4 个表格
2025-08-15 21:39:28.012 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1261 | 🔧 [P1-3] 智能表头重影检测完成，耗时 33.80ms
2025-08-15 21:39:28.024 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1262 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-08-15 21:39:28.024 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7586 | 🆕 使用新架构加载数据: salary_data_2025_05_active_employees（通过事件系统）
2025-08-15 21:39:28.024 | INFO     | src.services.table_data_service:load_table_data:366 | 加载表格数据: 页码: 1
2025-08-15 21:39:28.024 | INFO     | src.core.unified_data_request_manager:request_table_data:212 | 开始处理数据请求
2025-08-15 21:39:28.024 | ERROR    | src.core.unified_data_request_manager:_find_similar_table_names_by_type:385 | 🔧 [P0修复] 按类型查找相似表名失败: 'DynamicTableManager' object has no attribute 'get_all_table_names'
2025-08-15 21:39:28.024 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7595 | 🔧 [数据流追踪] 数据表 salary_data_2025_05_active_employees 尚未创建，显示空表格等待数据导入
2025-08-15 21:39:28.024 | INFO     | src.gui.prototype.prototype_main_window:_get_table_headers_by_type:8411 | 🔧 [数据流追踪] 使用在职人员表头: 22个字段
2025-08-15 21:39:28.024 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8494 | 🔧 [数据流追踪] 使用表 salary_data_2025_05_active_employees 的专用表头: 22个字段
2025-08-15 21:39:28.024 | INFO     | src.gui.prototype.prototype_main_window:set_data:845 | 空数据输入发生 3 次（2s 窗口），将显示空表提示
2025-08-15 21:39:28.024 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2322 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-15 21:39:28.024 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-15 21:39:28.024 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4487 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-15 21:39:28.040 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5300 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-08-15 21:39:28.040 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2825 | 🔧 [P0修复] 检测到空表且表头为中文，跳过字段映射: 22个表头
2025-08-15 21:39:28.040 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2904 | 表格格式化完成: default_table, 类型: active_employees
2025-08-15 21:39:28.040 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-15 21:39:28.040 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-15 21:39:28.040 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-15 21:39:28.040 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-15 21:39:28.040 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:442 | 表格数据已设置: 0 行, 22 列
2025-08-15 21:39:28.055 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 0 行, 耗时: 31.1ms
2025-08-15 21:39:28.066 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7926 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-15 21:39:28.067 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7939 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-15 21:39:28.071 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: default_table (22/22 列)
2025-08-15 21:39:28.072 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-15 21:39:28.073 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2346 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-15 21:39:28.080 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-15 21:39:28.081 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8516 | 已显示标准空表格，表头数量: 22
2025-08-15 21:39:28.099 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1252 | 🔧 [深度修复] 找到 4 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-15 21:39:28.104 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1252 | 🔧 [深度修复] 找到 4 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-15 21:39:28.110 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_execute_salary_data_load:1817 | 动态加载了 1 个月份的工资数据导航
2025-08-15 21:39:28.115 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_change_navigation_tree_data:1956 | 在 table_metadata 中未找到任何 'change_data' 类型的表
2025-08-15 21:39:28.126 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 05月 > 全部在职人员', '工资表 > 2025年', '工资表', '工资表 > 2025年 > 08月 > 全部在职人员', '工资表 > 2025年 > 08月']
2025-08-15 21:39:28.127 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7370 | 导航变化: 工资表 > 2025年 > 08月 > 全部在职人员
2025-08-15 21:39:28.128 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10489 | 🚫 [用户要求] 表格切换不显示加载条: salary_data_2025_05_active_employees -> None
2025-08-15 21:39:28.130 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10505 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-08-15 21:39:28.135 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8297 | 🔧 [表名生成] 从完整路径生成表名: ['工资表', '2025年', '08月', '全部在职人员'] -> salary_data_2025_08_active_employees
2025-08-15 21:39:28.136 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-15 21:39:28.137 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:362 | 已清理表 salary_data_2025_08_active_employees 的缓存
2025-08-15 21:39:28.141 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:11175 | 已注册 4 个表格到表头管理器
2025-08-15 21:39:28.141 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7586 | 🆕 使用新架构加载数据: salary_data_2025_08_active_employees（通过事件系统）
2025-08-15 21:39:28.142 | INFO     | src.core.unified_data_request_manager:request_table_data:212 | 开始处理数据请求
2025-08-15 21:39:28.143 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:805 | 正在从表 salary_data_2025_08_active_employees 分页获取数据（支持排序）: 第1页, 每页50条, 排序=0列
2025-08-15 21:39:28.155 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:921 | 成功从表 salary_data_2025_08_active_employees 获取第1页数据（含排序）: 50 行，总计1396行
2025-08-15 21:39:28.157 | INFO     | src.core.unified_data_request_manager:request_table_data:262 | 数据请求处理完成: 字段=28, 行数=50, 耗时=15.6ms
2025-08-15 21:39:28.263 | INFO     | src.core.event_bus:__init__:229 | 事件总线初始化完成
2025-08-15 21:39:28.264 | INFO     | src.core.unified_state_manager:_load_state:560 | 状态文件不存在，使用默认状态
2025-08-15 21:39:28.267 | INFO     | src.core.unified_state_manager:__init__:184 | 统一状态管理器初始化完成
2025-08-15 21:39:28.268 | INFO     | src.modules.format_management.field_registry:__init__:95 | 🏷️ [字段注册] 字段注册系统初始化: state/data/field_mappings.json
2025-08-15 21:39:28.269 | INFO     | src.modules.format_management.format_renderer:__init__:83 | 🎨 [格式渲染] 格式渲染器初始化完成
2025-08-15 21:39:28.271 | INFO     | src.modules.format_management.unified_format_manager:_setup_event_listeners:881 | 🎯 [事件驱动] 事件监听器设置完成
2025-08-15 21:39:28.273 | INFO     | src.core.unified_state_manager:update_global_state:350 | 全局状态已更新: StateChangeType.DATA_RELOADED
2025-08-15 21:39:28.274 | INFO     | src.modules.format_management.unified_format_manager:_sync_to_state_manager:896 | 🎯 [统一状态管理] 状态同步完成
2025-08-15 21:39:28.279 | INFO     | src.modules.format_management.format_config:load_config:385 | 🔧 [格式配置] 配置文件不存在，创建默认配置
2025-08-15 21:39:28.282 | INFO     | src.modules.format_management.format_config:save_config:435 | 🔧 [格式配置] 配置文件保存成功
2025-08-15 21:39:28.283 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:737 | 🔧 [自动修复] 为表 salary_data_2025_07_active_employees 自动生成字段类型配置
2025-08-15 21:39:28.285 | INFO     | src.modules.format_management.field_registry:_find_matching_default_config:921 | 🔧 [智能匹配] 表 salary_data_2025_07_active_employees 匹配成功: active_employees (得分:220, 原因:精确后缀匹配:active_employees,英文关键词:active,模式匹配:.*active.*employee.*,分词匹配:active)
2025-08-15 21:39:28.285 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:755 | 🔧 [自动修复] 使用匹配的默认配置: salary_data_2025_07_active_employees
2025-08-15 21:39:28.286 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:737 | 🔧 [自动修复] 为表 salary_data_2025_08_retired_employees 自动生成字段类型配置
2025-08-15 21:39:28.287 | INFO     | src.modules.format_management.field_registry:_find_matching_default_config:921 | 🔧 [智能匹配] 表 salary_data_2025_08_retired_employees 匹配成功: retired_employees (得分:220, 原因:精确后缀匹配:retired_employees,英文关键词:retired,模式匹配:.*retired.*employee.*,分词匹配:retired)
2025-08-15 21:39:28.294 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:755 | 🔧 [自动修复] 使用匹配的默认配置: salary_data_2025_08_retired_employees
2025-08-15 21:39:28.295 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:737 | 🔧 [自动修复] 为表 salary_data_2025_08_pension_employees 自动生成字段类型配置
2025-08-15 21:39:28.296 | INFO     | src.modules.format_management.field_registry:_find_matching_default_config:921 | 🔧 [智能匹配] 表 salary_data_2025_08_pension_employees 匹配成功: pension_employees (得分:300, 原因:精确后缀匹配:pension_employees,英文关键词:pension,英文关键词:pension_employees,模式匹配:.*pension.*employee.*,模式匹配:.*pension.*,分词匹配:pension)
2025-08-15 21:39:28.297 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:755 | 🔧 [自动修复] 使用匹配的默认配置: salary_data_2025_08_pension_employees
2025-08-15 21:39:28.297 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:737 | 🔧 [自动修复] 为表 salary_data_2025_08_active_employees 自动生成字段类型配置
2025-08-15 21:39:28.298 | INFO     | src.modules.format_management.field_registry:_find_matching_default_config:921 | 🔧 [智能匹配] 表 salary_data_2025_08_active_employees 匹配成功: active_employees (得分:220, 原因:精确后缀匹配:active_employees,英文关键词:active,模式匹配:.*active.*employee.*,分词匹配:active)
2025-08-15 21:39:28.299 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:755 | 🔧 [自动修复] 使用匹配的默认配置: salary_data_2025_08_active_employees
2025-08-15 21:39:28.302 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:737 | 🔧 [自动修复] 为表 salary_data_2025_08_a_grade_employees 自动生成字段类型配置
2025-08-15 21:39:28.305 | INFO     | src.modules.format_management.field_registry:_find_matching_default_config:921 | 🔧 [智能匹配] 表 salary_data_2025_08_a_grade_employees 匹配成功: a_grade_employees (得分:330, 原因:精确后缀匹配:a_grade_employees,英文关键词:a_grade,英文关键词:a_grade_employees,模式匹配:.*a.*grade.*employee.*,优先级加分:100)
2025-08-15 21:39:28.311 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:755 | 🔧 [自动修复] 使用匹配的默认配置: salary_data_2025_08_a_grade_employees
2025-08-15 21:39:28.314 | WARNING  | src.modules.format_management.field_registry:_merge_default_mappings:778 | 🔧 [架构优化] 配置一致性警告: ["表 salary_data_2025_08_active_employees 有字段缺少类型定义: {'sequence_number'}", "表 salary_data_2025_08_active_employees 有多余的字段类型定义: {'sequence', 'row_number'}", '🔧 [P3修复] 发现6个字段类型不一致问题，建议检查数值字段的类型定义', '🔧 [P3修复] 示例: 表 part_time_employees 字段 total_salary (currency -> float)', '🔧 [P3修复] 示例: 表 contract_employees 字段 base_salary (currency -> float)', '🔧 [P3修复] 示例: 表 contract_employees 字段 performance_bonus (currency -> float)', '🔧 [P3修复] 11个表的existing_display_fields为空: salary_data_2025_07_active_employees, active_employees, salary_data_2025_08_retired_employees 等11个表']
2025-08-15 21:39:28.315 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2060 | 🔧 [P1-1修复] 表 active_employees existing_display_fields已自动修复: ['employee_id', 'employee_name', 'department', 'employee_type', 'employee_type_code']...
2025-08-15 21:39:28.316 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2060 | 🔧 [P1-1修复] 表 salary_data_2025_08_retired_employees existing_display_fields已自动修复: ['employee_id', 'employee_name', 'department', 'basic_retirement_salary', 'balance_allowance']...
2025-08-15 21:39:28.317 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2060 | 🔧 [P1-1修复] 表 salary_data_2025_08_pension_employees existing_display_fields已自动修复: ['employee_id', 'employee_name', 'department', 'employee_type_code', 'basic_retirement_salary']...
2025-08-15 21:39:28.318 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2060 | 🔧 [P1-1修复] 表 salary_data_2025_08_active_employees existing_display_fields已自动修复: ['employee_id', 'employee_name', 'department', 'employee_type', 'employee_type_code']...
2025-08-15 21:39:28.320 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2060 | 🔧 [P1-1修复] 表 salary_data_2025_08_a_grade_employees existing_display_fields已自动修复: ['employee_id', 'employee_name', 'department', 'employee_type', 'employee_type_code']...
2025-08-15 21:39:28.327 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2060 | 🔧 [P1-1修复] 表 retired_employees existing_display_fields已自动修复: ['employee_id', 'employee_name', 'department', 'basic_retirement_salary', 'balance_allowance']...
2025-08-15 21:39:28.328 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2060 | 🔧 [P1-1修复] 表 pension_employees existing_display_fields已自动修复: ['employee_id', 'employee_name', 'department', 'employee_type_code', 'basic_retirement_salary']...
2025-08-15 21:39:28.329 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2060 | 🔧 [P1-1修复] 表 part_time_employees existing_display_fields已自动修复: ['employee_id', 'employee_name', 'department', 'hourly_rate', 'hours_worked']...
2025-08-15 21:39:28.331 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2060 | 🔧 [P1-1修复] 表 contract_employees existing_display_fields已自动修复: ['employee_id', 'employee_name', 'department', 'base_salary', 'performance_bonus']...
2025-08-15 21:39:28.334 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2060 | 🔧 [P1-1修复] 表 a_grade_employees existing_display_fields已自动修复: ['employee_id', 'employee_name', 'department', 'employee_type', 'employee_type_code']...
2025-08-15 21:39:28.335 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2070 | 🔧 [架构优化] 应用智能修复策略，执行 11 项修复操作
2025-08-15 21:39:28.349 | INFO     | src.modules.format_management.field_registry:save_mappings:662 | 🏷️ [字段注册] 字段映射保存成功
2025-08-15 21:39:28.352 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:786 | 🔧 [架构优化] 配置修复已持久化保存，变更数量: 5
2025-08-15 21:39:28.354 | INFO     | src.modules.format_management.field_registry:load_mappings:629 | 🏷️ [字段注册] 字段映射加载成功
2025-08-15 21:39:28.355 | INFO     | src.modules.format_management.unified_format_manager:_initialize_system:146 | 🎯 [统一格式管理] 系统初始化完成
2025-08-15 21:39:28.356 | INFO     | src.modules.format_management.unified_format_manager:__init__:130 | 🎯 [统一格式管理] 统一格式管理器初始化完成（新架构版本）
2025-08-15 21:39:28.357 | INFO     | src.modules.format_management.unified_format_manager:__init__:1079 | 🔧 [单例优化] 单例统一格式管理器初始化完成
2025-08-15 21:39:28.358 | INFO     | src.services.table_data_service:load_table_data:462 | [新架构] 使用统一格式管理器（单例优化）
2025-08-15 21:39:28.359 | INFO     | src.modules.format_management.format_renderer:render_dataframe:107 | 🎯 [格式渲染] 隐藏字段配置: ['updated_at', 'id', 'created_at', 'sequence_number', 'row_number']
2025-08-15 21:39:28.362 | INFO     | src.modules.format_management.format_renderer:render_dataframe:115 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-08-15 21:39:28.387 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-08-15 21:39:28.389 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-08-15 21:39:28.394 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 月份 -> 英文名: month -> 类型: month_string
2025-08-15 21:39:28.401 | INFO     | src.modules.format_management.field_registry:get_display_fields:1554 | 🔧 [P0-2修复] display_order转换完成: 25个字段，原始字段数: 25
2025-08-15 21:39:28.402 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | 🔧 [DEBUG] table_type=active_employees, display_fields=25个字段
2025-08-15 21:39:28.403 | INFO     | src.modules.format_management.format_renderer:render_dataframe:211 | 🔧 [P0修复] 配置字段匹配完成: 成功匹配 25/25 个字段
2025-08-15 21:39:28.404 | INFO     | src.modules.format_management.format_renderer:render_dataframe:219 | 🔧 [P0-关键修复] 保留未配置的业务字段: 3个
2025-08-15 21:39:28.407 | INFO     | src.modules.format_management.format_renderer:render_dataframe:224 | 🔧 [P0修复] 字段重排完成: 原始28个 -> 最终28个字段
2025-08-15 21:39:28.411 | INFO     | src.modules.format_management.format_renderer:render_dataframe:240 | 🎨 [格式渲染] DataFrame格式化完成: active_employees, 行数: 50, 列数: 28
2025-08-15 21:39:28.413 | INFO     | src.modules.format_management.unified_format_manager:format_data:367 | 🎯 [统一格式管理] 数据格式化完成: active_employees, 行数: 50, 列数: 28
2025-08-15 21:39:28.415 | INFO     | src.core.unified_state_manager:update_table_state:233 | 表状态已更新: salary_data_2025_08_active_employees, 变更类型: None
2025-08-15 21:39:28.416 | INFO     | src.services.table_data_service:load_table_data:512 | [修复数据发布] 数据加载成功，发布更新事件: 50行
2025-08-15 21:39:28.417 | INFO     | src.services.table_data_service:load_table_data:516 | 📨[data_event] publish | table=salary_data_2025_08_active_employees | rows=50 | page=1 | size=50 | total=1396 | req_type=RequestType.INITIAL_LOAD | request_id=SV-1755265168417-2f8585da
2025-08-15 21:39:28.418 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3785 | 📥[data_event] received | table=salary_data_2025_08_active_employees | request_id=SV-1755265168417-2f8585da
2025-08-15 21:39:28.419 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3817 | 数据内容: 50行 x 28列
2025-08-15 21:39:28.420 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3843 | 🔧 [数据更新] 接收到initial_load操作的数据更新事件: salary_data_2025_08_active_employees, 50行
2025-08-15 21:39:28.436 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7327 | 表 salary_data_2025_08_active_employees 没有用户偏好设置，显示所有可见字段
2025-08-15 21:39:28.440 | INFO     | src.gui.prototype.prototype_main_window:set_data:873 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-08-15 21:39:28.456 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 50行，表名: salary_data_2025_08_active_employees
2025-08-15 21:39:28.458 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=19990089, 薪资=2375.00
2025-08-15 21:39:28.459 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=20161565, 薪资=1696.00
2025-08-15 21:39:28.461 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:5271 | 最大可见行数已更新: 1000 -> 50
2025-08-15 21:39:28.461 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5324 | 根据数据量(50)自动调整最大可见行数为: 50
2025-08-15 21:39:28.466 | INFO     | src.modules.format_management.format_renderer:render_dataframe:107 | 🎯 [格式渲染] 隐藏字段配置: ['updated_at', 'id', 'created_at', 'sequence_number', 'row_number']
2025-08-15 21:39:28.471 | INFO     | src.modules.format_management.format_renderer:render_dataframe:115 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-08-15 21:39:28.485 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-08-15 21:39:28.487 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-08-15 21:39:28.489 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 月份 -> 英文名: month -> 类型: month_string
2025-08-15 21:39:28.490 | INFO     | src.modules.format_management.field_registry:get_display_fields:1554 | 🔧 [P0-2修复] display_order转换完成: 25个字段，原始字段数: 25
2025-08-15 21:39:28.491 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | 🔧 [DEBUG] table_type=active_employees, display_fields=25个字段
2025-08-15 21:39:28.492 | INFO     | src.modules.format_management.format_renderer:render_dataframe:211 | 🔧 [P0修复] 配置字段匹配完成: 成功匹配 24/25 个字段
2025-08-15 21:39:28.494 | INFO     | src.modules.format_management.format_renderer:render_dataframe:224 | 🔧 [P0修复] 字段重排完成: 原始24个 -> 最终24个字段
2025-08-15 21:39:28.501 | INFO     | src.modules.format_management.format_renderer:render_dataframe:240 | 🎨 [格式渲染] DataFrame格式化完成: active_employees, 行数: 50, 列数: 24
2025-08-15 21:39:28.502 | INFO     | src.modules.format_management.unified_format_manager:format_data:367 | 🎯 [统一格式管理] 数据格式化完成: active_employees, 行数: 50, 列数: 24
2025-08-15 21:39:28.519 | INFO     | src.modules.format_management.format_config:load_config:385 | 🔧 [格式配置] 配置文件不存在，创建默认配置
2025-08-15 21:39:28.521 | INFO     | src.modules.format_management.format_config:save_config:435 | 🔧 [格式配置] 配置文件保存成功
2025-08-15 21:39:28.610 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 50行数据
2025-08-15 21:39:28.612 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:52 | [数据流追踪] 开始小数据集渲染: 50行 x 24列
2025-08-15 21:39:28.628 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:112 | [数据流追踪] 小数据集渲染完成: 耗时16.0ms, 平均每行0.32ms
2025-08-15 21:39:28.638 | INFO     | src.core.performance_metrics_collector:__init__:74 | 性能度量收集器初始化完成，存储路径: logs\performance_metrics.json
2025-08-15 21:39:28.641 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=50, 渲染时间=16.0ms, 策略=small_dataset
2025-08-15 21:39:28.642 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:279 | [数据流追踪] 选择小数据集渲染策略
2025-08-15 21:39:28.643 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-08-15 21:39:28.644 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-08-15 21:39:28.645 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-08-15 21:39:28.646 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-08-15 21:39:28.647 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-08-15 21:39:28.647 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 50
2025-08-15 21:39:28.648 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[0]: 工号=19990089, 薪资=2375.00
2025-08-15 21:39:28.649 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[1]: 工号=20161565, 薪资=1696.00
2025-08-15 21:39:28.656 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[2]: 工号=20191782, 薪资=1427.00
2025-08-15 21:39:28.657 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[3]: 工号=20151515, 薪资=2175.00
2025-08-15 21:39:28.658 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[4]: 工号=20181640, 薪资=2582.00
2025-08-15 21:39:28.659 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:479 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-08-15 21:39:28.661 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:417 | 🔧 [新架构] 排序管理器切换到表格: salary_data_2025_08_active_employees
2025-08-15 21:39:28.663 | INFO     | src.gui.prototype.widgets.column_sort_manager:_reload_field_mapping_for_table:448 | 🔧 [新架构] 为表格 salary_data_2025_08_active_employees 重新加载 28 个字段映射
2025-08-15 21:39:28.665 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 50 行, 耗时: 204.1ms
2025-08-15 21:39:28.673 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7926 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-15 21:39:28.674 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7939 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-15 21:39:28.675 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1723 | 🆕 [列宽保存] 未找到表格 salary_data_2025_08_active_employees 的列宽配置
2025-08-15 21:39:28.675 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-15 21:39:28.685 | INFO     | src.core.pagination_state_manager:__init__:63 | 🔧 [立即修复] 分页状态管理器初始化完成
2025-08-15 21:39:28.686 | INFO     | src.gui.prototype.prototype_main_window:set_data:997 | 📊[pagination-write] 跳过写入当前页数据量(total=50)，保持 total=0，等待数据事件纠正
2025-08-15 21:39:28.688 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3887 | 🔧 [P1-1修复] 数据已成功设置到UI，使用新映射表头: 50行, 24列
2025-08-15 21:39:28.688 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 24 个, 行号起始 1, 共 50 行
2025-08-15 21:39:28.690 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3903 | 🔧 [P0-新5修复] 表头强制刷新完成: salary_data_2025_08_active_employees, 传递参数: 24个表头
2025-08-15 21:39:28.691 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3907 | 🔧 [统一数据设置] 数据已成功设置到UI: 50行, 24列
2025-08-15 21:39:28.692 | INFO     | src.gui.widgets.pagination_widget:batch_update:472 | 📊[pagination-write] batch_update | src=_on_new_data_updated/initial_load | rid=SV-1755265168417-2f8585da | total: 0->1396, page: 1->1, size: 50->50, pages: 1->28
2025-08-15 21:39:28.693 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3941 | ✅[pagination-state] now | page=1 / pages=28 / total=1396 | rid=SV-1755265168417-2f8585da
2025-08-15 21:39:28.696 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3954 | 🔍 [FINAL修复] 最终调用set_pagination_state: 第1页, 记录1-50 | rid=SV-1755265168417-2f8585da
2025-08-15 21:39:28.708 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4166 | 🔍 [表格调试] ================== set_pagination_state 开始 ==================
2025-08-15 21:39:28.708 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4167 | 🔍 [表格调试] 收到分页状态参数: {'current_page': 1, 'page_size': 50, 'total_records': 1396, 'start_record': 1, 'end_record': 50}
2025-08-15 21:39:28.710 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4168 | 🔍 [表格调试] 当前表格行数: 50
2025-08-15 21:39:28.711 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4169 | 🔍 [表格调试] 当前表格列数: 24
2025-08-15 21:39:28.712 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4171 | 🔧 [分页行号修复] 接收分页状态: 第1页, 记录1-50
2025-08-15 21:39:28.719 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1723 | 🆕 [列宽保存] 未找到表格 salary_data_2025_08_active_employees 的列宽配置
2025-08-15 21:39:28.719 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4198 | 🔧 [P0-3修复] 分页时已恢复列宽设置: salary_data_2025_08_active_employees
2025-08-15 21:39:28.720 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4206 | 🔧 [分页行号修复] 表格状态: 当前行数=50, 期望行数=50
2025-08-15 21:39:28.722 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 24 个, 行号起始 1, 共 50 行
2025-08-15 21:39:28.723 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_force_update_pagination_row_numbers:4286 | 🔧 [分页行号修复] 强制更新完成: 起始记录1, 共50行
2025-08-15 21:39:28.724 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3958 | 🔍 [FINAL修复] set_pagination_state最终调用完成
2025-08-15 21:39:28.792 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:379 | 已清理所有缓存
2025-08-15 21:39:28.792 | INFO     | src.gui.prototype.prototype_main_window:_validate_component_state_consistency:10983 | ✅ [新架构] 组件状态一致性验证通过
2025-08-15 21:39:28.795 | INFO     | src.gui.prototype.prototype_main_window:complete_navigation_transition:10946 | 🆕 [新架构] 导航迁移完成
2025-08-15 21:39:28.796 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3972 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-08-15 21:39:28.797 | INFO     | src.services.table_data_service:load_table_data:539 | 📨[data_event] published
2025-08-15 21:39:28.798 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7591 | 🆕 新架构数据加载请求已发送，等待事件系统处理
2025-08-15 21:39:28.800 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:890 | 导航面板已重构：移除功能性导航，专注数据导航
2025-08-15 21:39:28.804 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_get_latest_change_data_path:2343 | 🔧 [P1修复] 未找到任何异动表元数据
2025-08-15 21:39:28.808 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_check_and_expand_new_change_data:2326 | 🔧 [P1修复] 未发现新的异动表数据
2025-08-15 21:39:28.808 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_navigation_state:1943 | 🔧 [P2-2] 开始恢复导航状态...
2025-08-15 21:39:28.810 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_navigation_state:1974 | 🔧 [P2-2] 导航状态恢复完成: 0个展开项
2025-08-15 21:39:28.813 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:refresh_navigation_data:1896 | 🔧 [P2-2] 增强版导航数据刷新完成
2025-08-15 21:39:28.814 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2095 | MainWorkspaceArea 响应式适配: sm
2025-08-15 21:39:28.822 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1395 | 🔧 [P1-2修复] 开始自动选择最新数据...
2025-08-15 21:39:28.825 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2050 | 开始获取最新工资数据路径...
2025-08-15 21:39:28.830 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1252 | 🔧 [深度修复] 找到 4 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-15 21:39:28.830 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2098 | 找到最新工资数据路径: 工资表 > 2025年 > 08月 > 全部在职人员
2025-08-15 21:39:28.832 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_get_latest_path_with_retry:1553 | 🔧 [P1-2修复] 成功获取到最新路径
2025-08-15 21:39:28.834 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7370 | 导航变化: 工资表 > 2025年 > 08月 > 全部在职人员
2025-08-15 21:39:28.839 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10489 | 🚫 [用户要求] 表格切换不显示加载条: salary_data_2025_08_active_employees -> None
2025-08-15 21:39:28.840 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10505 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-08-15 21:39:28.841 | INFO     | src.gui.prototype.prototype_main_window:_process_navigation_change:7419 | 检测到自动选择的最新数据，将显示特殊提示
2025-08-15 21:39:28.841 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8297 | 🔧 [表名生成] 从完整路径生成表名: ['工资表', '2025年', '08月', '全部在职人员'] -> salary_data_2025_08_active_employees
2025-08-15 21:39:28.842 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-15 21:39:28.843 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:362 | 已清理表 salary_data_2025_08_active_employees 的缓存
2025-08-15 21:39:28.847 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:11175 | 已注册 4 个表格到表头管理器
2025-08-15 21:39:28.855 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7586 | 🆕 使用新架构加载数据: salary_data_2025_08_active_employees（通过事件系统）
2025-08-15 21:39:28.856 | INFO     | src.services.table_data_service:load_table_data:390 | [缓存命中] 使用缓存数据: 第1页
2025-08-15 21:39:28.857 | INFO     | src.services.table_data_service:load_table_data:401 | 📨[data_event] publish(cache) | table=salary_data_2025_08_active_employees | rows=50 | page=1 | size=50 | total=1396 | request_id=SV-1755265168857-47964e0f-C
2025-08-15 21:39:28.858 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3785 | 📥[data_event] received | table=salary_data_2025_08_active_employees | request_id=SV-1755265168857-47964e0f-C
2025-08-15 21:39:28.858 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3817 | 数据内容: 50行 x 28列
2025-08-15 21:39:28.859 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3843 | 🔧 [数据更新] 接收到cached_load操作的数据更新事件: salary_data_2025_08_active_employees, 50行
2025-08-15 21:39:28.864 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7327 | 表 salary_data_2025_08_active_employees 没有用户偏好设置，显示所有可见字段
2025-08-15 21:39:28.870 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3887 | 🔧 [P1-1修复] 数据已成功设置到UI，使用新映射表头: 50行, 24列
2025-08-15 21:39:28.871 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 24 个, 行号起始 1, 共 50 行
2025-08-15 21:39:28.871 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3903 | 🔧 [P0-新5修复] 表头强制刷新完成: salary_data_2025_08_active_employees, 传递参数: 24个表头
2025-08-15 21:39:28.873 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3907 | 🔧 [统一数据设置] 数据已成功设置到UI: 50行, 24列
2025-08-15 21:39:28.874 | INFO     | src.gui.widgets.pagination_widget:batch_update:472 | 📊[pagination-write] batch_update | src=_on_new_data_updated/cached_load | rid=SV-1755265168857-47964e0f-C | total: 0->1396, page: 1->1, size: 50->50, pages: 1->28
2025-08-15 21:39:28.874 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3941 | ✅[pagination-state] now | page=1 / pages=28 / total=1396 | rid=SV-1755265168857-47964e0f-C
2025-08-15 21:39:28.875 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3954 | 🔍 [FINAL修复] 最终调用set_pagination_state: 第1页, 记录1-50 | rid=SV-1755265168857-47964e0f-C
2025-08-15 21:39:28.876 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4166 | 🔍 [表格调试] ================== set_pagination_state 开始 ==================
2025-08-15 21:39:28.877 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4167 | 🔍 [表格调试] 收到分页状态参数: {'current_page': 1, 'page_size': 50, 'total_records': 1396, 'start_record': 1, 'end_record': 50}
2025-08-15 21:39:28.878 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4168 | 🔍 [表格调试] 当前表格行数: 50
2025-08-15 21:39:28.885 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4169 | 🔍 [表格调试] 当前表格列数: 24
2025-08-15 21:39:28.886 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4171 | 🔧 [分页行号修复] 接收分页状态: 第1页, 记录1-50
2025-08-15 21:39:28.887 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1723 | 🆕 [列宽保存] 未找到表格 salary_data_2025_08_active_employees 的列宽配置
2025-08-15 21:39:28.888 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4198 | 🔧 [P0-3修复] 分页时已恢复列宽设置: salary_data_2025_08_active_employees
2025-08-15 21:39:28.888 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4206 | 🔧 [分页行号修复] 表格状态: 当前行数=50, 期望行数=50
2025-08-15 21:39:28.889 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 24 个, 行号起始 1, 共 50 行
2025-08-15 21:39:28.890 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_force_update_pagination_row_numbers:4286 | 🔧 [分页行号修复] 强制更新完成: 起始记录1, 共50行
2025-08-15 21:39:28.891 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3958 | 🔍 [FINAL修复] set_pagination_state最终调用完成
2025-08-15 21:39:28.951 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:379 | 已清理所有缓存
2025-08-15 21:39:28.951 | INFO     | src.gui.prototype.prototype_main_window:_validate_component_state_consistency:10983 | ✅ [新架构] 组件状态一致性验证通过
2025-08-15 21:39:28.954 | INFO     | src.gui.prototype.prototype_main_window:complete_navigation_transition:10946 | 🆕 [新架构] 导航迁移完成
2025-08-15 21:39:28.956 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3972 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-08-15 21:39:28.958 | INFO     | src.services.table_data_service:load_table_data:426 | 📨[data_event] published(cache)
2025-08-15 21:39:28.960 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7591 | 🆕 新架构数据加载请求已发送，等待事件系统处理
2025-08-15 21:39:28.961 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:refresh_navigation_data:1845 | 🔧 [P2-2] 开始增强版导航数据刷新...
2025-08-15 21:39:28.968 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1252 | 🔧 [深度修复] 找到 4 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-15 21:39:28.978 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_change_navigation_tree_data:1956 | 在 table_metadata 中未找到任何 'change_data' 类型的表
2025-08-15 21:39:28.979 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:890 | 导航面板已重构：移除功能性导航，专注数据导航
2025-08-15 21:39:28.983 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_get_latest_change_data_path:2343 | 🔧 [P1修复] 未找到任何异动表元数据
2025-08-15 21:39:28.985 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_check_and_expand_new_change_data:2326 | 🔧 [P1修复] 未发现新的异动表数据
2025-08-15 21:39:28.986 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_navigation_state:1943 | 🔧 [P2-2] 开始恢复导航状态...
2025-08-15 21:39:28.987 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_navigation_state:1974 | 🔧 [P2-2] 导航状态恢复完成: 0个展开项
2025-08-15 21:39:28.988 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_post_refresh_auto_select:1723 | 导航树刷新完成，重新执行自动选择...
2025-08-15 21:39:28.989 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2050 | 开始获取最新工资数据路径...
2025-08-15 21:39:28.993 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1252 | 🔧 [深度修复] 找到 4 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-15 21:39:28.993 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2098 | 找到最新工资数据路径: 工资表 > 2025年 > 08月 > 全部在职人员
2025-08-15 21:39:28.994 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_post_refresh_auto_select:1732 | 重新选择最新数据路径: 工资表 > 2025年 > 08月 > 全部在职人员
2025-08-15 21:39:29.006 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_post_refresh_auto_select:1744 | 🔧 [P1修复] 路径验证通过: 工资表 > 2025年 > 08月 > 全部在职人员 (4层)
2025-08-15 21:39:29.008 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_post_refresh_auto_select:1761 | 重新选择完成: 工资表 > 2025年 > 08月 > 全部在职人员
2025-08-15 21:39:29.011 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:refresh_navigation_data:1896 | 🔧 [P2-2] 增强版导航数据刷新完成
2025-08-15 21:39:29.012 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2095 | MainWorkspaceArea 响应式适配: sm
2025-08-15 21:39:29.017 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2050 | 开始获取最新工资数据路径...
2025-08-15 21:39:29.023 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1252 | 🔧 [深度修复] 找到 4 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-15 21:39:29.023 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:2098 | 找到最新工资数据路径: 工资表 > 2025年 > 08月 > 全部在职人员
2025-08-15 21:39:29.024 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_get_latest_path_with_retry:1553 | 🔧 [P1-2修复] 成功获取到最新路径
2025-08-15 21:39:29.065 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7961 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: salary_data_2025_08_active_employees
2025-08-15 21:39:29.068 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7961 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: salary_data_2025_08_active_employees
2025-08-15 21:39:29.827 | INFO     | src.gui.prototype.prototype_main_window:_force_navigate_after_import:6376 | 🔧 [修复] 强制导航到导入路径: 工资表 > 2025年 > 08月 > 全部在职人员
2025-08-15 21:39:29.827 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:refresh_navigation_data:1845 | 🔧 [P2-2] 开始增强版导航数据刷新...
2025-08-15 21:39:29.833 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 08月 > 全部在职人员', '工资表 > 2025年 > 05月 > 全部在职人员', '工资表 > 2025年', '工资表', '工资表 > 2025年 > 08月']
2025-08-15 21:39:29.834 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7370 | 导航变化: 工资表 > 2025年 > 08月 > 全部在职人员
2025-08-15 21:39:29.835 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10489 | 🚫 [用户要求] 表格切换不显示加载条: salary_data_2025_08_active_employees -> None
2025-08-15 21:39:29.836 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10505 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-08-15 21:39:29.837 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8297 | 🔧 [表名生成] 从完整路径生成表名: ['工资表', '2025年', '08月', '全部在职人员'] -> salary_data_2025_08_active_employees
2025-08-15 21:39:29.838 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-15 21:39:29.844 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:362 | 已清理表 salary_data_2025_08_active_employees 的缓存
2025-08-15 21:39:29.848 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:11175 | 已注册 4 个表格到表头管理器
2025-08-15 21:39:29.848 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1188 | 🔧 [P1-3] 开始智能表头重影检测，共 4 个表格
2025-08-15 21:39:29.850 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1261 | 🔧 [P1-3] 智能表头重影检测完成，耗时 2.05ms
2025-08-15 21:39:29.850 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1262 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-08-15 21:39:29.856 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7586 | 🆕 使用新架构加载数据: salary_data_2025_08_active_employees（通过事件系统）
2025-08-15 21:39:29.857 | INFO     | src.services.table_data_service:load_table_data:366 | 加载表格数据: 页码: 1
2025-08-15 21:39:29.858 | INFO     | src.services.table_data_service:load_table_data:390 | [缓存命中] 使用缓存数据: 第1页
2025-08-15 21:39:29.859 | INFO     | src.services.table_data_service:load_table_data:401 | 📨[data_event] publish(cache) | table=salary_data_2025_08_active_employees | rows=50 | page=1 | size=50 | total=1396 | request_id=SV-1755265169859-1c91e863-C
2025-08-15 21:39:29.860 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3785 | 📥[data_event] received | table=salary_data_2025_08_active_employees | request_id=SV-1755265169859-1c91e863-C
2025-08-15 21:39:29.861 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3817 | 数据内容: 50行 x 28列
2025-08-15 21:39:29.861 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3843 | 🔧 [数据更新] 接收到cached_load操作的数据更新事件: salary_data_2025_08_active_employees, 50行
2025-08-15 21:39:29.868 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7327 | 表 salary_data_2025_08_active_employees 没有用户偏好设置，显示所有可见字段
2025-08-15 21:39:29.874 | INFO     | src.gui.prototype.prototype_main_window:set_data:873 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-08-15 21:39:29.880 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 50行，表名: salary_data_2025_08_active_employees
2025-08-15 21:39:29.883 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=19990089, 薪资=2375.00
2025-08-15 21:39:29.885 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=20161565, 薪资=1696.00
2025-08-15 21:39:29.886 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:5271 | 最大可见行数已更新: 50 -> 50
2025-08-15 21:39:29.887 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5324 | 根据数据量(50)自动调整最大可见行数为: 50
2025-08-15 21:39:29.894 | INFO     | src.modules.format_management.format_renderer:render_dataframe:107 | 🎯 [格式渲染] 隐藏字段配置: ['updated_at', 'id', 'created_at', 'sequence_number', 'row_number']
2025-08-15 21:39:29.902 | INFO     | src.modules.format_management.format_renderer:render_dataframe:115 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-08-15 21:39:29.915 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-08-15 21:39:29.917 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-08-15 21:39:29.918 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 月份 -> 英文名: month -> 类型: month_string
2025-08-15 21:39:29.919 | INFO     | src.modules.format_management.field_registry:get_display_fields:1554 | 🔧 [P0-2修复] display_order转换完成: 25个字段，原始字段数: 25
2025-08-15 21:39:29.920 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | 🔧 [DEBUG] table_type=active_employees, display_fields=25个字段
2025-08-15 21:39:29.921 | INFO     | src.modules.format_management.format_renderer:render_dataframe:211 | 🔧 [P0修复] 配置字段匹配完成: 成功匹配 24/25 个字段
2025-08-15 21:39:29.923 | INFO     | src.modules.format_management.format_renderer:render_dataframe:224 | 🔧 [P0修复] 字段重排完成: 原始24个 -> 最终24个字段
2025-08-15 21:39:29.932 | INFO     | src.modules.format_management.format_renderer:render_dataframe:240 | 🎨 [格式渲染] DataFrame格式化完成: active_employees, 行数: 50, 列数: 24
2025-08-15 21:39:29.933 | INFO     | src.modules.format_management.unified_format_manager:format_data:367 | 🎯 [统一格式管理] 数据格式化完成: active_employees, 行数: 50, 列数: 24
2025-08-15 21:39:29.960 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 50行数据
2025-08-15 21:39:29.961 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:52 | [数据流追踪] 开始小数据集渲染: 50行 x 24列
2025-08-15 21:39:29.977 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:112 | [数据流追踪] 小数据集渲染完成: 耗时16.2ms, 平均每行0.32ms
2025-08-15 21:39:29.977 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=50, 渲染时间=16.2ms, 策略=small_dataset
2025-08-15 21:39:29.978 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:279 | [数据流追踪] 选择小数据集渲染策略
2025-08-15 21:39:29.979 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-08-15 21:39:29.980 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-08-15 21:39:29.981 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-08-15 21:39:29.982 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-08-15 21:39:29.983 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-08-15 21:39:29.993 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 50
2025-08-15 21:39:29.994 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[0]: 工号=19990089, 薪资=2375.00
2025-08-15 21:39:29.994 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[1]: 工号=20161565, 薪资=1696.00
2025-08-15 21:39:29.996 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[2]: 工号=20191782, 薪资=1427.00
2025-08-15 21:39:29.996 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[3]: 工号=20151515, 薪资=2175.00
2025-08-15 21:39:29.997 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[4]: 工号=20181640, 薪资=2582.00
2025-08-15 21:39:29.998 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:479 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-08-15 21:39:29.999 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:442 | 表格数据已设置: 50 行, 24 列
2025-08-15 21:39:30.002 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 50 行, 耗时: 121.0ms
2025-08-15 21:39:30.007 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7926 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-15 21:39:30.008 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7939 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-15 21:39:30.010 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: salary_data_2025_08_active_employees (24/24 列)
2025-08-15 21:39:30.010 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-15 21:39:30.011 | INFO     | src.gui.prototype.prototype_main_window:set_data:997 | 📊[pagination-write] 跳过写入当前页数据量(total=50)，保持 total=0，等待数据事件纠正
2025-08-15 21:39:30.012 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3887 | 🔧 [P1-1修复] 数据已成功设置到UI，使用新映射表头: 50行, 24列
2025-08-15 21:39:30.013 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 24 个, 行号起始 1, 共 50 行
2025-08-15 21:39:30.014 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3903 | 🔧 [P0-新5修复] 表头强制刷新完成: salary_data_2025_08_active_employees, 传递参数: 24个表头
2025-08-15 21:39:30.017 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3907 | 🔧 [统一数据设置] 数据已成功设置到UI: 50行, 24列
2025-08-15 21:39:30.025 | INFO     | src.gui.widgets.pagination_widget:batch_update:472 | 📊[pagination-write] batch_update | src=_on_new_data_updated/cached_load | rid=SV-1755265169859-1c91e863-C | total: 0->1396, page: 1->1, size: 50->50, pages: 1->28
2025-08-15 21:39:30.026 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3941 | ✅[pagination-state] now | page=1 / pages=28 / total=1396 | rid=SV-1755265169859-1c91e863-C
2025-08-15 21:39:30.027 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3954 | 🔍 [FINAL修复] 最终调用set_pagination_state: 第1页, 记录1-50 | rid=SV-1755265169859-1c91e863-C
2025-08-15 21:39:30.028 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4166 | 🔍 [表格调试] ================== set_pagination_state 开始 ==================
2025-08-15 21:39:30.029 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4167 | 🔍 [表格调试] 收到分页状态参数: {'current_page': 1, 'page_size': 50, 'total_records': 1396, 'start_record': 1, 'end_record': 50}
2025-08-15 21:39:30.030 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4168 | 🔍 [表格调试] 当前表格行数: 50
2025-08-15 21:39:30.031 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4169 | 🔍 [表格调试] 当前表格列数: 24
2025-08-15 21:39:30.032 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4171 | 🔧 [分页行号修复] 接收分页状态: 第1页, 记录1-50
2025-08-15 21:39:30.034 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: salary_data_2025_08_active_employees (24/24 列)
2025-08-15 21:39:30.041 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4198 | 🔧 [P0-3修复] 分页时已恢复列宽设置: salary_data_2025_08_active_employees
2025-08-15 21:39:30.042 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4206 | 🔧 [分页行号修复] 表格状态: 当前行数=50, 期望行数=50
2025-08-15 21:39:30.043 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 24 个, 行号起始 1, 共 50 行
2025-08-15 21:39:30.044 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_force_update_pagination_row_numbers:4286 | 🔧 [分页行号修复] 强制更新完成: 起始记录1, 共50行
2025-08-15 21:39:30.047 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3958 | 🔍 [FINAL修复] set_pagination_state最终调用完成
2025-08-15 21:39:30.103 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:379 | 已清理所有缓存
2025-08-15 21:39:30.103 | INFO     | src.gui.prototype.prototype_main_window:_validate_component_state_consistency:10983 | ✅ [新架构] 组件状态一致性验证通过
2025-08-15 21:39:30.107 | INFO     | src.gui.prototype.prototype_main_window:complete_navigation_transition:10946 | 🆕 [新架构] 导航迁移完成
2025-08-15 21:39:30.108 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3972 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-08-15 21:39:30.109 | INFO     | src.services.table_data_service:load_table_data:426 | 📨[data_event] published(cache)
2025-08-15 21:39:30.109 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7591 | 🆕 新架构数据加载请求已发送，等待事件系统处理
2025-08-15 21:39:30.115 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1252 | 🔧 [深度修复] 找到 4 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-15 21:39:30.123 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1252 | 🔧 [深度修复] 找到 4 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-15 21:39:30.129 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_execute_salary_data_load:1817 | 动态加载了 1 个月份的工资数据导航
2025-08-15 21:39:30.134 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_change_navigation_tree_data:1956 | 在 table_metadata 中未找到任何 'change_data' 类型的表
2025-08-15 21:39:30.141 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 08月 > 全部在职人员', '工资表 > 2025年', '工资表', '工资表 > 2025年 > 05月 > 全部在职人员', '工资表 > 2025年 > 08月']
2025-08-15 21:39:30.141 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:890 | 导航面板已重构：移除功能性导航，专注数据导航
2025-08-15 21:39:30.149 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_get_latest_change_data_path:2343 | 🔧 [P1修复] 未找到任何异动表元数据
2025-08-15 21:39:30.150 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_check_and_expand_new_change_data:2326 | 🔧 [P1修复] 未发现新的异动表数据
2025-08-15 21:39:30.151 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_navigation_state:1943 | 🔧 [P2-2] 开始恢复导航状态...
2025-08-15 21:39:30.154 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_navigation_state:1974 | 🔧 [P2-2] 导航状态恢复完成: 4个展开项
2025-08-15 21:39:30.157 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:refresh_navigation_data:1896 | 🔧 [P2-2] 增强版导航数据刷新完成
2025-08-15 21:39:30.158 | INFO     | src.gui.prototype.prototype_main_window:_force_navigate_after_import:6381 | 导航面板数据已刷新
2025-08-15 21:39:30.159 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8297 | 🔧 [表名生成] 从完整路径生成表名: ['工资表', '2025年', '08月', '全部在职人员'] -> salary_data_2025_08_active_employees
2025-08-15 21:39:30.159 | INFO     | src.gui.prototype.prototype_main_window:_force_navigate_after_import:6463 | 🔧 [修复] 更新当前表名: salary_data_2025_08_active_employees
2025-08-15 21:39:30.164 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7961 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: salary_data_2025_08_active_employees
2025-08-15 21:39:30.660 | INFO     | src.gui.prototype.prototype_main_window:_refresh_current_data_display:6593 | [数据流追踪] 开始智能数据显示刷新: salary_data_2025_08_active_employees
2025-08-15 21:39:30.660 | INFO     | src.gui.prototype.prototype_main_window:_refresh_current_data_display:6598 | 🔧 [P1修复] 刷新数据时更新表名: salary_data_2025_08_active_employees
2025-08-15 21:39:30.670 | INFO     | src.core.smart_pagination_strategy:__init__:47 | 智能分页策略管理器初始化完成
2025-08-15 21:39:30.670 | INFO     | src.core.smart_pagination_strategy:should_use_pagination:68 | [数据流追踪] 分页决策输入: 总记录数=1396, 页面大小=50, 用户偏好=None
2025-08-15 21:39:30.670 | INFO     | src.core.smart_pagination_strategy:should_use_pagination:93 | [数据流追踪] 分页决策输出: 策略=pagination, 原因=大数据集(1396条)分28页显示, 预期性能=550ms, 决策耗时=0ms
2025-08-15 21:39:30.678 | INFO     | src.gui.prototype.prototype_main_window:_refresh_current_data_display:6620 | [数据流追踪] 智能分页策略决策: pagination, 原因=大数据集(1396条)分28页显示, 预期性能=550ms
2025-08-15 21:39:30.679 | INFO     | src.gui.prototype.prototype_main_window:_execute_pagination_mode:6694 | [数据流追踪] 执行分页显示模式: salary_data_2025_08_active_employees, 1396条记录
2025-08-15 21:39:30.679 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:7722 | 使用分页模式加载 salary_data_2025_08_active_employees，第1页，每页50条
2025-08-15 21:39:30.681 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:7874 | 缓存未命中，从数据库加载: salary_data_2025_08_active_employees 第1页
2025-08-15 21:39:30.684 | INFO     | src.gui.prototype.prototype_main_window:_execute_pagination_mode:6712 | [数据流追踪] 分页显示模式: 1396条记录分28页显示
2025-08-15 21:39:30.684 | INFO     | src.gui.prototype.prototype_main_window:run:137 | 开始加载表 salary_data_2025_08_active_employees 第1页数据，每页50条
2025-08-15 21:39:30.692 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:805 | 正在从表 salary_data_2025_08_active_employees 分页获取数据（支持排序）: 第1页, 每页50条, 排序=0列
2025-08-15 21:39:30.696 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:921 | 成功从表 salary_data_2025_08_active_employees 获取第1页数据（含排序）: 50 行，总计1396行
2025-08-15 21:39:30.697 | INFO     | src.gui.prototype.prototype_main_window:run:182 | 🔧 [P2修复] 使用排序查询: 0 个排序列, 总记录数: 1396
2025-08-15 21:39:30.698 | INFO     | src.gui.prototype.prototype_main_window:run:234 | 原始数据: 50行, 28列
2025-08-15 21:39:30.699 | INFO     | src.gui.prototype.prototype_main_window:run:241 | 开始应用字段映射
2025-08-15 21:39:30.705 | INFO     | src.gui.prototype.prototype_main_window:_apply_unified_field_processing:7119 | 开始统一字段处理: salary_data_2025_08_active_employees, 原始列数: 28
2025-08-15 21:39:30.707 | INFO     | src.modules.format_management.field_registry:__init__:95 | 🏷️ [字段注册] 字段注册系统初始化: state/data/field_mappings.json
2025-08-15 21:39:30.708 | INFO     | src.modules.format_management.format_renderer:__init__:83 | 🎨 [格式渲染] 格式渲染器初始化完成
2025-08-15 21:39:30.709 | INFO     | src.modules.format_management.unified_format_manager:_setup_event_listeners:881 | 🎯 [事件驱动] 事件监听器设置完成
2025-08-15 21:39:30.712 | INFO     | src.core.unified_state_manager:update_global_state:350 | 全局状态已更新: StateChangeType.DATA_RELOADED
2025-08-15 21:39:30.725 | INFO     | src.modules.format_management.unified_format_manager:_sync_to_state_manager:896 | 🎯 [统一状态管理] 状态同步完成
2025-08-15 21:39:30.729 | INFO     | src.modules.format_management.format_config:load_config:403 | 🔧 [格式配置] 配置文件加载成功
2025-08-15 21:39:30.732 | INFO     | src.modules.format_management.field_registry:load_mappings:629 | 🏷️ [字段注册] 字段映射加载成功
2025-08-15 21:39:30.733 | INFO     | src.modules.format_management.unified_format_manager:_initialize_system:146 | 🎯 [统一格式管理] 系统初始化完成
2025-08-15 21:39:30.734 | INFO     | src.modules.format_management.unified_format_manager:__init__:130 | 🎯 [统一格式管理] 统一格式管理器初始化完成（新架构版本）
2025-08-15 21:39:30.735 | INFO     | src.core.architecture_factory:get_unified_format_manager:293 | 🎯 [统一格式管理] 统一格式管理器创建成功
2025-08-15 21:39:30.736 | INFO     | src.modules.format_management.format_renderer:render_dataframe:107 | 🎯 [格式渲染] 隐藏字段配置: ['updated_at', 'id', 'created_at', 'sequence_number', 'row_number']
2025-08-15 21:39:30.737 | INFO     | src.modules.format_management.format_renderer:render_dataframe:113 | 🎯 [格式渲染] 已隐藏字段: ['updated_at', 'id', 'created_at', 'sequence_number']
2025-08-15 21:39:30.763 | INFO     | src.modules.format_management.field_registry:get_display_fields:1554 | 🔧 [P0-2修复] display_order转换完成: 24个字段，原始字段数: 24
2025-08-15 21:39:30.764 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | 🔧 [DEBUG] table_type=salary_data_2025_08_active_employees, display_fields=24个字段
2025-08-15 21:39:30.769 | INFO     | src.modules.format_management.format_renderer:_find_mapped_field_name:1185 | 🔧 [P2修复] 直接映射成功: '工号' -> 'employee_id'
2025-08-15 21:39:30.770 | INFO     | src.modules.format_management.format_renderer:_find_mapped_field_name:1185 | 🔧 [P2修复] 直接映射成功: '姓名' -> 'employee_name'
2025-08-15 21:39:30.771 | INFO     | src.modules.format_management.format_renderer:_find_mapped_field_name:1185 | 🔧 [P2修复] 直接映射成功: '部门名称' -> 'department'
2025-08-15 21:39:30.771 | INFO     | src.modules.format_management.format_renderer:_find_mapped_field_name:1185 | 🔧 [P2修复] 直接映射成功: '人员类别' -> 'employee_type'
2025-08-15 21:39:30.773 | INFO     | src.modules.format_management.format_renderer:_find_mapped_field_name:1185 | 🔧 [P2修复] 直接映射成功: '人员类别代码' -> 'employee_type_code'
2025-08-15 21:39:30.775 | INFO     | src.modules.format_management.format_renderer:_find_mapped_field_name:1185 | 🔧 [P2修复] 直接映射成功: '2025年岗位工资' -> 'position_salary_2025'
2025-08-15 21:39:30.779 | INFO     | src.modules.format_management.format_renderer:_find_mapped_field_name:1185 | 🔧 [P2修复] 直接映射成功: '2025年薪级工资' -> 'grade_salary_2025'
2025-08-15 21:39:30.785 | INFO     | src.modules.format_management.format_renderer:_find_mapped_field_name:1185 | 🔧 [P2修复] 直接映射成功: '津贴' -> 'allowance'
2025-08-15 21:39:30.786 | INFO     | src.modules.format_management.format_renderer:_find_mapped_field_name:1185 | 🔧 [P2修复] 直接映射成功: '结余津贴' -> 'balance_allowance'
2025-08-15 21:39:30.788 | INFO     | src.modules.format_management.format_renderer:_find_mapped_field_name:1185 | 🔧 [P2修复] 直接映射成功: '应发工资' -> 'total_salary'
2025-08-15 21:39:30.789 | INFO     | src.modules.format_management.format_renderer:_find_mapped_field_name:1185 | 🔧 [P2修复] 直接映射成功: '补发' -> 'supplement'
2025-08-15 21:39:30.790 | INFO     | src.modules.format_management.format_renderer:_find_mapped_field_name:1185 | 🔧 [P2修复] 直接映射成功: '借支' -> 'advance'
2025-08-15 21:39:30.791 | INFO     | src.modules.format_management.format_renderer:_find_mapped_field_name:1185 | 🔧 [P2修复] 直接映射成功: '月份' -> 'month'
2025-08-15 21:39:30.792 | INFO     | src.modules.format_management.format_renderer:render_dataframe:211 | 🔧 [P0修复] 配置字段匹配完成: 成功匹配 13/24 个字段
2025-08-15 21:39:30.793 | INFO     | src.modules.format_management.format_renderer:render_dataframe:219 | 🔧 [P0-关键修复] 保留未配置的业务字段: 11个
2025-08-15 21:39:30.801 | INFO     | src.modules.format_management.format_renderer:render_dataframe:224 | 🔧 [P0修复] 字段重排完成: 原始24个 -> 最终24个字段
2025-08-15 21:39:30.801 | INFO     | src.modules.format_management.format_renderer:render_dataframe:240 | 🎨 [格式渲染] DataFrame格式化完成: salary_data_2025_08_active_employees, 行数: 50, 列数: 24
2025-08-15 21:39:30.802 | INFO     | src.modules.format_management.unified_format_manager:format_data:367 | 🎯 [统一格式管理] 数据格式化完成: salary_data_2025_08_active_employees, 行数: 50, 列数: 24
2025-08-15 21:39:30.806 | INFO     | src.gui.prototype.prototype_main_window:_apply_unified_field_processing:7127 | 🔧 [调试] 格式化时删除的列: {'id', 'updated_at', 'created_at', 'sequence_number'}
2025-08-15 21:39:30.815 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7327 | 表 salary_data_2025_08_active_employees 没有用户偏好设置，显示所有可见字段
2025-08-15 21:39:30.817 | INFO     | src.gui.prototype.prototype_main_window:_apply_unified_field_processing:7180 | 🔧 [字段处理] 统一字段处理完成并缓存: 24个字段
2025-08-15 21:39:30.818 | INFO     | src.gui.prototype.prototype_main_window:run:251 | PaginationWorker - 字段映射成功: 28 -> 24列
2025-08-15 21:39:30.818 | INFO     | src.gui.prototype.prototype_main_window:run:265 | 字段映射成功: 24列
2025-08-15 21:39:30.821 | INFO     | src.gui.prototype.prototype_main_window:run:291 | 最终数据: 50行, 24列, 总记录数: 1396
2025-08-15 21:39:30.826 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:7905 | 分页数据加载成功（数据库）: 50条数据，第1页，总计1396条
2025-08-15 21:39:30.827 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:7939 | 🚀 [性能缓存] 数据已缓存: salary_data_2025_08_active_employees 第1页
2025-08-15 21:39:30.841 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:747 | 正在从表 salary_data_2025_08_active_employees 分页获取数据: 第2页, 每页50条
2025-08-15 21:39:30.843 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:7968 | 🧹 [异步分页] 使用重构后的统一格式化结果: 50行, 24列
2025-08-15 21:39:30.845 | INFO     | src.gui.prototype.prototype_main_window:set_data:873 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-08-15 21:39:30.853 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 50行，表名: salary_data_2025_08_active_employees
2025-08-15 21:39:30.855 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:780 | 成功从表 salary_data_2025_08_active_employees 获取第2页数据: 50 行，总计1396行
2025-08-15 21:39:30.859 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=19990089, 薪资=2375.00
2025-08-15 21:39:30.870 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=20161565, 薪资=1696.00
2025-08-15 21:39:30.871 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:5271 | 最大可见行数已更新: 50 -> 50
2025-08-15 21:39:30.872 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5324 | 根据数据量(50)自动调整最大可见行数为: 50
2025-08-15 21:39:30.878 | INFO     | src.modules.format_management.format_renderer:render_dataframe:107 | 🎯 [格式渲染] 隐藏字段配置: ['updated_at', 'id', 'created_at', 'sequence_number', 'row_number']
2025-08-15 21:39:30.882 | INFO     | src.modules.format_management.format_renderer:render_dataframe:115 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-08-15 21:39:30.889 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-08-15 21:39:30.890 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-08-15 21:39:30.891 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 月份 -> 英文名: month -> 类型: month_string
2025-08-15 21:39:30.905 | INFO     | src.modules.format_management.field_registry:get_display_fields:1554 | 🔧 [P0-2修复] display_order转换完成: 25个字段，原始字段数: 25
2025-08-15 21:39:30.906 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | 🔧 [DEBUG] table_type=active_employees, display_fields=25个字段
2025-08-15 21:39:30.908 | INFO     | src.modules.format_management.format_renderer:render_dataframe:211 | 🔧 [P0修复] 配置字段匹配完成: 成功匹配 24/25 个字段
2025-08-15 21:39:30.914 | INFO     | src.modules.format_management.format_renderer:render_dataframe:224 | 🔧 [P0修复] 字段重排完成: 原始24个 -> 最终24个字段
2025-08-15 21:39:30.915 | INFO     | src.modules.format_management.format_renderer:render_dataframe:240 | 🎨 [格式渲染] DataFrame格式化完成: active_employees, 行数: 50, 列数: 24
2025-08-15 21:39:30.917 | INFO     | src.modules.format_management.unified_format_manager:format_data:367 | 🎯 [统一格式管理] 数据格式化完成: active_employees, 行数: 50, 列数: 24
2025-08-15 21:39:30.944 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 50行数据
2025-08-15 21:39:30.945 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:52 | [数据流追踪] 开始小数据集渲染: 50行 x 24列
2025-08-15 21:39:30.958 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:112 | [数据流追踪] 小数据集渲染完成: 耗时12.8ms, 平均每行0.26ms
2025-08-15 21:39:30.961 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=50, 渲染时间=12.8ms, 策略=small_dataset
2025-08-15 21:39:30.962 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:279 | [数据流追踪] 选择小数据集渲染策略
2025-08-15 21:39:30.963 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19990089
2025-08-15 21:39:30.964 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20161565
2025-08-15 21:39:30.965 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20191782
2025-08-15 21:39:30.966 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20151515
2025-08-15 21:39:30.967 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20181640
2025-08-15 21:39:30.969 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 50
2025-08-15 21:39:30.975 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[0]: 工号=19990089, 薪资=2375.00
2025-08-15 21:39:30.975 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[1]: 工号=20161565, 薪资=1696.00
2025-08-15 21:39:30.976 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[2]: 工号=20191782, 薪资=1427.00
2025-08-15 21:39:30.977 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[3]: 工号=20151515, 薪资=2175.00
2025-08-15 21:39:30.977 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[4]: 工号=20181640, 薪资=2582.00
2025-08-15 21:39:30.978 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:479 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-08-15 21:39:30.980 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 50 行, 耗时: 123.9ms
2025-08-15 21:39:30.981 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7926 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-15 21:39:30.982 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7939 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-15 21:39:30.994 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: salary_data_2025_08_active_employees (24/24 列)
2025-08-15 21:39:30.994 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-15 21:39:30.995 | INFO     | src.gui.prototype.prototype_main_window:set_data:997 | 📊[pagination-write] 跳过写入当前页数据量(total=50)，保持 total=1396，等待数据事件纠正
2025-08-15 21:39:30.996 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:7987 | 🔍 [调试-分页] 即将调用set_pagination_state: 第1页, 记录1-50
2025-08-15 21:39:30.997 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4166 | 🔍 [表格调试] ================== set_pagination_state 开始 ==================
2025-08-15 21:39:30.998 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4167 | 🔍 [表格调试] 收到分页状态参数: {'current_page': 1, 'page_size': 50, 'total_records': 1396, 'start_record': 1, 'end_record': 50}
2025-08-15 21:39:30.999 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4168 | 🔍 [表格调试] 当前表格行数: 50
2025-08-15 21:39:31.000 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4169 | 🔍 [表格调试] 当前表格列数: 24
2025-08-15 21:39:31.001 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4171 | 🔧 [分页行号修复] 接收分页状态: 第1页, 记录1-50
2025-08-15 21:39:31.009 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: salary_data_2025_08_active_employees (24/24 列)
2025-08-15 21:39:31.009 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4198 | 🔧 [P0-3修复] 分页时已恢复列宽设置: salary_data_2025_08_active_employees
2025-08-15 21:39:31.010 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4206 | 🔧 [分页行号修复] 表格状态: 当前行数=50, 期望行数=50
2025-08-15 21:39:31.011 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 24 个, 行号起始 1, 共 50 行
2025-08-15 21:39:31.012 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_force_update_pagination_row_numbers:4286 | 🔧 [分页行号修复] 强制更新完成: 起始记录1, 共50行
2025-08-15 21:39:31.013 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:7989 | 🔍 [调试-分页] set_pagination_state调用完成
2025-08-15 21:39:31.014 | INFO     | src.gui.widgets.pagination_widget:set_total_records:442 | 📊[pagination-write] set_total_records | old_total=1396 -> new_total=1396 | pages_old=28 -> pages_new=28
2025-08-15 21:39:31.024 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:8011 | 🔧 [P0-新1修复] 分页状态验证: 当前第1页，共28页，总记录1396条
2025-08-15 21:39:31.031 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:8062 | 🆕 [新架构] 分页数据加载完成，已完成渐进式状态迁移
2025-08-15 21:39:31.094 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7961 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: salary_data_2025_08_active_employees
2025-08-15 21:39:35.280 | INFO     | src.gui.prototype.widgets.column_sort_manager:handle_header_click:138 | 🆕 [多列排序] 新增列6(2025年薪级工资)排序: 升序
2025-08-15 21:39:35.280 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_state_changed:7425 | 🆕 [新架构多列排序] 排序状态变化: 1 列
2025-08-15 21:39:35.296 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_state_changed:7433 | 🆕 [新架构多列排序] 当前排序: 2025年薪级工资: 升序
2025-08-15 21:39:35.296 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_request:7448 | 🆕 [新架构多列排序] 排序请求: salary_data_2025_08_active_employees.grade_salary_2025 -> ascending
2025-08-15 21:39:35.296 | INFO     | src.gui.prototype.prototype_main_window:_handle_sort_applied_impl:4527 | 🔧 [P0-CRITICAL] 简化排序处理: 列6, grade_salary_2025, ascending
2025-08-15 21:39:35.296 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:4202 | 🔧 [排序调试] 准备发布排序请求: salary_data_2025_08_active_employees, [{'column_name': 'grade_salary_2025', 'order': 'ascending', 'priority': 0}]
2025-08-15 21:39:35.296 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:4239 | [数据流追踪] 开始处理排序请求: 表=salary_data_2025_08_active_employees, 排序列数=1, 页码=1
2025-08-15 21:39:35.296 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:4285 | 🔧 [排序] 使用实际页码: 1 (传入页码: 1)
2025-08-15 21:39:35.296 | INFO     | src.services.table_data_service:_handle_sort_request:154 | [排序调试] 接收到排序请求
2025-08-15 21:39:35.296 | INFO     | src.core.unified_state_manager:update_table_state:233 | 表状态已更新: salary_data_2025_08_active_employees, 变更类型: StateChangeType.SORT_CHANGED
2025-08-15 21:39:35.296 | INFO     | src.core.unified_data_request_manager:request_table_data:212 | 开始处理数据请求
2025-08-15 21:39:35.296 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:994 | [FIX] [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_08_active_employees' 中（类型: REAL）
2025-08-15 21:39:35.296 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:994 | [FIX] [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_08_active_employees' 中（类型: REAL）
2025-08-15 21:39:35.296 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:805 | 正在从表 salary_data_2025_08_active_employees 分页获取数据（支持排序）: 第1页, 每页50条, 排序=1列
2025-08-15 21:39:35.296 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:994 | [FIX] [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_08_active_employees' 中（类型: REAL）
2025-08-15 21:39:35.311 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:921 | 成功从表 salary_data_2025_08_active_employees 获取第1页数据（含排序）: 50 行，总计1396行
2025-08-15 21:39:35.311 | INFO     | src.core.unified_data_request_manager:request_table_data:262 | 数据请求处理完成: 字段=28, 行数=50, 耗时=15.2ms
2025-08-15 21:39:35.311 | INFO     | src.services.table_data_service:_handle_sort_request:205 | [修复排序] 排序操作成功，发布数据更新事件: salary_data_2025_08_active_employees, 50行
2025-08-15 21:39:35.311 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3785 | 📥[data_event] received | table=salary_data_2025_08_active_employees | request_id=None
2025-08-15 21:39:35.311 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3817 | 数据内容: 50行 x 28列
2025-08-15 21:39:35.311 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3840 | 🔧 [关键修复] 接收到sort_change操作的数据更新事件，开始UI更新: salary_data_2025_08_active_employees, 50行
2025-08-15 21:39:35.311 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7327 | 表 salary_data_2025_08_active_employees 没有用户偏好设置，显示所有可见字段
2025-08-15 21:39:35.327 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3869 | 🚀 [排序优化] 检测到排序操作，标记为高优先级同步更新
2025-08-15 21:39:35.327 | INFO     | src.gui.prototype.prototype_main_window:set_data:873 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-08-15 21:39:35.327 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 50行，表名: salary_data_2025_08_active_employees
2025-08-15 21:39:35.343 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=19961347.0, 薪资=N/A
2025-08-15 21:39:35.343 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=20251003.0, 薪资=N/A
2025-08-15 21:39:35.343 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:5271 | 最大可见行数已更新: 50 -> 50
2025-08-15 21:39:35.343 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5324 | 根据数据量(50)自动调整最大可见行数为: 50
2025-08-15 21:39:35.343 | INFO     | src.modules.format_management.format_renderer:render_dataframe:107 | 🎯 [格式渲染] 隐藏字段配置: ['updated_at', 'id', 'created_at', 'sequence_number', 'row_number']
2025-08-15 21:39:35.343 | INFO     | src.modules.format_management.format_renderer:render_dataframe:115 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-08-15 21:39:35.361 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-08-15 21:39:35.376 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-08-15 21:39:35.376 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 月份 -> 英文名: month -> 类型: month_string
2025-08-15 21:39:35.376 | INFO     | src.modules.format_management.field_registry:get_display_fields:1554 | 🔧 [P0-2修复] display_order转换完成: 25个字段，原始字段数: 25
2025-08-15 21:39:35.376 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | 🔧 [DEBUG] table_type=active_employees, display_fields=25个字段
2025-08-15 21:39:35.376 | INFO     | src.modules.format_management.format_renderer:render_dataframe:211 | 🔧 [P0修复] 配置字段匹配完成: 成功匹配 24/25 个字段
2025-08-15 21:39:35.376 | INFO     | src.modules.format_management.format_renderer:render_dataframe:224 | 🔧 [P0修复] 字段重排完成: 原始24个 -> 最终24个字段
2025-08-15 21:39:35.376 | INFO     | src.modules.format_management.format_renderer:render_dataframe:240 | 🎨 [格式渲染] DataFrame格式化完成: active_employees, 行数: 50, 列数: 24
2025-08-15 21:39:35.376 | INFO     | src.modules.format_management.unified_format_manager:format_data:367 | 🎯 [统一格式管理] 数据格式化完成: active_employees, 行数: 50, 列数: 24
2025-08-15 21:39:35.407 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 50行数据
2025-08-15 21:39:35.407 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:52 | [数据流追踪] 开始小数据集渲染: 50行 x 24列
2025-08-15 21:39:35.420 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:112 | [数据流追踪] 小数据集渲染完成: 耗时13.8ms, 平均每行0.28ms
2025-08-15 21:39:35.420 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=50, 渲染时间=13.8ms, 策略=small_dataset
2025-08-15 21:39:35.420 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:279 | [数据流追踪] 选择小数据集渲染策略
2025-08-15 21:39:35.420 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19961347
2025-08-15 21:39:35.420 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20251003
2025-08-15 21:39:35.420 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20251006
2025-08-15 21:39:35.420 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20251007
2025-08-15 21:39:35.420 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20251008
2025-08-15 21:39:35.420 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 50
2025-08-15 21:39:35.420 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[0]: 工号=19961347, 薪资=0.00
2025-08-15 21:39:35.420 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[1]: 工号=20251003, 薪资=0.00
2025-08-15 21:39:35.420 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[2]: 工号=20251006, 薪资=0.00
2025-08-15 21:39:35.420 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[3]: 工号=20251007, 薪资=0.00
2025-08-15 21:39:35.420 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[4]: 工号=20251008, 薪资=0.00
2025-08-15 21:39:35.436 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:479 | 可见行数据顺序: ['19961347', '20251003', '20251006', '20251007', '20251008']
2025-08-15 21:39:35.436 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:442 | 表格数据已设置: 50 行, 24 列
2025-08-15 21:39:35.448 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 50 行, 耗时: 121.6ms
2025-08-15 21:39:35.450 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7926 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-15 21:39:35.452 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7936 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-08-15 21:39:35.452 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7937 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 升序
2025-08-15 21:39:35.454 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: salary_data_2025_08_active_employees (24/24 列)
2025-08-15 21:39:35.459 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-15 21:39:35.461 | INFO     | src.gui.prototype.prototype_main_window:set_data:997 | 📊[pagination-write] 跳过写入当前页数据量(total=50)，保持 total=1396，等待数据事件纠正
2025-08-15 21:39:35.462 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3887 | 🔧 [P1-1修复] 数据已成功设置到UI，使用新映射表头: 50行, 24列
2025-08-15 21:39:35.462 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 24 个, 行号起始 1, 共 50 行
2025-08-15 21:39:35.463 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3903 | 🔧 [P0-新5修复] 表头强制刷新完成: salary_data_2025_08_active_employees, 传递参数: 24个表头
2025-08-15 21:39:35.464 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3907 | 🔧 [统一数据设置] 数据已成功设置到UI: 50行, 24列
2025-08-15 21:39:35.465 | INFO     | src.gui.widgets.pagination_widget:batch_update:472 | 📊[pagination-write] batch_update | src=_on_new_data_updated/sort_change | rid=None | total: 1396->1396, page: 1->1, size: 50->50, pages: 28->28
2025-08-15 21:39:35.465 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3941 | ✅[pagination-state] now | page=1 / pages=28 / total=1396 | rid=None
2025-08-15 21:39:35.466 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3954 | 🔍 [FINAL修复] 最终调用set_pagination_state: 第1页, 记录1-50 | rid=None
2025-08-15 21:39:35.475 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4166 | 🔍 [表格调试] ================== set_pagination_state 开始 ==================
2025-08-15 21:39:35.475 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4167 | 🔍 [表格调试] 收到分页状态参数: {'current_page': 1, 'page_size': 50, 'total_records': 1396, 'start_record': 1, 'end_record': 50}
2025-08-15 21:39:35.476 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4168 | 🔍 [表格调试] 当前表格行数: 50
2025-08-15 21:39:35.477 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4169 | 🔍 [表格调试] 当前表格列数: 24
2025-08-15 21:39:35.482 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4171 | 🔧 [分页行号修复] 接收分页状态: 第1页, 记录1-50
2025-08-15 21:39:35.484 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: salary_data_2025_08_active_employees (24/24 列)
2025-08-15 21:39:35.491 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4198 | 🔧 [P0-3修复] 分页时已恢复列宽设置: salary_data_2025_08_active_employees
2025-08-15 21:39:35.491 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4206 | 🔧 [分页行号修复] 表格状态: 当前行数=50, 期望行数=50
2025-08-15 21:39:35.492 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 24 个, 行号起始 1, 共 50 行
2025-08-15 21:39:35.493 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_force_update_pagination_row_numbers:4286 | 🔧 [分页行号修复] 强制更新完成: 起始记录1, 共50行
2025-08-15 21:39:35.494 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3958 | 🔍 [FINAL修复] set_pagination_state最终调用完成
2025-08-15 21:39:35.495 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3972 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-08-15 21:39:35.495 | INFO     | src.services.table_data_service:_handle_sort_request:225 | [修复排序] 数据更新事件已发布
2025-08-15 21:39:35.496 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:4324 | [数据流追踪] 排序事件已发布: 表=salary_data_2025_08_active_employees, 排序列数=1, 页码=1
2025-08-15 21:39:35.499 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_header_clicked_impl:8175 | 🆕 [新架构多列排序] 成功处理列6(2025年薪级工资)点击
2025-08-15 21:39:35.560 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7961 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: salary_data_2025_08_active_employees
2025-08-15 21:39:47.077 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4768 | 📍[page-change] 入口 | table=salary_data_2025_08_active_employees | target_page=2 | request_id=PG-1755265187077-6e372625
2025-08-15 21:39:47.077 | INFO     | src.core.pagination_event_optimizer:__init__:69 | 🔧 [立即修复] 分页事件优化器初始化完成
2025-08-15 21:39:47.077 | INFO     | src.gui.prototype.prototype_main_window:clear_field_processing_cache:7240 | 🔧 [缓存清理] 清理了表 salary_data_2025_08_active_employees 的 1 个缓存条目
2025-08-15 21:39:47.077 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:362 | 已清理表 salary_data_2025_08_active_employees 的缓存
2025-08-15 21:39:47.077 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4796 | 🔧 [P0-3修复] 已清理表 salary_data_2025_08_active_employees 的相关缓存
2025-08-15 21:39:47.077 | INFO     | src.core.pagination_state_manager:can_start_pagination:113 | ✅ [允许] 分页请求可以开始: salary_data_2025_08_active_employees, 页2
2025-08-15 21:39:47.094 | INFO     | src.gui.prototype.prototype_main_window:_save_current_table_ui_state:10568 | 🔧 [P0-2修复] 通过StateManager保存状态成功: salary_data_2025_08_active_employees
2025-08-15 21:39:47.095 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4853 | 🔧 [分页请求] 第2页, 表: salary_data_2025_08_active_employees, 上下文已设置
2025-08-15 21:39:47.105 | INFO     | src.core.request_deduplication_manager:__init__:65 | 请求去重管理器初始化完成，默认TTL: 0.3秒
2025-08-15 21:39:47.105 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4870 | 🔧 [立即修复] 分页请求通过强化去重检查: salary_data_2025_08_active_employees, 第2页
2025-08-15 21:39:47.106 | INFO     | src.services.table_data_service:load_table_data:366 | 加载表格数据: 页码: 2
2025-08-15 21:39:47.107 | INFO     | src.core.unified_data_request_manager:request_table_data:212 | 开始处理数据请求
2025-08-15 21:39:47.108 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:994 | [FIX] [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_08_active_employees' 中（类型: REAL）
2025-08-15 21:39:47.111 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:994 | [FIX] [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_08_active_employees' 中（类型: REAL）
2025-08-15 21:39:47.117 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:805 | 正在从表 salary_data_2025_08_active_employees 分页获取数据（支持排序）: 第2页, 每页50条, 排序=1列
2025-08-15 21:39:47.119 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:994 | [FIX] [排序修复] 列 'grade_salary_2025' 存在于表 'salary_data_2025_08_active_employees' 中（类型: REAL）
2025-08-15 21:39:47.125 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:921 | 成功从表 salary_data_2025_08_active_employees 获取第2页数据（含排序）: 50 行，总计1396行
2025-08-15 21:39:47.130 | INFO     | src.core.unified_data_request_manager:request_table_data:262 | 数据请求处理完成: 字段=28, 行数=50, 耗时=22.4ms
2025-08-15 21:39:47.130 | INFO     | src.services.table_data_service:load_table_data:462 | [新架构] 使用统一格式管理器（单例优化）
2025-08-15 21:39:47.132 | INFO     | src.modules.format_management.format_renderer:render_dataframe:107 | 🎯 [格式渲染] 隐藏字段配置: ['updated_at', 'id', 'created_at', 'sequence_number', 'row_number']
2025-08-15 21:39:47.132 | INFO     | src.modules.format_management.format_renderer:render_dataframe:115 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-08-15 21:39:47.145 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-08-15 21:39:47.146 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-08-15 21:39:47.151 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 月份 -> 英文名: month -> 类型: month_string
2025-08-15 21:39:47.155 | INFO     | src.modules.format_management.field_registry:get_display_fields:1554 | 🔧 [P0-2修复] display_order转换完成: 25个字段，原始字段数: 25
2025-08-15 21:39:47.157 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | 🔧 [DEBUG] table_type=active_employees, display_fields=25个字段
2025-08-15 21:39:47.158 | INFO     | src.modules.format_management.format_renderer:render_dataframe:211 | 🔧 [P0修复] 配置字段匹配完成: 成功匹配 25/25 个字段
2025-08-15 21:39:47.159 | INFO     | src.modules.format_management.format_renderer:render_dataframe:219 | 🔧 [P0-关键修复] 保留未配置的业务字段: 3个
2025-08-15 21:39:47.162 | INFO     | src.modules.format_management.format_renderer:render_dataframe:224 | 🔧 [P0修复] 字段重排完成: 原始28个 -> 最终28个字段
2025-08-15 21:39:47.162 | INFO     | src.modules.format_management.format_renderer:render_dataframe:240 | 🎨 [格式渲染] DataFrame格式化完成: active_employees, 行数: 50, 列数: 28
2025-08-15 21:39:47.163 | INFO     | src.modules.format_management.unified_format_manager:format_data:367 | 🎯 [统一格式管理] 数据格式化完成: active_employees, 行数: 50, 列数: 28
2025-08-15 21:39:47.165 | INFO     | src.core.unified_state_manager:update_table_state:233 | 表状态已更新: salary_data_2025_08_active_employees, 变更类型: None
2025-08-15 21:39:47.175 | INFO     | src.services.table_data_service:load_table_data:512 | [修复数据发布] 数据加载成功，发布更新事件: 50行
2025-08-15 21:39:47.175 | INFO     | src.services.table_data_service:load_table_data:516 | 📨[data_event] publish | table=salary_data_2025_08_active_employees | rows=50 | page=2 | size=50 | total=1396 | req_type=RequestType.INITIAL_LOAD | request_id=PG-1755265187077-6e372625
2025-08-15 21:39:47.177 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3785 | 📥[data_event] received | table=salary_data_2025_08_active_employees | request_id=PG-1755265187077-6e372625
2025-08-15 21:39:47.179 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3817 | 数据内容: 50行 x 28列
2025-08-15 21:39:47.179 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3843 | 🔧 [数据更新] 接收到initial_load操作的数据更新事件: salary_data_2025_08_active_employees, 50行
2025-08-15 21:39:47.185 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7327 | 表 salary_data_2025_08_active_employees 没有用户偏好设置，显示所有可见字段
2025-08-15 21:39:47.191 | INFO     | src.gui.prototype.prototype_main_window:set_data:873 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-08-15 21:39:47.197 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 50行，表名: salary_data_2025_08_active_employees
2025-08-15 21:39:47.207 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=20211012, 薪资=1005.00
2025-08-15 21:39:47.207 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=20221065, 薪资=1005.00
2025-08-15 21:39:47.208 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:5271 | 最大可见行数已更新: 50 -> 50
2025-08-15 21:39:47.209 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5324 | 根据数据量(50)自动调整最大可见行数为: 50
2025-08-15 21:39:47.218 | INFO     | src.modules.format_management.format_renderer:render_dataframe:107 | 🎯 [格式渲染] 隐藏字段配置: ['updated_at', 'id', 'created_at', 'sequence_number', 'row_number']
2025-08-15 21:39:47.219 | INFO     | src.modules.format_management.format_renderer:render_dataframe:115 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-08-15 21:39:47.234 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-08-15 21:39:47.236 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-08-15 21:39:47.238 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 月份 -> 英文名: month -> 类型: month_string
2025-08-15 21:39:47.243 | INFO     | src.modules.format_management.field_registry:get_display_fields:1554 | 🔧 [P0-2修复] display_order转换完成: 25个字段，原始字段数: 25
2025-08-15 21:39:47.244 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | 🔧 [DEBUG] table_type=active_employees, display_fields=25个字段
2025-08-15 21:39:47.245 | INFO     | src.modules.format_management.format_renderer:render_dataframe:211 | 🔧 [P0修复] 配置字段匹配完成: 成功匹配 24/25 个字段
2025-08-15 21:39:47.248 | INFO     | src.modules.format_management.format_renderer:render_dataframe:224 | 🔧 [P0修复] 字段重排完成: 原始24个 -> 最终24个字段
2025-08-15 21:39:47.249 | INFO     | src.modules.format_management.format_renderer:render_dataframe:240 | 🎨 [格式渲染] DataFrame格式化完成: active_employees, 行数: 50, 列数: 24
2025-08-15 21:39:47.250 | INFO     | src.modules.format_management.unified_format_manager:format_data:367 | 🎯 [统一格式管理] 数据格式化完成: active_employees, 行数: 50, 列数: 24
2025-08-15 21:39:47.273 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 50行数据
2025-08-15 21:39:47.273 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:52 | [数据流追踪] 开始小数据集渲染: 50行 x 24列
2025-08-15 21:39:47.286 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:112 | [数据流追踪] 小数据集渲染完成: 耗时12.8ms, 平均每行0.26ms
2025-08-15 21:39:47.289 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=50, 渲染时间=12.8ms, 策略=small_dataset
2025-08-15 21:39:47.290 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:279 | [数据流追踪] 选择小数据集渲染策略
2025-08-15 21:39:47.292 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 20211012
2025-08-15 21:39:47.295 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 20221065
2025-08-15 21:39:47.295 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 20231054
2025-08-15 21:39:47.296 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 20241001
2025-08-15 21:39:47.304 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 20231033
2025-08-15 21:39:47.305 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 50
2025-08-15 21:39:47.306 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[0]: 工号=20211012, 薪资=1005.00
2025-08-15 21:39:47.306 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[1]: 工号=20221065, 薪资=1005.00
2025-08-15 21:39:47.307 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[2]: 工号=20231054, 薪资=1005.00
2025-08-15 21:39:47.308 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[3]: 工号=20241001, 薪资=1005.00
2025-08-15 21:39:47.309 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[4]: 工号=20231033, 薪资=1005.00
2025-08-15 21:39:47.310 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:479 | 可见行数据顺序: ['20211012', '20221065', '20231054', '20241001', '20231033']
2025-08-15 21:39:47.311 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:442 | 表格数据已设置: 50 行, 24 列
2025-08-15 21:39:47.318 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 50 行, 耗时: 114.1ms
2025-08-15 21:39:47.319 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7926 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-15 21:39:47.320 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7936 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-08-15 21:39:47.322 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7937 | 🔧 [P0-排序修复] 排序描述: 2025年薪级工资: 升序
2025-08-15 21:39:47.327 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: salary_data_2025_08_active_employees (24/24 列)
2025-08-15 21:39:47.331 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-15 21:39:47.332 | INFO     | src.gui.prototype.prototype_main_window:set_data:997 | 📊[pagination-write] 跳过写入当前页数据量(total=50)，保持 total=1396，等待数据事件纠正
2025-08-15 21:39:47.333 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3887 | 🔧 [P1-1修复] 数据已成功设置到UI，使用新映射表头: 50行, 24列
2025-08-15 21:39:47.334 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 24 个, 行号起始 1, 共 50 行
2025-08-15 21:39:47.335 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3903 | 🔧 [P0-新5修复] 表头强制刷新完成: salary_data_2025_08_active_employees, 传递参数: 24个表头
2025-08-15 21:39:47.336 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3907 | 🔧 [统一数据设置] 数据已成功设置到UI: 50行, 24列
2025-08-15 21:39:47.337 | INFO     | src.gui.widgets.pagination_widget:batch_update:472 | 📊[pagination-write] batch_update | src=_on_new_data_updated/initial_load | rid=PG-1755265187077-6e372625 | total: 1396->1396, page: 2->2, size: 50->50, pages: 28->28
2025-08-15 21:39:47.338 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3941 | ✅[pagination-state] now | page=2 / pages=28 / total=1396 | rid=PG-1755265187077-6e372625
2025-08-15 21:39:47.339 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3954 | 🔍 [FINAL修复] 最终调用set_pagination_state: 第2页, 记录51-100 | rid=PG-1755265187077-6e372625
2025-08-15 21:39:47.345 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4166 | 🔍 [表格调试] ================== set_pagination_state 开始 ==================
2025-08-15 21:39:47.346 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4167 | 🔍 [表格调试] 收到分页状态参数: {'current_page': 2, 'page_size': 50, 'total_records': 1396, 'start_record': 51, 'end_record': 100}
2025-08-15 21:39:47.347 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4168 | 🔍 [表格调试] 当前表格行数: 50
2025-08-15 21:39:47.349 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4169 | 🔍 [表格调试] 当前表格列数: 24
2025-08-15 21:39:47.349 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4171 | 🔧 [分页行号修复] 接收分页状态: 第2页, 记录51-100
2025-08-15 21:39:47.350 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: salary_data_2025_08_active_employees (24/24 列)
2025-08-15 21:39:47.352 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4198 | 🔧 [P0-3修复] 分页时已恢复列宽设置: salary_data_2025_08_active_employees
2025-08-15 21:39:47.354 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4206 | 🔧 [分页行号修复] 表格状态: 当前行数=50, 期望行数=50
2025-08-15 21:39:47.361 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 24 个, 行号起始 51, 共 50 行
2025-08-15 21:39:47.363 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_force_update_pagination_row_numbers:4286 | 🔧 [分页行号修复] 强制更新完成: 起始记录51, 共50行
2025-08-15 21:39:47.364 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3958 | 🔍 [FINAL修复] set_pagination_state最终调用完成
2025-08-15 21:39:47.365 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3972 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-08-15 21:39:47.366 | INFO     | src.services.table_data_service:load_table_data:539 | 📨[data_event] published
2025-08-15 21:39:47.366 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4886 | 🚫 [用户要求] 不显示加载条，直接处理数据: salary_data_2025_08_active_employees 第2页
2025-08-15 21:39:47.367 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4889 | 🔧 [根本修复] 分页数据获取成功: 第2页, 50行
2025-08-15 21:39:47.368 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4895 | 🔧 [根本修复] 获取数据的前3个工号: ['无工号']
2025-08-15 21:39:47.376 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4903 | [数据流追踪] 分页数据请求成功: 第2页, 50行
2025-08-15 21:39:47.376 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4912 | 🔧 [立即修复] 分页处理标志已清除，允许UI更新: 第2页
2025-08-15 21:39:47.377 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4917 | 🧹 [智能修复] 数据返回完成，缓存状态: False, 第2页
2025-08-15 21:39:47.378 | INFO     | src.gui.widgets.pagination_widget:batch_update:472 | 📊[pagination-write] batch_update | src=_on_pagination_data_loaded | rid=PL-1755265187378-14de2db6 | total: 1396->1396, page: 2->2, size: 50->50, pages: 28->28
2025-08-15 21:39:47.379 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4930 | ✅[pagination-state] now | page=2 / pages=28 / total=1396 | rid=PL-1755265187378-14de2db6
2025-08-15 21:39:47.381 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4939 | 🔧 [P0-2修复] 字段映射应用完成: 28列
2025-08-15 21:39:47.383 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4943 | 🔧 [P0-CRITICAL修复] 系统字段过滤完成: 28列 -> 24列
2025-08-15 21:39:47.391 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4952 | 🔧 [P0-2修复] 分页UI更新成功: 第2页, 50行数据
2025-08-15 21:39:47.392 | INFO     | src.gui.prototype.prototype_main_window:_restore_table_ui_state:10642 | 🔧 [P0-新4修复] 通过StateManager恢复状态成功: salary_data_2025_08_active_employees
2025-08-15 21:39:47.393 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 24 个, 行号起始 51, 共 50 行
2025-08-15 21:39:47.394 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4970 | 🔧 [P0-CRITICAL修复] 强制刷新表头完成: 24个过滤后的表头（无系统字段）, 记录51-100
2025-08-15 21:39:47.395 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4981 | 🔧 [P0-2修复] 分页处理完成，UI已更新: 第2页
2025-08-15 21:39:47.396 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:5017 | 🔧 [立即修复] 分页处理标志已原子清除: 第2页
2025-08-15 21:39:47.397 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:5042 | 🔧 [立即修复] 分页处理完成，标志已清除: 第2页
2025-08-15 21:39:47.398 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4768 | 📍[page-change] 入口 | table=salary_data_2025_08_active_employees | target_page=2 | request_id=PG-1755265187398-24b3cf0e
2025-08-15 21:39:47.400 | INFO     | src.gui.prototype.prototype_main_window:clear_field_processing_cache:7240 | 🔧 [缓存清理] 清理了表 salary_data_2025_08_active_employees 的 0 个缓存条目
2025-08-15 21:39:47.408 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:362 | 已清理表 salary_data_2025_08_active_employees 的缓存
2025-08-15 21:39:47.409 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4796 | 🔧 [P0-3修复] 已清理表 salary_data_2025_08_active_employees 的相关缓存
2025-08-15 21:39:47.410 | INFO     | src.core.pagination_state_manager:can_start_pagination:113 | ✅ [允许] 分页请求可以开始: salary_data_2025_08_active_employees, 页2
2025-08-15 21:39:47.411 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4831 | 🔧 [立即修复] 分页事件被去重优化: 第2页
2025-08-15 21:39:47.414 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:5017 | 🔧 [立即修复] 分页处理标志已原子清除: 第2页
2025-08-15 21:39:47.417 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:5042 | 🔧 [立即修复] 分页处理完成，标志已清除: 第2页
2025-08-15 21:39:47.422 | INFO     | src.core.pagination_state_manager:start_pagination:152 | 🔧 [开始] 分页操作已开始: salary_data_2025_08_active_employees, 页2, ID=page_1755265187422830_8428
2025-08-15 21:39:47.423 | INFO     | src.gui.prototype.prototype_main_window:_execute_pagination_data_load:5048 | 🔧 [事件优化] 开始执行分页数据加载: 第2页
2025-08-15 21:39:47.424 | INFO     | src.core.request_deduplication_manager:__init__:65 | 请求去重管理器初始化完成，默认TTL: 0.3秒
2025-08-15 21:39:47.425 | INFO     | src.services.table_data_service:get_paginated_data:875 | 🔧 [P1修复] 获取分页数据: salary_data_2025_08_active_employees, 页码: 2, 页大小: 50
2025-08-15 21:39:47.426 | INFO     | src.services.table_data_service:load_table_data:390 | [缓存命中] 使用缓存数据: 第2页
2025-08-15 21:39:47.428 | INFO     | src.services.table_data_service:load_table_data:401 | 📨[data_event] publish(cache) | table=salary_data_2025_08_active_employees | rows=50 | page=2 | size=50 | total=1396 | request_id=SV-1755265187428-315d74ae-C
2025-08-15 21:39:47.428 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3785 | 📥[data_event] received | table=salary_data_2025_08_active_employees | request_id=SV-1755265187428-315d74ae-C
2025-08-15 21:39:47.429 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3817 | 数据内容: 50行 x 28列
2025-08-15 21:39:47.430 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3843 | 🔧 [数据更新] 接收到cached_load操作的数据更新事件: salary_data_2025_08_active_employees, 50行
2025-08-15 21:39:47.443 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7327 | 表 salary_data_2025_08_active_employees 没有用户偏好设置，显示所有可见字段
2025-08-15 21:39:47.445 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3887 | 🔧 [P1-1修复] 数据已成功设置到UI，使用新映射表头: 50行, 24列
2025-08-15 21:39:47.446 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 24 个, 行号起始 1, 共 50 行
2025-08-15 21:39:47.452 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3903 | 🔧 [P0-新5修复] 表头强制刷新完成: salary_data_2025_08_active_employees, 传递参数: 24个表头
2025-08-15 21:39:47.453 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3907 | 🔧 [统一数据设置] 数据已成功设置到UI: 50行, 24列
2025-08-15 21:39:47.454 | INFO     | src.gui.widgets.pagination_widget:batch_update:472 | 📊[pagination-write] batch_update | src=_on_new_data_updated/cached_load | rid=SV-1755265187428-315d74ae-C | total: 1396->1396, page: 2->2, size: 50->50, pages: 28->28
2025-08-15 21:39:47.455 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3941 | ✅[pagination-state] now | page=2 / pages=28 / total=1396 | rid=SV-1755265187428-315d74ae-C
2025-08-15 21:39:47.456 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3954 | 🔍 [FINAL修复] 最终调用set_pagination_state: 第2页, 记录51-100 | rid=SV-1755265187428-315d74ae-C
2025-08-15 21:39:47.457 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4166 | 🔍 [表格调试] ================== set_pagination_state 开始 ==================
2025-08-15 21:39:47.458 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4167 | 🔍 [表格调试] 收到分页状态参数: {'current_page': 2, 'page_size': 50, 'total_records': 1396, 'start_record': 51, 'end_record': 100}
2025-08-15 21:39:47.465 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4168 | 🔍 [表格调试] 当前表格行数: 50
2025-08-15 21:39:47.465 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4169 | 🔍 [表格调试] 当前表格列数: 24
2025-08-15 21:39:47.465 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4171 | 🔧 [分页行号修复] 接收分页状态: 第2页, 记录51-100
2025-08-15 21:39:47.467 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: salary_data_2025_08_active_employees (24/24 列)
2025-08-15 21:39:47.467 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4198 | 🔧 [P0-3修复] 分页时已恢复列宽设置: salary_data_2025_08_active_employees
2025-08-15 21:39:47.468 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4206 | 🔧 [分页行号修复] 表格状态: 当前行数=50, 期望行数=50
2025-08-15 21:39:47.469 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 24 个, 行号起始 51, 共 50 行
2025-08-15 21:39:47.472 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_force_update_pagination_row_numbers:4286 | 🔧 [分页行号修复] 强制更新完成: 起始记录51, 共50行
2025-08-15 21:39:47.472 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3958 | 🔍 [FINAL修复] set_pagination_state最终调用完成
2025-08-15 21:39:47.483 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3972 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-08-15 21:39:47.484 | INFO     | src.services.table_data_service:load_table_data:426 | 📨[data_event] published(cache)
2025-08-15 21:39:47.485 | INFO     | src.services.table_data_service:get_paginated_data:887 | 🔧 [P1修复] 分页数据获取成功: salary_data_2025_08_active_employees, 返回 50 行
2025-08-15 21:39:47.486 | INFO     | src.gui.prototype.prototype_main_window:_execute_pagination_data_load:5088 | 🔧 [事件优化] 分页数据加载成功，UI更新已加入队列: 第2页
2025-08-15 21:39:47.488 | INFO     | src.core.pagination_state_manager:complete_pagination:189 | 🔧 [P1修复] 分页操作已完成: salary_data_2025_08_active_employees, 页2, 成功=True, 耗时=0.066s
2025-08-15 21:39:47.489 | INFO     | src.core.pagination_event_optimizer:process_events:167 | 🔧 [事件处理] 批量处理完成: 1个事件
2025-08-15 21:39:47.527 | INFO     | src.gui.prototype.prototype_main_window:_restore_table_ui_state:10642 | 🔧 [P0-新4修复] 通过StateManager恢复状态成功: salary_data_2025_08_active_employees
2025-08-15 21:39:47.529 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7961 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: salary_data_2025_08_active_employees
2025-08-15 21:39:47.587 | INFO     | src.gui.prototype.prototype_main_window:_update_pagination_ui:5115 | 🔧 [P0修复] 开始分页UI更新: salary_data_2025_08_active_employees, 第2页
2025-08-15 21:39:47.588 | INFO     | src.gui.prototype.prototype_main_window:clear_field_processing_cache:7240 | 🔧 [缓存清理] 清理了表 salary_data_2025_08_active_employees 的 0 个缓存条目
2025-08-15 21:39:47.590 | INFO     | src.gui.prototype.prototype_main_window:_update_pagination_ui:5119 | 🔧 [P0-3修复] 已清理表 salary_data_2025_08_active_employees 的字段处理缓存
2025-08-15 21:39:47.591 | INFO     | src.gui.prototype.prototype_main_window:_update_pagination_ui:5131 | 🔧 [P0修复] 原始数据: 50行, 28列
2025-08-15 21:39:47.593 | INFO     | src.gui.prototype.prototype_main_window:_update_pagination_ui:5135 | 🔧 [P0修复] 字段映射后: 50行, 28列
2025-08-15 21:39:47.600 | INFO     | src.gui.prototype.prototype_main_window:_update_pagination_ui:5139 | 🔧 [P0修复] 系统字段过滤后: 50行, 24列
2025-08-15 21:39:47.607 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7327 | 表 salary_data_2025_08_active_employees 没有用户偏好设置，显示所有可见字段
2025-08-15 21:39:47.607 | INFO     | src.gui.prototype.prototype_main_window:_update_pagination_ui:5144 | 🔧 [P0修复] 用户偏好过滤后: 50行, 24列
2025-08-15 21:39:47.609 | INFO     | src.gui.prototype.prototype_main_window:_update_pagination_ui:5165 | 🔧 [P0修复] 分页数据设置成功: salary_data_2025_08_active_employees, 第2页, 50行, 24列
2025-08-15 21:39:47.610 | INFO     | src.gui.prototype.prototype_main_window:_restore_table_ui_state:10642 | 🔧 [P0-新4修复] 通过StateManager恢复状态成功: salary_data_2025_08_active_employees
2025-08-15 21:39:47.611 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 24 个, 行号起始 51, 共 50 行
2025-08-15 21:39:47.611 | INFO     | src.gui.prototype.prototype_main_window:_update_pagination_ui:5182 | 🔧 [P0修复] 表头强制刷新完成: 24个过滤后的表头
2025-08-15 21:39:47.613 | INFO     | src.gui.prototype.prototype_main_window:_update_pagination_ui:5184 | 🔧 [P0修复] 分页UI更新完成: 第2页，50条记录，24个字段
2025-08-15 21:39:47.614 | INFO     | src.core.pagination_event_optimizer:process_events:167 | 🔧 [事件处理] 批量处理完成: 1个事件
2025-08-15 21:39:56.578 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 08月 > 全部在职人员', '工资表 > 2025年', '工资表', '工资表 > 2025年 > 05月 > 全部在职人员', '工资表 > 2025年 > 08月']
2025-08-15 21:39:56.578 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7370 | 导航变化: 工资表 > 2025年 > 08月 > 离休人员
2025-08-15 21:39:56.578 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10489 | 🚫 [用户要求] 表格切换不显示加载条: salary_data_2025_08_active_employees -> None
2025-08-15 21:39:56.578 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10505 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-08-15 21:39:56.578 | INFO     | src.gui.prototype.prototype_main_window:clear_field_processing_cache:7245 | 🔧 [缓存清理] 清理了所有 0 个字段处理缓存条目
2025-08-15 21:39:56.578 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8297 | 🔧 [表名生成] 从完整路径生成表名: ['工资表', '2025年', '08月', '离休人员'] -> salary_data_2025_08_retired_employees
2025-08-15 21:39:56.578 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-15 21:39:56.578 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:362 | 已清理表 salary_data_2025_08_retired_employees 的缓存
2025-08-15 21:39:56.594 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:11175 | 已注册 4 个表格到表头管理器
2025-08-15 21:39:56.594 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1188 | 🔧 [P1-3] 开始智能表头重影检测，共 4 个表格
2025-08-15 21:39:56.594 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1261 | 🔧 [P1-3] 智能表头重影检测完成，耗时 0.00ms
2025-08-15 21:39:56.594 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1262 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-08-15 21:39:56.594 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7586 | 🆕 使用新架构加载数据: salary_data_2025_08_retired_employees（通过事件系统）
2025-08-15 21:39:56.594 | INFO     | src.services.table_data_service:load_table_data:366 | 加载表格数据: 页码: 1
2025-08-15 21:39:56.594 | INFO     | src.core.unified_data_request_manager:request_table_data:212 | 开始处理数据请求
2025-08-15 21:39:56.594 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:805 | 正在从表 salary_data_2025_08_retired_employees 分页获取数据（支持排序）: 第1页, 每页50条, 排序=0列
2025-08-15 21:39:56.594 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:921 | 成功从表 salary_data_2025_08_retired_employees 获取第1页数据（含排序）: 2 行，总计2行
2025-08-15 21:39:56.594 | INFO     | src.core.unified_data_request_manager:request_table_data:262 | 数据请求处理完成: 字段=21, 行数=2, 耗时=0.0ms
2025-08-15 21:39:56.594 | INFO     | src.services.table_data_service:load_table_data:462 | [新架构] 使用统一格式管理器（单例优化）
2025-08-15 21:39:56.594 | INFO     | src.modules.format_management.format_renderer:render_dataframe:107 | 🎯 [格式渲染] 隐藏字段配置: ['id', 'updated_at', 'created_at', 'sequence_number']
2025-08-15 21:39:56.594 | INFO     | src.modules.format_management.format_renderer:render_dataframe:115 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-08-15 21:39:56.609 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 增发一次性生活补贴 -> 英文名: one_time_living_allowance -> 类型: float
2025-08-15 21:39:56.609 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-08-15 21:39:56.609 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-08-15 21:39:56.609 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 备注 -> 英文名: remarks -> 类型: string
2025-08-15 21:39:56.609 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 月份 -> 英文名: month -> 类型: month_string_extract_last_two
2025-08-15 21:39:56.609 | INFO     | src.modules.format_management.field_registry:get_display_fields:1554 | 🔧 [P0-2修复] display_order转换完成: 17个字段，原始字段数: 17
2025-08-15 21:39:56.609 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | 🔧 [DEBUG] table_type=retired_employees, display_fields=17个字段
2025-08-15 21:39:56.609 | INFO     | src.modules.format_management.format_renderer:render_dataframe:211 | 🔧 [P0修复] 配置字段匹配完成: 成功匹配 17/17 个字段
2025-08-15 21:39:56.609 | INFO     | src.modules.format_management.format_renderer:render_dataframe:219 | 🔧 [P0-关键修复] 保留未配置的业务字段: 4个
2025-08-15 21:39:56.636 | INFO     | src.modules.format_management.format_renderer:render_dataframe:224 | 🔧 [P0修复] 字段重排完成: 原始21个 -> 最终21个字段
2025-08-15 21:39:56.641 | INFO     | src.modules.format_management.format_renderer:render_dataframe:240 | 🎨 [格式渲染] DataFrame格式化完成: retired_employees, 行数: 2, 列数: 21
2025-08-15 21:39:56.641 | INFO     | src.modules.format_management.unified_format_manager:format_data:367 | 🎯 [统一格式管理] 数据格式化完成: retired_employees, 行数: 2, 列数: 21
2025-08-15 21:39:56.641 | INFO     | src.core.unified_state_manager:update_table_state:233 | 表状态已更新: salary_data_2025_08_retired_employees, 变更类型: None
2025-08-15 21:39:56.641 | INFO     | src.services.table_data_service:load_table_data:512 | [修复数据发布] 数据加载成功，发布更新事件: 2行
2025-08-15 21:39:56.641 | INFO     | src.services.table_data_service:load_table_data:516 | 📨[data_event] publish | table=salary_data_2025_08_retired_employees | rows=2 | page=1 | size=50 | total=2 | req_type=RequestType.INITIAL_LOAD | request_id=SV-1755265196641-b710ab64
2025-08-15 21:39:56.641 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3785 | 📥[data_event] received | table=salary_data_2025_08_retired_employees | request_id=SV-1755265196641-b710ab64
2025-08-15 21:39:56.641 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3817 | 数据内容: 2行 x 21列
2025-08-15 21:39:56.641 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3843 | 🔧 [数据更新] 接收到initial_load操作的数据更新事件: salary_data_2025_08_retired_employees, 2行
2025-08-15 21:39:56.641 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7327 | 表 salary_data_2025_08_retired_employees 没有用户偏好设置，显示所有可见字段
2025-08-15 21:39:56.641 | INFO     | src.gui.prototype.prototype_main_window:set_data:873 | 通过公共接口设置新的表格数据(DataFrame): 2 行
2025-08-15 21:39:56.656 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 2行，表名: salary_data_2025_08_retired_employees
2025-08-15 21:39:56.656 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=19289006, 薪资=N/A
2025-08-15 21:39:56.656 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=19339009, 薪资=N/A
2025-08-15 21:39:56.672 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:5271 | 最大可见行数已更新: 50 -> 10
2025-08-15 21:39:56.672 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5324 | 根据数据量(2)自动调整最大可见行数为: 10
2025-08-15 21:39:56.672 | INFO     | src.modules.format_management.format_renderer:render_dataframe:107 | 🎯 [格式渲染] 隐藏字段配置: ['id', 'updated_at', 'created_at', 'sequence_number']
2025-08-15 21:39:56.672 | INFO     | src.modules.format_management.format_renderer:render_dataframe:115 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-08-15 21:39:56.688 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 增发一次性生活补贴 -> 英文名: one_time_living_allowance -> 类型: float
2025-08-15 21:39:56.688 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-08-15 21:39:56.688 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-08-15 21:39:56.703 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 备注 -> 英文名: remarks -> 类型: string
2025-08-15 21:39:56.703 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 月份 -> 英文名: month -> 类型: month_string_extract_last_two
2025-08-15 21:39:56.703 | INFO     | src.modules.format_management.field_registry:get_display_fields:1554 | 🔧 [P0-2修复] display_order转换完成: 17个字段，原始字段数: 17
2025-08-15 21:39:56.703 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | 🔧 [DEBUG] table_type=retired_employees, display_fields=17个字段
2025-08-15 21:39:56.703 | INFO     | src.modules.format_management.format_renderer:render_dataframe:211 | 🔧 [P0修复] 配置字段匹配完成: 成功匹配 17/17 个字段
2025-08-15 21:39:56.703 | INFO     | src.modules.format_management.format_renderer:render_dataframe:224 | 🔧 [P0修复] 字段重排完成: 原始17个 -> 最终17个字段
2025-08-15 21:39:56.703 | INFO     | src.modules.format_management.format_renderer:render_dataframe:240 | 🎨 [格式渲染] DataFrame格式化完成: retired_employees, 行数: 2, 列数: 17
2025-08-15 21:39:56.703 | INFO     | src.modules.format_management.unified_format_manager:format_data:367 | 🎯 [统一格式管理] 数据格式化完成: retired_employees, 行数: 2, 列数: 17
2025-08-15 21:39:56.719 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 2行数据
2025-08-15 21:39:56.719 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:52 | [数据流追踪] 开始小数据集渲染: 2行 x 17列
2025-08-15 21:39:56.719 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:112 | [数据流追踪] 小数据集渲染完成: 耗时0.0ms, 平均每行0.00ms
2025-08-15 21:39:56.719 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=2, 渲染时间=0.0ms, 策略=small_dataset
2025-08-15 21:39:56.734 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:279 | [数据流追踪] 选择小数据集渲染策略
2025-08-15 21:39:56.734 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19289006
2025-08-15 21:39:56.734 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 19339009
2025-08-15 21:39:56.734 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 2
2025-08-15 21:39:56.734 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[0]: 工号=19289006, 薪资=N/A
2025-08-15 21:39:56.734 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[1]: 工号=19339009, 薪资=N/A
2025-08-15 21:39:56.734 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:479 | 可见行数据顺序: ['19289006', '19339009']
2025-08-15 21:39:56.734 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:442 | 表格数据已设置: 2 行, 17 列
2025-08-15 21:39:56.746 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:417 | 🔧 [新架构] 排序管理器切换到表格: salary_data_2025_08_retired_employees
2025-08-15 21:39:56.752 | INFO     | src.gui.prototype.widgets.column_sort_manager:_reload_field_mapping_for_table:448 | 🔧 [新架构] 为表格 salary_data_2025_08_retired_employees 重新加载 21 个字段映射
2025-08-15 21:39:56.753 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_state_changed:7425 | 🆕 [新架构多列排序] 排序状态变化: 0 列
2025-08-15 21:39:56.754 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_state_changed:7433 | 🆕 [新架构多列排序] 当前排序: 无排序
2025-08-15 21:39:56.755 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_cleared:7470 | 🆕 [新架构多列排序] 排序已清除
2025-08-15 21:39:56.756 | INFO     | src.gui.prototype.prototype_main_window:_handle_sort_cleared:4554 | 🆕 [新架构排序] 处理排序清除
2025-08-15 21:39:56.757 | INFO     | src.gui.prototype.prototype_main_window:_handle_sort_cleared:4565 | 排序状态已从管理器中清除
2025-08-15 21:39:56.758 | INFO     | src.gui.prototype.prototype_main_window:_handle_sort_cleared:4571 | 清除排序，当前页码: 1
2025-08-15 21:39:56.759 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:4202 | 🔧 [排序调试] 准备发布排序请求: salary_data_2025_08_retired_employees, []
2025-08-15 21:39:56.759 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:4213 | 🔧 [P3修复] 排序列为空，尝试从状态管理器恢复
2025-08-15 21:39:56.767 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:4239 | [数据流追踪] 开始处理排序请求: 表=salary_data_2025_08_retired_employees, 排序列数=0, 页码=1
2025-08-15 21:39:56.767 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:4285 | 🔧 [排序] 使用实际页码: 1 (传入页码: 1)
2025-08-15 21:39:56.768 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:4311 | 🔧 [P1-修复] 转换后排序列为空，将发布清空排序事件
2025-08-15 21:39:56.769 | INFO     | src.services.table_data_service:_handle_sort_request:154 | [排序调试] 接收到排序请求
2025-08-15 21:39:56.770 | INFO     | src.services.table_data_service:_handle_sort_request:166 | [排序调试] 收到清空排序请求，将清理排序状态
2025-08-15 21:39:56.772 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:805 | 正在从表 salary_data_2025_08_retired_employees 分页获取数据（支持排序）: 第1页, 每页50条, 排序=0列
2025-08-15 21:39:56.774 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:921 | 成功从表 salary_data_2025_08_retired_employees 获取第1页数据（含排序）: 2 行，总计2行
2025-08-15 21:39:56.775 | INFO     | src.services.table_data_service:_handle_sort_request:205 | [修复排序] 排序操作成功，发布数据更新事件: salary_data_2025_08_retired_employees, 2行
2025-08-15 21:39:56.783 | INFO     | src.services.table_data_service:_handle_sort_request:225 | [修复排序] 数据更新事件已发布
2025-08-15 21:39:56.785 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:4324 | [数据流追踪] 排序事件已发布: 表=salary_data_2025_08_retired_employees, 排序列数=0, 页码=1
2025-08-15 21:39:56.785 | INFO     | src.gui.prototype.widgets.column_sort_manager:clear_all_sorts:268 | 🆕 [多列排序] 已清除所有排序
2025-08-15 21:39:56.786 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 2 行, 耗时: 89.5ms
2025-08-15 21:39:56.787 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7926 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-15 21:39:56.788 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7939 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-15 21:39:56.791 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1723 | 🆕 [列宽保存] 未找到表格 salary_data_2025_08_retired_employees 的列宽配置
2025-08-15 21:39:56.797 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-15 21:39:56.798 | INFO     | src.gui.prototype.prototype_main_window:set_data:997 | 📊[pagination-write] 跳过写入当前页数据量(total=2)，保持 total=0，等待数据事件纠正
2025-08-15 21:39:56.799 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3887 | 🔧 [P1-1修复] 数据已成功设置到UI，使用新映射表头: 2行, 17列
2025-08-15 21:39:56.800 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 17 个, 行号起始 1, 共 2 行
2025-08-15 21:39:56.801 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3903 | 🔧 [P0-新5修复] 表头强制刷新完成: salary_data_2025_08_retired_employees, 传递参数: 17个表头
2025-08-15 21:39:56.802 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3907 | 🔧 [统一数据设置] 数据已成功设置到UI: 2行, 17列
2025-08-15 21:39:56.803 | INFO     | src.gui.widgets.pagination_widget:batch_update:472 | 📊[pagination-write] batch_update | src=_on_new_data_updated/initial_load | rid=SV-1755265196641-b710ab64 | total: 0->2, page: 1->1, size: 50->50, pages: 1->1
2025-08-15 21:39:56.804 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3941 | ✅[pagination-state] now | page=1 / pages=1 / total=2 | rid=SV-1755265196641-b710ab64
2025-08-15 21:39:56.805 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3954 | 🔍 [FINAL修复] 最终调用set_pagination_state: 第1页, 记录1-2 | rid=SV-1755265196641-b710ab64
2025-08-15 21:39:56.816 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4166 | 🔍 [表格调试] ================== set_pagination_state 开始 ==================
2025-08-15 21:39:56.817 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4167 | 🔍 [表格调试] 收到分页状态参数: {'current_page': 1, 'page_size': 50, 'total_records': 2, 'start_record': 1, 'end_record': 2}
2025-08-15 21:39:56.818 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4168 | 🔍 [表格调试] 当前表格行数: 2
2025-08-15 21:39:56.819 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4169 | 🔍 [表格调试] 当前表格列数: 17
2025-08-15 21:39:56.820 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4171 | 🔧 [分页行号修复] 接收分页状态: 第1页, 记录1-2
2025-08-15 21:39:56.821 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1723 | 🆕 [列宽保存] 未找到表格 salary_data_2025_08_retired_employees 的列宽配置
2025-08-15 21:39:56.821 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4198 | 🔧 [P0-3修复] 分页时已恢复列宽设置: salary_data_2025_08_retired_employees
2025-08-15 21:39:56.822 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4206 | 🔧 [分页行号修复] 表格状态: 当前行数=2, 期望行数=2
2025-08-15 21:39:56.822 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 17 个, 行号起始 1, 共 2 行
2025-08-15 21:39:56.830 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_force_update_pagination_row_numbers:4286 | 🔧 [分页行号修复] 强制更新完成: 起始记录1, 共2行
2025-08-15 21:39:56.831 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3958 | 🔍 [FINAL修复] set_pagination_state最终调用完成
2025-08-15 21:39:56.888 | INFO     | src.gui.table_header_manager:cleanup_callback:162 | 🔧 [P1-1] 表格 table_3_1580491932448 已自动清理（弱引用回调）
2025-08-15 21:39:56.889 | INFO     | src.gui.table_header_manager:cleanup_callback:162 | 🔧 [P1-1] 表格 table_2_1580491933024 已自动清理（弱引用回调）
2025-08-15 21:39:56.904 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:379 | 已清理所有缓存
2025-08-15 21:39:56.905 | INFO     | src.gui.prototype.prototype_main_window:_validate_component_state_consistency:10983 | ✅ [新架构] 组件状态一致性验证通过
2025-08-15 21:39:56.905 | INFO     | src.gui.prototype.prototype_main_window:complete_navigation_transition:10946 | 🆕 [新架构] 导航迁移完成
2025-08-15 21:39:56.906 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3972 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-08-15 21:39:56.908 | INFO     | src.services.table_data_service:load_table_data:539 | 📨[data_event] published
2025-08-15 21:39:56.908 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7591 | 🆕 新架构数据加载请求已发送，等待事件系统处理
2025-08-15 21:39:56.909 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:1025 | 导航选择: 工资表 > 2025年 > 08月 > 离休人员
2025-08-15 21:39:56.911 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7961 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: salary_data_2025_08_retired_employees
2025-08-15 21:39:58.802 | INFO     | src.gui.prototype.widgets.column_sort_manager:handle_header_click:138 | 🆕 [多列排序] 新增列3(基本离休费)排序: 升序
2025-08-15 21:39:58.802 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_state_changed:7425 | 🆕 [新架构多列排序] 排序状态变化: 1 列
2025-08-15 21:39:58.802 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_state_changed:7433 | 🆕 [新架构多列排序] 当前排序: 基本离休费: 升序
2025-08-15 21:39:58.802 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_request:7448 | 🆕 [新架构多列排序] 排序请求: salary_data_2025_08_retired_employees.basic_retirement_salary -> ascending
2025-08-15 21:39:58.817 | INFO     | src.gui.prototype.prototype_main_window:_handle_sort_applied_impl:4527 | 🔧 [P0-CRITICAL] 简化排序处理: 列3, basic_retirement_salary, ascending
2025-08-15 21:39:58.817 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:4202 | 🔧 [排序调试] 准备发布排序请求: salary_data_2025_08_retired_employees, [{'column_name': 'basic_retirement_salary', 'order': 'ascending', 'priority': 0}]
2025-08-15 21:39:58.817 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:4239 | [数据流追踪] 开始处理排序请求: 表=salary_data_2025_08_retired_employees, 排序列数=1, 页码=1
2025-08-15 21:39:58.817 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:4285 | 🔧 [排序] 使用实际页码: 1 (传入页码: 1)
2025-08-15 21:39:58.817 | INFO     | src.services.table_data_service:_handle_sort_request:154 | [排序调试] 接收到排序请求
2025-08-15 21:39:58.817 | INFO     | src.core.unified_state_manager:update_table_state:233 | 表状态已更新: salary_data_2025_08_retired_employees, 变更类型: StateChangeType.SORT_CHANGED
2025-08-15 21:39:58.817 | INFO     | src.core.unified_data_request_manager:request_table_data:212 | 开始处理数据请求
2025-08-15 21:39:58.817 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:994 | [FIX] [排序修复] 列 'basic_retirement_salary' 存在于表 'salary_data_2025_08_retired_employees' 中（类型: REAL）
2025-08-15 21:39:58.817 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:994 | [FIX] [排序修复] 列 'basic_retirement_salary' 存在于表 'salary_data_2025_08_retired_employees' 中（类型: REAL）
2025-08-15 21:39:58.817 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:805 | 正在从表 salary_data_2025_08_retired_employees 分页获取数据（支持排序）: 第1页, 每页50条, 排序=1列
2025-08-15 21:39:58.817 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:994 | [FIX] [排序修复] 列 'basic_retirement_salary' 存在于表 'salary_data_2025_08_retired_employees' 中（类型: REAL）
2025-08-15 21:39:58.817 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:921 | 成功从表 salary_data_2025_08_retired_employees 获取第1页数据（含排序）: 2 行，总计2行
2025-08-15 21:39:58.817 | INFO     | src.core.unified_data_request_manager:request_table_data:262 | 数据请求处理完成: 字段=21, 行数=2, 耗时=0.0ms
2025-08-15 21:39:58.833 | INFO     | src.services.table_data_service:_handle_sort_request:205 | [修复排序] 排序操作成功，发布数据更新事件: salary_data_2025_08_retired_employees, 2行
2025-08-15 21:39:58.833 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3785 | 📥[data_event] received | table=salary_data_2025_08_retired_employees | request_id=None
2025-08-15 21:39:58.849 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3817 | 数据内容: 2行 x 21列
2025-08-15 21:39:58.849 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3840 | 🔧 [关键修复] 接收到sort_change操作的数据更新事件，开始UI更新: salary_data_2025_08_retired_employees, 2行
2025-08-15 21:39:58.849 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7327 | 表 salary_data_2025_08_retired_employees 没有用户偏好设置，显示所有可见字段
2025-08-15 21:39:58.849 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3869 | 🚀 [排序优化] 检测到排序操作，标记为高优先级同步更新
2025-08-15 21:39:58.849 | INFO     | src.gui.prototype.prototype_main_window:set_data:873 | 通过公共接口设置新的表格数据(DataFrame): 2 行
2025-08-15 21:39:58.849 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7327 | 表 salary_data_2025_08_retired_employees 没有用户偏好设置，显示所有可见字段
2025-08-15 21:39:58.849 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 2行，表名: salary_data_2025_08_retired_employees
2025-08-15 21:39:58.849 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=19339009.0, 薪资=N/A
2025-08-15 21:39:58.849 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=19289006.0, 薪资=N/A
2025-08-15 21:39:58.849 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:5271 | 最大可见行数已更新: 10 -> 10
2025-08-15 21:39:58.849 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5324 | 根据数据量(2)自动调整最大可见行数为: 10
2025-08-15 21:39:58.864 | INFO     | src.modules.format_management.format_renderer:render_dataframe:107 | 🎯 [格式渲染] 隐藏字段配置: ['id', 'updated_at', 'created_at', 'sequence_number']
2025-08-15 21:39:58.864 | INFO     | src.modules.format_management.format_renderer:render_dataframe:115 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-08-15 21:39:58.882 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 增发一次性生活补贴 -> 英文名: one_time_living_allowance -> 类型: float
2025-08-15 21:39:58.882 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-08-15 21:39:58.882 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-08-15 21:39:58.882 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 备注 -> 英文名: remarks -> 类型: string
2025-08-15 21:39:58.882 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 月份 -> 英文名: month -> 类型: month_string_extract_last_two
2025-08-15 21:39:58.882 | INFO     | src.modules.format_management.field_registry:get_display_fields:1554 | 🔧 [P0-2修复] display_order转换完成: 17个字段，原始字段数: 17
2025-08-15 21:39:58.882 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | 🔧 [DEBUG] table_type=retired_employees, display_fields=17个字段
2025-08-15 21:39:58.882 | INFO     | src.modules.format_management.format_renderer:render_dataframe:211 | 🔧 [P0修复] 配置字段匹配完成: 成功匹配 17/17 个字段
2025-08-15 21:39:58.882 | INFO     | src.modules.format_management.format_renderer:render_dataframe:224 | 🔧 [P0修复] 字段重排完成: 原始17个 -> 最终17个字段
2025-08-15 21:39:58.882 | INFO     | src.modules.format_management.format_renderer:render_dataframe:240 | 🎨 [格式渲染] DataFrame格式化完成: retired_employees, 行数: 2, 列数: 17
2025-08-15 21:39:58.882 | INFO     | src.modules.format_management.unified_format_manager:format_data:367 | 🎯 [统一格式管理] 数据格式化完成: retired_employees, 行数: 2, 列数: 17
2025-08-15 21:39:58.895 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 2行数据
2025-08-15 21:39:58.895 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:52 | [数据流追踪] 开始小数据集渲染: 2行 x 17列
2025-08-15 21:39:58.895 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:112 | [数据流追踪] 小数据集渲染完成: 耗时0.0ms, 平均每行0.00ms
2025-08-15 21:39:58.895 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=2, 渲染时间=0.0ms, 策略=small_dataset
2025-08-15 21:39:58.895 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:279 | [数据流追踪] 选择小数据集渲染策略
2025-08-15 21:39:58.895 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19339009
2025-08-15 21:39:58.895 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 19289006
2025-08-15 21:39:58.895 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 2
2025-08-15 21:39:58.895 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[0]: 工号=19339009, 薪资=N/A
2025-08-15 21:39:58.895 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[1]: 工号=19289006, 薪资=N/A
2025-08-15 21:39:58.895 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:479 | 可见行数据顺序: ['19339009', '19289006']
2025-08-15 21:39:58.895 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:442 | 表格数据已设置: 2 行, 17 列
2025-08-15 21:39:58.909 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 2 行, 耗时: 59.5ms
2025-08-15 21:39:58.925 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7926 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-15 21:39:58.926 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7936 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-08-15 21:39:58.927 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7937 | 🔧 [P0-排序修复] 排序描述: 基本离休费: 升序
2025-08-15 21:39:58.929 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1723 | 🆕 [列宽保存] 未找到表格 salary_data_2025_08_retired_employees 的列宽配置
2025-08-15 21:39:58.930 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-15 21:39:58.931 | INFO     | src.gui.prototype.prototype_main_window:set_data:997 | 📊[pagination-write] 跳过写入当前页数据量(total=2)，保持 total=2，等待数据事件纠正
2025-08-15 21:39:58.932 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3887 | 🔧 [P1-1修复] 数据已成功设置到UI，使用新映射表头: 2行, 17列
2025-08-15 21:39:58.932 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 17 个, 行号起始 1, 共 2 行
2025-08-15 21:39:58.939 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3903 | 🔧 [P0-新5修复] 表头强制刷新完成: salary_data_2025_08_retired_employees, 传递参数: 17个表头
2025-08-15 21:39:58.940 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3907 | 🔧 [统一数据设置] 数据已成功设置到UI: 2行, 17列
2025-08-15 21:39:58.942 | INFO     | src.gui.widgets.pagination_widget:batch_update:472 | 📊[pagination-write] batch_update | src=_on_new_data_updated/sort_change | rid=None | total: 2->2, page: 1->1, size: 50->50, pages: 1->1
2025-08-15 21:39:58.944 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3941 | ✅[pagination-state] now | page=1 / pages=1 / total=2 | rid=None
2025-08-15 21:39:58.945 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3954 | 🔍 [FINAL修复] 最终调用set_pagination_state: 第1页, 记录1-2 | rid=None
2025-08-15 21:39:58.946 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4166 | 🔍 [表格调试] ================== set_pagination_state 开始 ==================
2025-08-15 21:39:58.947 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4167 | 🔍 [表格调试] 收到分页状态参数: {'current_page': 1, 'page_size': 50, 'total_records': 2, 'start_record': 1, 'end_record': 2}
2025-08-15 21:39:58.953 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4168 | 🔍 [表格调试] 当前表格行数: 2
2025-08-15 21:39:58.953 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4169 | 🔍 [表格调试] 当前表格列数: 17
2025-08-15 21:39:58.954 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4171 | 🔧 [分页行号修复] 接收分页状态: 第1页, 记录1-2
2025-08-15 21:39:58.956 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1723 | 🆕 [列宽保存] 未找到表格 salary_data_2025_08_retired_employees 的列宽配置
2025-08-15 21:39:58.957 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4198 | 🔧 [P0-3修复] 分页时已恢复列宽设置: salary_data_2025_08_retired_employees
2025-08-15 21:39:58.957 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4206 | 🔧 [分页行号修复] 表格状态: 当前行数=2, 期望行数=2
2025-08-15 21:39:58.958 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 17 个, 行号起始 1, 共 2 行
2025-08-15 21:39:58.959 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_force_update_pagination_row_numbers:4286 | 🔧 [分页行号修复] 强制更新完成: 起始记录1, 共2行
2025-08-15 21:39:58.960 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3958 | 🔍 [FINAL修复] set_pagination_state最终调用完成
2025-08-15 21:39:58.961 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3972 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-08-15 21:39:58.968 | INFO     | src.services.table_data_service:_handle_sort_request:225 | [修复排序] 数据更新事件已发布
2025-08-15 21:39:58.968 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:4324 | [数据流追踪] 排序事件已发布: 表=salary_data_2025_08_retired_employees, 排序列数=1, 页码=1
2025-08-15 21:39:58.969 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_header_clicked_impl:8175 | 🆕 [新架构多列排序] 成功处理列3(基本离休费)点击
2025-08-15 21:39:59.030 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7961 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: salary_data_2025_08_retired_employees
2025-08-15 21:40:00.751 | INFO     | src.gui.prototype.widgets.column_sort_manager:handle_header_click:138 | 🆕 [多列排序] 新增列5(生活补贴)排序: 升序
2025-08-15 21:40:00.751 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_state_changed:7425 | 🆕 [新架构多列排序] 排序状态变化: 2 列
2025-08-15 21:40:00.751 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_state_changed:7433 | 🆕 [新架构多列排序] 当前排序: 生活补贴(1): 升序; 基本离休费(2): 升序
2025-08-15 21:40:00.751 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_request:7448 | 🆕 [新架构多列排序] 排序请求: salary_data_2025_08_retired_employees.living_allowance -> ascending
2025-08-15 21:40:00.751 | INFO     | src.gui.prototype.prototype_main_window:_handle_sort_applied_impl:4527 | 🔧 [P0-CRITICAL] 简化排序处理: 列5, living_allowance, ascending
2025-08-15 21:40:00.751 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:4202 | 🔧 [排序调试] 准备发布排序请求: salary_data_2025_08_retired_employees, [{'column_name': 'living_allowance', 'order': 'ascending', 'priority': 0}]
2025-08-15 21:40:00.751 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:4239 | [数据流追踪] 开始处理排序请求: 表=salary_data_2025_08_retired_employees, 排序列数=1, 页码=1
2025-08-15 21:40:00.751 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:4285 | 🔧 [排序] 使用实际页码: 1 (传入页码: 1)
2025-08-15 21:40:00.767 | INFO     | src.services.table_data_service:_handle_sort_request:154 | [排序调试] 接收到排序请求
2025-08-15 21:40:00.767 | INFO     | src.core.unified_state_manager:update_table_state:233 | 表状态已更新: salary_data_2025_08_retired_employees, 变更类型: StateChangeType.SORT_CHANGED
2025-08-15 21:40:00.767 | INFO     | src.core.unified_data_request_manager:request_table_data:212 | 开始处理数据请求
2025-08-15 21:40:00.767 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:994 | [FIX] [排序修复] 列 'living_allowance' 存在于表 'salary_data_2025_08_retired_employees' 中（类型: REAL）
2025-08-15 21:40:00.767 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:994 | [FIX] [排序修复] 列 'living_allowance' 存在于表 'salary_data_2025_08_retired_employees' 中（类型: REAL）
2025-08-15 21:40:00.767 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:805 | 正在从表 salary_data_2025_08_retired_employees 分页获取数据（支持排序）: 第1页, 每页50条, 排序=1列
2025-08-15 21:40:00.767 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:994 | [FIX] [排序修复] 列 'living_allowance' 存在于表 'salary_data_2025_08_retired_employees' 中（类型: REAL）
2025-08-15 21:40:00.767 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:921 | 成功从表 salary_data_2025_08_retired_employees 获取第1页数据（含排序）: 2 行，总计2行
2025-08-15 21:40:00.767 | INFO     | src.core.unified_data_request_manager:request_table_data:262 | 数据请求处理完成: 字段=21, 行数=2, 耗时=0.0ms
2025-08-15 21:40:00.767 | INFO     | src.services.table_data_service:_handle_sort_request:205 | [修复排序] 排序操作成功，发布数据更新事件: salary_data_2025_08_retired_employees, 2行
2025-08-15 21:40:00.767 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3785 | 📥[data_event] received | table=salary_data_2025_08_retired_employees | request_id=None
2025-08-15 21:40:00.767 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3817 | 数据内容: 2行 x 21列
2025-08-15 21:40:00.767 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3840 | 🔧 [关键修复] 接收到sort_change操作的数据更新事件，开始UI更新: salary_data_2025_08_retired_employees, 2行
2025-08-15 21:40:00.767 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7327 | 表 salary_data_2025_08_retired_employees 没有用户偏好设置，显示所有可见字段
2025-08-15 21:40:00.782 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3869 | 🚀 [排序优化] 检测到排序操作，标记为高优先级同步更新
2025-08-15 21:40:00.782 | INFO     | src.gui.prototype.prototype_main_window:set_data:873 | 通过公共接口设置新的表格数据(DataFrame): 2 行
2025-08-15 21:40:00.782 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7327 | 表 salary_data_2025_08_retired_employees 没有用户偏好设置，显示所有可见字段
2025-08-15 21:40:00.798 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 2行，表名: salary_data_2025_08_retired_employees
2025-08-15 21:40:00.798 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=19339009.0, 薪资=N/A
2025-08-15 21:40:00.798 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=19289006.0, 薪资=N/A
2025-08-15 21:40:00.798 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:5271 | 最大可见行数已更新: 10 -> 10
2025-08-15 21:40:00.798 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5324 | 根据数据量(2)自动调整最大可见行数为: 10
2025-08-15 21:40:00.798 | INFO     | src.modules.format_management.format_renderer:render_dataframe:107 | 🎯 [格式渲染] 隐藏字段配置: ['id', 'updated_at', 'created_at', 'sequence_number']
2025-08-15 21:40:00.798 | INFO     | src.modules.format_management.format_renderer:render_dataframe:115 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-08-15 21:40:00.814 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 增发一次性生活补贴 -> 英文名: one_time_living_allowance -> 类型: float
2025-08-15 21:40:00.814 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-08-15 21:40:00.814 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-08-15 21:40:00.814 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 备注 -> 英文名: remarks -> 类型: string
2025-08-15 21:40:00.814 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 月份 -> 英文名: month -> 类型: month_string_extract_last_two
2025-08-15 21:40:00.814 | INFO     | src.modules.format_management.field_registry:get_display_fields:1554 | 🔧 [P0-2修复] display_order转换完成: 17个字段，原始字段数: 17
2025-08-15 21:40:00.814 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | 🔧 [DEBUG] table_type=retired_employees, display_fields=17个字段
2025-08-15 21:40:00.830 | INFO     | src.modules.format_management.format_renderer:render_dataframe:211 | 🔧 [P0修复] 配置字段匹配完成: 成功匹配 17/17 个字段
2025-08-15 21:40:00.830 | INFO     | src.modules.format_management.format_renderer:render_dataframe:224 | 🔧 [P0修复] 字段重排完成: 原始17个 -> 最终17个字段
2025-08-15 21:40:00.830 | INFO     | src.modules.format_management.format_renderer:render_dataframe:240 | 🎨 [格式渲染] DataFrame格式化完成: retired_employees, 行数: 2, 列数: 17
2025-08-15 21:40:00.830 | INFO     | src.modules.format_management.unified_format_manager:format_data:367 | 🎯 [统一格式管理] 数据格式化完成: retired_employees, 行数: 2, 列数: 17
2025-08-15 21:40:00.830 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 2行数据
2025-08-15 21:40:00.830 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:52 | [数据流追踪] 开始小数据集渲染: 2行 x 17列
2025-08-15 21:40:00.830 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:112 | [数据流追踪] 小数据集渲染完成: 耗时0.0ms, 平均每行0.00ms
2025-08-15 21:40:00.830 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=2, 渲染时间=0.0ms, 策略=small_dataset
2025-08-15 21:40:00.830 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:279 | [数据流追踪] 选择小数据集渲染策略
2025-08-15 21:40:00.845 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19339009
2025-08-15 21:40:00.845 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 19289006
2025-08-15 21:40:00.845 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 2
2025-08-15 21:40:00.845 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[0]: 工号=19339009, 薪资=N/A
2025-08-15 21:40:00.845 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[1]: 工号=19289006, 薪资=N/A
2025-08-15 21:40:00.845 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:479 | 可见行数据顺序: ['19339009', '19289006']
2025-08-15 21:40:00.845 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:442 | 表格数据已设置: 2 行, 17 列
2025-08-15 21:40:00.853 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 2 行, 耗时: 54.6ms
2025-08-15 21:40:00.869 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7926 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-15 21:40:00.870 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7936 | 🔧 [P0-排序修复] 恢复排序指示器成功: 2 列排序
2025-08-15 21:40:00.872 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7937 | 🔧 [P0-排序修复] 排序描述: 生活补贴(1): 升序; 基本离休费(2): 升序
2025-08-15 21:40:00.874 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1723 | 🆕 [列宽保存] 未找到表格 salary_data_2025_08_retired_employees 的列宽配置
2025-08-15 21:40:00.874 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-15 21:40:00.875 | INFO     | src.gui.prototype.prototype_main_window:set_data:997 | 📊[pagination-write] 跳过写入当前页数据量(total=2)，保持 total=2，等待数据事件纠正
2025-08-15 21:40:00.876 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3887 | 🔧 [P1-1修复] 数据已成功设置到UI，使用新映射表头: 2行, 17列
2025-08-15 21:40:00.876 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 17 个, 行号起始 1, 共 2 行
2025-08-15 21:40:00.877 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3903 | 🔧 [P0-新5修复] 表头强制刷新完成: salary_data_2025_08_retired_employees, 传递参数: 17个表头
2025-08-15 21:40:00.878 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3907 | 🔧 [统一数据设置] 数据已成功设置到UI: 2行, 17列
2025-08-15 21:40:00.885 | INFO     | src.gui.widgets.pagination_widget:batch_update:472 | 📊[pagination-write] batch_update | src=_on_new_data_updated/sort_change | rid=None | total: 2->2, page: 1->1, size: 50->50, pages: 1->1
2025-08-15 21:40:00.885 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3941 | ✅[pagination-state] now | page=1 / pages=1 / total=2 | rid=None
2025-08-15 21:40:00.886 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3954 | 🔍 [FINAL修复] 最终调用set_pagination_state: 第1页, 记录1-2 | rid=None
2025-08-15 21:40:00.887 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4166 | 🔍 [表格调试] ================== set_pagination_state 开始 ==================
2025-08-15 21:40:00.888 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4167 | 🔍 [表格调试] 收到分页状态参数: {'current_page': 1, 'page_size': 50, 'total_records': 2, 'start_record': 1, 'end_record': 2}
2025-08-15 21:40:00.889 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4168 | 🔍 [表格调试] 当前表格行数: 2
2025-08-15 21:40:00.890 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4169 | 🔍 [表格调试] 当前表格列数: 17
2025-08-15 21:40:00.891 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4171 | 🔧 [分页行号修复] 接收分页状态: 第1页, 记录1-2
2025-08-15 21:40:00.894 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1723 | 🆕 [列宽保存] 未找到表格 salary_data_2025_08_retired_employees 的列宽配置
2025-08-15 21:40:00.902 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4198 | 🔧 [P0-3修复] 分页时已恢复列宽设置: salary_data_2025_08_retired_employees
2025-08-15 21:40:00.903 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4206 | 🔧 [分页行号修复] 表格状态: 当前行数=2, 期望行数=2
2025-08-15 21:40:00.904 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 17 个, 行号起始 1, 共 2 行
2025-08-15 21:40:00.904 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_force_update_pagination_row_numbers:4286 | 🔧 [分页行号修复] 强制更新完成: 起始记录1, 共2行
2025-08-15 21:40:00.905 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3958 | 🔍 [FINAL修复] set_pagination_state最终调用完成
2025-08-15 21:40:00.907 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3972 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-08-15 21:40:00.908 | INFO     | src.services.table_data_service:_handle_sort_request:225 | [修复排序] 数据更新事件已发布
2025-08-15 21:40:00.909 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:4324 | [数据流追踪] 排序事件已发布: 表=salary_data_2025_08_retired_employees, 排序列数=1, 页码=1
2025-08-15 21:40:00.910 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_header_clicked_impl:8175 | 🆕 [新架构多列排序] 成功处理列5(生活补贴)点击
2025-08-15 21:40:00.974 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7961 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: salary_data_2025_08_retired_employees
2025-08-15 21:40:01.599 | INFO     | src.gui.prototype.widgets.column_sort_manager:handle_header_click:133 | 🆕 [多列排序] 更新列5(生活补贴)排序: descending
2025-08-15 21:40:01.599 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_state_changed:7425 | 🆕 [新架构多列排序] 排序状态变化: 2 列
2025-08-15 21:40:01.599 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_state_changed:7433 | 🆕 [新架构多列排序] 当前排序: 生活补贴(1): 降序; 基本离休费(2): 升序
2025-08-15 21:40:01.599 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_request:7448 | 🆕 [新架构多列排序] 排序请求: salary_data_2025_08_retired_employees.living_allowance -> descending
2025-08-15 21:40:01.599 | INFO     | src.gui.prototype.prototype_main_window:_handle_sort_applied_impl:4527 | 🔧 [P0-CRITICAL] 简化排序处理: 列5, living_allowance, descending
2025-08-15 21:40:01.599 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:4202 | 🔧 [排序调试] 准备发布排序请求: salary_data_2025_08_retired_employees, [{'column_name': 'living_allowance', 'order': 'descending', 'priority': 0}]
2025-08-15 21:40:01.599 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:4239 | [数据流追踪] 开始处理排序请求: 表=salary_data_2025_08_retired_employees, 排序列数=1, 页码=1
2025-08-15 21:40:01.599 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:4285 | 🔧 [排序] 使用实际页码: 1 (传入页码: 1)
2025-08-15 21:40:01.614 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:994 | [FIX] [排序修复] 列 'living_allowance' 存在于表 'salary_data_2025_08_retired_employees' 中（类型: REAL）
2025-08-15 21:40:01.614 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:994 | [FIX] [排序修复] 列 'living_allowance' 存在于表 'salary_data_2025_08_retired_employees' 中（类型: REAL）
2025-08-15 21:40:01.614 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:805 | 正在从表 salary_data_2025_08_retired_employees 分页获取数据（支持排序）: 第1页, 每页50条, 排序=1列
2025-08-15 21:40:01.614 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:994 | [FIX] [排序修复] 列 'living_allowance' 存在于表 'salary_data_2025_08_retired_employees' 中（类型: REAL）
2025-08-15 21:40:01.614 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:921 | 成功从表 salary_data_2025_08_retired_employees 获取第1页数据（含排序）: 2 行，总计2行
2025-08-15 21:40:01.614 | INFO     | src.services.table_data_service:_handle_sort_request:205 | [修复排序] 排序操作成功，发布数据更新事件: salary_data_2025_08_retired_employees, 2行
2025-08-15 21:40:01.614 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3785 | 📥[data_event] received | table=salary_data_2025_08_retired_employees | request_id=None
2025-08-15 21:40:01.614 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3817 | 数据内容: 2行 x 21列
2025-08-15 21:40:01.614 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3840 | 🔧 [关键修复] 接收到sort_change操作的数据更新事件，开始UI更新: salary_data_2025_08_retired_employees, 2行
2025-08-15 21:40:01.614 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7327 | 表 salary_data_2025_08_retired_employees 没有用户偏好设置，显示所有可见字段
2025-08-15 21:40:01.614 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3869 | 🚀 [排序优化] 检测到排序操作，标记为高优先级同步更新
2025-08-15 21:40:01.614 | INFO     | src.gui.prototype.prototype_main_window:set_data:873 | 通过公共接口设置新的表格数据(DataFrame): 2 行
2025-08-15 21:40:01.630 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7327 | 表 salary_data_2025_08_retired_employees 没有用户偏好设置，显示所有可见字段
2025-08-15 21:40:01.647 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 2行，表名: salary_data_2025_08_retired_employees
2025-08-15 21:40:01.647 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=19289006.0, 薪资=N/A
2025-08-15 21:40:01.647 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=19339009.0, 薪资=N/A
2025-08-15 21:40:01.647 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:5271 | 最大可见行数已更新: 10 -> 10
2025-08-15 21:40:01.647 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5324 | 根据数据量(2)自动调整最大可见行数为: 10
2025-08-15 21:40:01.647 | INFO     | src.modules.format_management.format_renderer:render_dataframe:107 | 🎯 [格式渲染] 隐藏字段配置: ['id', 'updated_at', 'created_at', 'sequence_number']
2025-08-15 21:40:01.647 | INFO     | src.modules.format_management.format_renderer:render_dataframe:115 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-08-15 21:40:01.647 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 增发一次性生活补贴 -> 英文名: one_time_living_allowance -> 类型: float
2025-08-15 21:40:01.661 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-08-15 21:40:01.661 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-08-15 21:40:01.661 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 备注 -> 英文名: remarks -> 类型: string
2025-08-15 21:40:01.661 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 月份 -> 英文名: month -> 类型: month_string_extract_last_two
2025-08-15 21:40:01.661 | INFO     | src.modules.format_management.field_registry:get_display_fields:1554 | 🔧 [P0-2修复] display_order转换完成: 17个字段，原始字段数: 17
2025-08-15 21:40:01.661 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | 🔧 [DEBUG] table_type=retired_employees, display_fields=17个字段
2025-08-15 21:40:01.661 | INFO     | src.modules.format_management.format_renderer:render_dataframe:211 | 🔧 [P0修复] 配置字段匹配完成: 成功匹配 17/17 个字段
2025-08-15 21:40:01.661 | INFO     | src.modules.format_management.format_renderer:render_dataframe:224 | 🔧 [P0修复] 字段重排完成: 原始17个 -> 最终17个字段
2025-08-15 21:40:01.661 | INFO     | src.modules.format_management.format_renderer:render_dataframe:240 | 🎨 [格式渲染] DataFrame格式化完成: retired_employees, 行数: 2, 列数: 17
2025-08-15 21:40:01.661 | INFO     | src.modules.format_management.unified_format_manager:format_data:367 | 🎯 [统一格式管理] 数据格式化完成: retired_employees, 行数: 2, 列数: 17
2025-08-15 21:40:01.678 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 2行数据
2025-08-15 21:40:01.678 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:52 | [数据流追踪] 开始小数据集渲染: 2行 x 17列
2025-08-15 21:40:01.693 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:112 | [数据流追踪] 小数据集渲染完成: 耗时14.2ms, 平均每行7.11ms
2025-08-15 21:40:01.693 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=2, 渲染时间=14.2ms, 策略=small_dataset
2025-08-15 21:40:01.693 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:279 | [数据流追踪] 选择小数据集渲染策略
2025-08-15 21:40:01.693 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19289006
2025-08-15 21:40:01.693 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 19339009
2025-08-15 21:40:01.693 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 2
2025-08-15 21:40:01.693 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[0]: 工号=19289006, 薪资=N/A
2025-08-15 21:40:01.693 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[1]: 工号=19339009, 薪资=N/A
2025-08-15 21:40:01.693 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:479 | 可见行数据顺序: ['19289006', '19339009']
2025-08-15 21:40:01.700 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 2 行, 耗时: 53.1ms
2025-08-15 21:40:01.700 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7926 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-15 21:40:01.712 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7936 | 🔧 [P0-排序修复] 恢复排序指示器成功: 2 列排序
2025-08-15 21:40:01.713 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7937 | 🔧 [P0-排序修复] 排序描述: 生活补贴(1): 降序; 基本离休费(2): 升序
2025-08-15 21:40:01.714 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1723 | 🆕 [列宽保存] 未找到表格 salary_data_2025_08_retired_employees 的列宽配置
2025-08-15 21:40:01.714 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-15 21:40:01.716 | INFO     | src.gui.prototype.prototype_main_window:set_data:997 | 📊[pagination-write] 跳过写入当前页数据量(total=2)，保持 total=2，等待数据事件纠正
2025-08-15 21:40:01.717 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3887 | 🔧 [P1-1修复] 数据已成功设置到UI，使用新映射表头: 2行, 17列
2025-08-15 21:40:01.717 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 17 个, 行号起始 1, 共 2 行
2025-08-15 21:40:01.718 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3903 | 🔧 [P0-新5修复] 表头强制刷新完成: salary_data_2025_08_retired_employees, 传递参数: 17个表头
2025-08-15 21:40:01.718 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3907 | 🔧 [统一数据设置] 数据已成功设置到UI: 2行, 17列
2025-08-15 21:40:01.719 | INFO     | src.gui.widgets.pagination_widget:batch_update:472 | 📊[pagination-write] batch_update | src=_on_new_data_updated/sort_change | rid=None | total: 2->2, page: 1->1, size: 50->50, pages: 1->1
2025-08-15 21:40:01.719 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3941 | ✅[pagination-state] now | page=1 / pages=1 / total=2 | rid=None
2025-08-15 21:40:01.720 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3954 | 🔍 [FINAL修复] 最终调用set_pagination_state: 第1页, 记录1-2 | rid=None
2025-08-15 21:40:01.720 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4166 | 🔍 [表格调试] ================== set_pagination_state 开始 ==================
2025-08-15 21:40:01.720 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4167 | 🔍 [表格调试] 收到分页状态参数: {'current_page': 1, 'page_size': 50, 'total_records': 2, 'start_record': 1, 'end_record': 2}
2025-08-15 21:40:01.721 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4168 | 🔍 [表格调试] 当前表格行数: 2
2025-08-15 21:40:01.742 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4169 | 🔍 [表格调试] 当前表格列数: 17
2025-08-15 21:40:01.743 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4171 | 🔧 [分页行号修复] 接收分页状态: 第1页, 记录1-2
2025-08-15 21:40:01.744 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1723 | 🆕 [列宽保存] 未找到表格 salary_data_2025_08_retired_employees 的列宽配置
2025-08-15 21:40:01.744 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4198 | 🔧 [P0-3修复] 分页时已恢复列宽设置: salary_data_2025_08_retired_employees
2025-08-15 21:40:01.745 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4206 | 🔧 [分页行号修复] 表格状态: 当前行数=2, 期望行数=2
2025-08-15 21:40:01.745 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 17 个, 行号起始 1, 共 2 行
2025-08-15 21:40:01.746 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_force_update_pagination_row_numbers:4286 | 🔧 [分页行号修复] 强制更新完成: 起始记录1, 共2行
2025-08-15 21:40:01.746 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3958 | 🔍 [FINAL修复] set_pagination_state最终调用完成
2025-08-15 21:40:01.747 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3972 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-08-15 21:40:01.747 | INFO     | src.services.table_data_service:_handle_sort_request:225 | [修复排序] 数据更新事件已发布
2025-08-15 21:40:01.748 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:4324 | [数据流追踪] 排序事件已发布: 表=salary_data_2025_08_retired_employees, 排序列数=1, 页码=1
2025-08-15 21:40:01.749 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_header_clicked_impl:8175 | 🆕 [新架构多列排序] 成功处理列5(生活补贴)点击
2025-08-15 21:40:01.815 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7961 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: salary_data_2025_08_retired_employees
2025-08-15 21:40:07.827 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2095 | MainWorkspaceArea 响应式适配: sm
2025-08-15 21:40:09.609 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 08月 > 全部在职人员', '异动人员表']
2025-08-15 21:40:09.624 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7370 | 导航变化: 异动人员表
2025-08-15 21:40:09.624 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10489 | 🚫 [用户要求] 表格切换不显示加载条: salary_data_2025_08_retired_employees -> None
2025-08-15 21:40:09.624 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10505 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-08-15 21:40:09.624 | INFO     | src.gui.prototype.prototype_main_window:clear_field_processing_cache:7245 | 🔧 [缓存清理] 清理了所有 0 个字段处理缓存条目
2025-08-15 21:40:09.624 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8170 | 🔧 [表名生成] 处理异动表路径: ['异动人员表']
2025-08-15 21:40:09.624 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8216 | 🔧 [P1修复] 异动表路径层次较少，使用当前年月: change_data_2025_08_active_employees
2025-08-15 21:40:09.624 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8218 | 🔧 [表名生成] 生成异动表名: change_data_2025_08_active_employees
2025-08-15 21:40:09.624 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-15 21:40:09.624 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:362 | 已清理表 change_data_2025_08_active_employees 的缓存
2025-08-15 21:40:09.624 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:11175 | 已注册 2 个表格到表头管理器
2025-08-15 21:40:09.624 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1188 | 🔧 [P1-3] 开始智能表头重影检测，共 2 个表格
2025-08-15 21:40:09.624 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1261 | 🔧 [P1-3] 智能表头重影检测完成，耗时 0.00ms
2025-08-15 21:40:09.624 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1262 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-08-15 21:40:09.624 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7586 | 🆕 使用新架构加载数据: change_data_2025_08_active_employees（通过事件系统）
2025-08-15 21:40:09.624 | INFO     | src.services.table_data_service:load_table_data:366 | 加载表格数据: 页码: 1
2025-08-15 21:40:09.624 | INFO     | src.core.unified_data_request_manager:request_table_data:212 | 开始处理数据请求
2025-08-15 21:40:09.624 | ERROR    | src.core.unified_data_request_manager:_find_similar_table_names_by_type:385 | 🔧 [P0修复] 按类型查找相似表名失败: 'DynamicTableManager' object has no attribute 'get_all_table_names'
2025-08-15 21:40:09.624 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7595 | 🔧 [数据流追踪] 数据表 change_data_2025_08_active_employees 尚未创建，显示空表格等待数据导入
2025-08-15 21:40:09.624 | INFO     | src.gui.prototype.prototype_main_window:_get_table_headers_by_type:8411 | 🔧 [数据流追踪] 使用在职人员表头: 22个字段
2025-08-15 21:40:09.624 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8494 | 🔧 [数据流追踪] 使用表 change_data_2025_08_active_employees 的专用表头: 22个字段
2025-08-15 21:40:09.624 | INFO     | src.gui.prototype.prototype_main_window:_get_default_table_name:2322 | 🔧 [兼容性修复] 暂无数据表，返回占位符（等待数据导入）
2025-08-15 21:40:09.624 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 0行，表名: default_table
2025-08-15 21:40:09.624 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_validate_and_fix_data_consistency:4487 | 🔧 [数据修复] 数据或表头为空，跳过一致性验证
2025-08-15 21:40:09.624 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5300 | 数据量为0，不调整最大可见行数，保持当前值: 10
2025-08-15 21:40:09.624 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2825 | 🔧 [P0修复] 检测到空表且表头为中文，跳过字段映射: 22个表头
2025-08-15 21:40:09.624 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2904 | 表格格式化完成: default_table, 类型: active_employees
2025-08-15 21:40:09.640 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-08-15 21:40:09.640 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:256 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-08-15 21:40:09.658 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:264 | 🔧 [P0-修复] 空数据集设置表头: 22个
2025-08-15 21:40:09.658 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-08-15 21:40:09.658 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:442 | 表格数据已设置: 0 行, 22 列
2025-08-15 21:40:09.660 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:417 | 🔧 [新架构] 排序管理器切换到表格: default_table
2025-08-15 21:40:09.663 | INFO     | src.gui.prototype.widgets.column_sort_manager:_load_field_mappings_from_config:344 | 🔧 [新架构] 成功加载 46 个字段映射
2025-08-15 21:40:09.663 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_state_changed:7425 | 🆕 [新架构多列排序] 排序状态变化: 0 列
2025-08-15 21:40:09.664 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_state_changed:7433 | 🆕 [新架构多列排序] 当前排序: 无排序
2025-08-15 21:40:09.664 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_cleared:7470 | 🆕 [新架构多列排序] 排序已清除
2025-08-15 21:40:09.665 | INFO     | src.gui.prototype.prototype_main_window:_handle_sort_cleared:4554 | 🆕 [新架构排序] 处理排序清除
2025-08-15 21:40:09.665 | INFO     | src.gui.prototype.prototype_main_window:_handle_sort_cleared:4565 | 排序状态已从管理器中清除
2025-08-15 21:40:09.666 | INFO     | src.gui.prototype.prototype_main_window:_handle_sort_cleared:4571 | 清除排序，当前页码: 1
2025-08-15 21:40:09.679 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:4202 | 🔧 [排序调试] 准备发布排序请求: change_data_2025_08_active_employees, []
2025-08-15 21:40:09.680 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:4213 | 🔧 [P3修复] 排序列为空，尝试从状态管理器恢复
2025-08-15 21:40:09.680 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:4239 | [数据流追踪] 开始处理排序请求: 表=change_data_2025_08_active_employees, 排序列数=0, 页码=1
2025-08-15 21:40:09.681 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:4285 | 🔧 [排序] 使用实际页码: 1 (传入页码: 1)
2025-08-15 21:40:09.681 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:4311 | 🔧 [P1-修复] 转换后排序列为空，将发布清空排序事件
2025-08-15 21:40:09.682 | INFO     | src.services.table_data_service:_handle_sort_request:154 | [排序调试] 接收到排序请求
2025-08-15 21:40:09.682 | INFO     | src.services.table_data_service:_handle_sort_request:166 | [排序调试] 收到清空排序请求，将清理排序状态
2025-08-15 21:40:09.684 | INFO     | src.core.unified_state_manager:update_table_state:233 | 表状态已更新: change_data_2025_08_active_employees, 变更类型: StateChangeType.SORT_CHANGED
2025-08-15 21:40:09.685 | ERROR    | src.core.unified_data_request_manager:_find_similar_table_names_by_type:385 | 🔧 [P0修复] 按类型查找相似表名失败: 'DynamicTableManager' object has no attribute 'get_all_table_names'
2025-08-15 21:40:09.690 | ERROR    | src.services.table_data_service:_handle_sort_request:227 | [排序调试] 排序请求处理失败: 请求参数验证失败
2025-08-15 21:40:09.692 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:4324 | [数据流追踪] 排序事件已发布: 表=change_data_2025_08_active_employees, 排序列数=0, 页码=1
2025-08-15 21:40:09.693 | INFO     | src.gui.prototype.widgets.column_sort_manager:clear_all_sorts:268 | 🆕 [多列排序] 已清除所有排序
2025-08-15 21:40:09.693 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 0 行, 耗时: 36.2ms
2025-08-15 21:40:09.694 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7926 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-15 21:40:09.694 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7939 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-15 21:40:09.697 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: default_table (22/22 列)
2025-08-15 21:40:09.697 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-15 21:40:09.697 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2346 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个，所有表头为中文
2025-08-15 21:40:09.699 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-15 21:40:09.713 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:8516 | 已显示标准空表格，表头数量: 22
2025-08-15 21:40:09.715 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1748 | 列宽已恢复: default_table (22/22 列)
2025-08-15 21:40:09.716 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_adjust_column_widths_delayed_with_restore:4730 | 🔧 [P0-3修复] 用户列宽设置已恢复: default_table
2025-08-15 21:40:09.717 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_adjust_column_widths_delayed_with_restore:4752 | 🔧 [列宽修复] 使用用户保存的列宽，跳过默认调整
2025-08-15 21:40:09.798 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7961 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: default_table
2025-08-15 21:40:14.288 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data:654 | 数据导入功能被触发，发出 import_requested 信号。
2025-08-15 21:40:14.288 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:6031 | 接收到数据导入请求，推断的目标路径: 异动人员表。打开导入对话框。
2025-08-15 21:40:14.288 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-08-15 21:40:14.288 | INFO     | src.modules.data_import.multi_sheet_importer:__init__:75 | 多Sheet导入器初始化完成
2025-08-15 21:40:14.288 | INFO     | src.gui.widgets.target_selection_widget:_load_available_options:250 | 成功加载导航配置
2025-08-15 21:40:14.303 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1252 | 🔧 [深度修复] 找到 4 个匹配类型 'salary_data' 的表 (尝试 1/5)
2025-08-15 21:40:14.350 | INFO     | src.gui.main_dialogs:_get_template_fields:1880 | 🔧 [P3-修复] ConfigSyncManager未初始化，使用默认字段模板
2025-08-15 21:40:14.350 | INFO     | src.gui.main_dialogs:_init_field_mapping:1867 | 🔧 [P3-修复] 未找到字段模板，使用内置默认字段列表
2025-08-15 21:40:14.397 | INFO     | src.modules.data_import.import_defaults_manager:load_settings:86 | 未找到用户设置文件，使用默认设置
2025-08-15 21:40:14.397 | INFO     | src.modules.data_import.import_defaults_manager:get_smart_defaults_for_category:240 | 检测到异动表导入，设置table_template为salary_changes
2025-08-15 21:40:14.397 | INFO     | src.gui.main_dialogs:_apply_default_settings:2232 | 已应用默认设置: {'start_row': 1, 'import_mode': 'multi_sheet', 'auto_match_sheet': True, 'include_header': True, 'skip_empty_rows': True, 'create_table_mode': 'sheet_name', 'import_strategy': 'separate_tables', 'table_template': 'salary_changes'}
2025-08-15 21:40:14.397 | INFO     | src.gui.main_dialogs:_setup_tooltips:2487 | 工具提示设置完成
2025-08-15 21:40:14.397 | INFO     | src.gui.main_dialogs:_setup_shortcuts:2526 | 快捷键设置完成
2025-08-15 21:40:14.397 | INFO     | src.gui.main_dialogs:__init__:78 | 数据导入对话框初始化完成。
2025-08-15 21:40:14.397 | INFO     | src.modules.data_import.multi_sheet_importer:set_config_sync_manager:84 | 🔧 [修复] ConfigSyncManager已设置到MultiSheetImporter
2025-08-15 21:40:14.397 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:6042 | 🔧 [P0-修复] ConfigSyncManager已设置到数据导入对话框
2025-08-15 21:40:16.897 | INFO     | src.gui.main_dialogs:_on_target_changed:2157 | 目标位置已更新: 异动人员表 > 2025年 > 8月 > 全部在职人员
2025-08-15 21:40:19.522 | INFO     | src.gui.main_dialogs:_on_target_changed:2157 | 目标位置已更新: 异动人员表 > 2025年 > 12月 > 全部在职人员
2025-08-15 21:40:19.522 | INFO     | src.gui.main_dialogs:_on_target_changed:2176 | 注意：数据期间(2025-08)与目标位置月份(2025-12)不同
2025-08-15 21:40:26.428 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:178 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-08-15 21:40:28.032 | INFO     | src.modules.data_import.smart_sheet_matcher:record_user_choice:372 | 记录用户选择: 全部在职人员 -> 全部在职人员工资表
2025-08-15 21:40:28.034 | INFO     | src.gui.main_dialogs:_auto_select_sheet_by_category:2267 | 根据人员类别 '全部在职人员' 自动选择工作表: 全部在职人员工资表 (匹配类型: fuzzy, 得分: 0.60)
2025-08-15 21:40:47.688 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:178 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-08-15 21:40:47.875 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-08-15 21:40:47.892 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:216 | 开始导入多Sheet Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-08-15 21:40:48.079 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:233 | 检测到 4 个工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-08-15 21:40:48.094 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-08-15 21:40:48.094 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-15 21:40:48.188 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 16列 (列过滤: 否)
2025-08-15 21:40:48.188 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-15 21:40:48.188 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 16 列 (原始 16 列)
2025-08-15 21:40:48.188 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 3行 x 16列
2025-08-15 21:40:48.188 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:599 | 数据质量检查: 已过滤1条姓名为空的记录
2025-08-15 21:40:48.188 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:642 | 数据过滤完成: 原始3条记录，过滤1条无效记录，有效记录2条
2025-08-15 21:40:48.188 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 2行 × 16列
2025-08-15 21:40:48.188 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:530 | 工作表 离休人员工资表 使用智能默认配置
2025-08-15 21:40:48.188 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:818 | 为Sheet '离休人员工资表' 生成智能默认配置: 1 个必需字段
2025-08-15 21:40:48.188 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:141 | 为模板 retired_employees 生成了 21 个字段映射
2025-08-15 21:40:48.188 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:547 | 使用专用模板 retired_employees 生成字段映射: 21 个字段
2025-08-15 21:40:48.203 | INFO     | src.modules.format_management.unified_format_manager:_on_config_updated:943 | 🚀 [配置同步] 收到配置更新事件: 表=unknown, 类型=unknown
2025-08-15 21:40:48.203 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:737 | 🔧 [自动修复] 为表 salary_data_2025_12_retired_employees 自动生成字段类型配置
2025-08-15 21:40:48.203 | INFO     | src.modules.format_management.field_registry:_find_matching_default_config:921 | 🔧 [智能匹配] 表 salary_data_2025_12_retired_employees 匹配成功: retired_employees (得分:220, 原因:精确后缀匹配:retired_employees,英文关键词:retired,模式匹配:.*retired.*employee.*,分词匹配:retired)
2025-08-15 21:40:48.203 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:755 | 🔧 [自动修复] 使用匹配的默认配置: salary_data_2025_12_retired_employees
2025-08-15 21:40:48.203 | WARNING  | src.modules.format_management.field_registry:_merge_default_mappings:778 | 🔧 [架构优化] 配置一致性警告: ["表 salary_data_2025_08_active_employees 有字段缺少类型定义: {'sequence_number'}", "表 salary_data_2025_08_active_employees 有多余的字段类型定义: {'sequence', 'row_number'}", '🔧 [P3修复] 发现6个字段类型不一致问题，建议检查数值字段的类型定义', '🔧 [P3修复] 示例: 表 part_time_employees 字段 total_salary (currency -> float)', '🔧 [P3修复] 示例: 表 contract_employees 字段 base_salary (currency -> float)', '🔧 [P3修复] 示例: 表 contract_employees 字段 performance_bonus (currency -> float)', '🔧 [P3修复] 2个表的existing_display_fields为空: salary_data_2025_07_active_employees, salary_data_2025_12_retired_employees']
2025-08-15 21:40:48.203 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2060 | 🔧 [P1-1修复] 表 salary_data_2025_12_retired_employees existing_display_fields已自动修复: ['employee_id', 'employee_name', 'department', 'basic_retirement_salary', 'balance_allowance']...
2025-08-15 21:40:48.219 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2070 | 🔧 [架构优化] 应用智能修复策略，执行 1 项修复操作
2025-08-15 21:40:48.219 | INFO     | src.modules.format_management.field_registry:save_mappings:662 | 🏷️ [字段注册] 字段映射保存成功
2025-08-15 21:40:48.219 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:786 | 🔧 [架构优化] 配置修复已持久化保存，变更数量: 1
2025-08-15 21:40:48.219 | INFO     | src.modules.format_management.field_registry:load_mappings:629 | 🏷️ [字段注册] 字段映射加载成功
2025-08-15 21:40:48.219 | INFO     | src.modules.format_management.format_renderer:__init__:83 | 🎨 [格式渲染] 格式渲染器初始化完成
2025-08-15 21:40:48.219 | INFO     | src.core.unified_state_manager:update_global_state:350 | 全局状态已更新: StateChangeType.DATA_RELOADED
2025-08-15 21:40:48.219 | INFO     | src.modules.format_management.unified_format_manager:_on_config_updated:960 | 🚀 [配置同步] 配置重新加载完成: unknown, 加载状态: True -> True
2025-08-15 21:40:48.219 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:665 | 🚀 [配置同步] 已发布配置变更事件: salary_data_2025_12_retired_employees
2025-08-15 21:40:48.219 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:669 | 完整字段映射保存成功: salary_data_2025_12_retired_employees
2025-08-15 21:40:48.219 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:594 | 为表 salary_data_2025_12_retired_employees 生成标准化字段映射: 21 个字段
2025-08-15 21:40:48.219 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:615 | Sheet 离休人员工资表 数据处理完成: 2 行
2025-08-15 21:40:48.235 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:406 | 🔧 [P0修复] 用户选择异动表路径: 异动人员表 > 2025年 > 12月 > 全部在职人员
2025-08-15 21:40:48.235 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:422 | 🔧 [P0修复] 异动表使用灵活模板: change_data
2025-08-15 21:40:48.235 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:436 | 🔧 [P0修复] 异动表名: change_data_2025_12_retired_employees
2025-08-15 21:40:48.235 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:525 | 🔧 [P0修复] 开始创建灵活异动表: change_data_2025_12_retired_employees
2025-08-15 21:40:48.235 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:526 | 🔧 [P0修复] 用户Excel列数: 18
2025-08-15 21:40:48.235 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:527 | 🔧 [P0修复] 用户Excel列名: ['序号', '人员代码', '姓名', '部门名称', '基本\n离休费', '结余\n津贴', '生活\n补贴', '住房\n补贴', '物业\n补贴', '离休\n补贴', '护理费', '增发一次\n性生活补贴', '补发', '合计', '借支', '备注', 'data_source', 'import_time']
2025-08-15 21:40:48.235 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 序号
2025-08-15 21:40:48.235 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 人员代码
2025-08-15 21:40:48.235 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 姓名
2025-08-15 21:40:48.235 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 部门名称
2025-08-15 21:40:48.235 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 基本
离休费
2025-08-15 21:40:48.235 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 结余
津贴
2025-08-15 21:40:48.235 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 生活
补贴
2025-08-15 21:40:48.235 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 住房
补贴
2025-08-15 21:40:48.251 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 物业
补贴
2025-08-15 21:40:48.251 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 离休
补贴
2025-08-15 21:40:48.251 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 护理费
2025-08-15 21:40:48.251 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 增发一次
性生活补贴
2025-08-15 21:40:48.251 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 补发
2025-08-15 21:40:48.251 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 合计
2025-08-15 21:40:48.251 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 借支
2025-08-15 21:40:48.251 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 备注
2025-08-15 21:40:48.251 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: data_source
2025-08-15 21:40:48.251 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: import_time
2025-08-15 21:40:48.251 | ERROR    | src.modules.data_storage.database_manager:create_table:504 | 🔧 [P0修复] 表创建失败: 'ColumnDefinition' object has no attribute 'data_type'
2025-08-15 21:40:48.251 | ERROR    | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:580 | 🔧 [P0修复] 灵活异动表创建失败: change_data_2025_12_retired_employees
2025-08-15 21:40:48.251 | ERROR    | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:460 | 🔧 [P0修复] 灵活异动表创建失败: change_data_2025_12_retired_employees
2025-08-15 21:40:48.251 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-15 21:40:48.360 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-15 21:40:48.360 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 27 列 (原始 27 列)
2025-08-15 21:40:48.375 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:599 | 数据质量检查: 已过滤1条姓名为空的记录
2025-08-15 21:40:48.375 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:642 | 数据过滤完成: 原始14条记录，过滤1条无效记录，有效记录13条
2025-08-15 21:40:48.375 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:530 | 工作表 退休人员工资表 使用智能默认配置
2025-08-15 21:40:48.375 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:818 | 为Sheet '退休人员工资表' 生成智能默认配置: 1 个必需字段
2025-08-15 21:40:48.375 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:141 | 为模板 pension_employees 生成了 32 个字段映射
2025-08-15 21:40:48.375 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:547 | 使用专用模板 pension_employees 生成字段映射: 32 个字段
2025-08-15 21:40:48.375 | INFO     | src.modules.format_management.unified_format_manager:_on_config_updated:943 | 🚀 [配置同步] 收到配置更新事件: 表=unknown, 类型=unknown
2025-08-15 21:40:48.375 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:737 | 🔧 [自动修复] 为表 salary_data_2025_12_pension_employees 自动生成字段类型配置
2025-08-15 21:40:48.391 | INFO     | src.modules.format_management.field_registry:_find_matching_default_config:921 | 🔧 [智能匹配] 表 salary_data_2025_12_pension_employees 匹配成功: pension_employees (得分:300, 原因:精确后缀匹配:pension_employees,英文关键词:pension,英文关键词:pension_employees,模式匹配:.*pension.*employee.*,模式匹配:.*pension.*,分词匹配:pension)
2025-08-15 21:40:48.391 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:755 | 🔧 [自动修复] 使用匹配的默认配置: salary_data_2025_12_pension_employees
2025-08-15 21:40:48.391 | WARNING  | src.modules.format_management.field_registry:_merge_default_mappings:778 | 🔧 [架构优化] 配置一致性警告: ["表 salary_data_2025_08_active_employees 有字段缺少类型定义: {'sequence_number'}", "表 salary_data_2025_08_active_employees 有多余的字段类型定义: {'sequence', 'row_number'}", '🔧 [P3修复] 发现6个字段类型不一致问题，建议检查数值字段的类型定义', '🔧 [P3修复] 示例: 表 part_time_employees 字段 total_salary (currency -> float)', '🔧 [P3修复] 示例: 表 contract_employees 字段 base_salary (currency -> float)', '🔧 [P3修复] 示例: 表 contract_employees 字段 performance_bonus (currency -> float)', '🔧 [P3修复] 2个表的existing_display_fields为空: salary_data_2025_07_active_employees, salary_data_2025_12_pension_employees']
2025-08-15 21:40:48.391 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2060 | 🔧 [P1-1修复] 表 salary_data_2025_12_pension_employees existing_display_fields已自动修复: ['employee_id', 'employee_name', 'department', 'employee_type_code', 'basic_retirement_salary']...
2025-08-15 21:40:48.407 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2070 | 🔧 [架构优化] 应用智能修复策略，执行 1 项修复操作
2025-08-15 21:40:48.407 | INFO     | src.modules.format_management.field_registry:save_mappings:662 | 🏷️ [字段注册] 字段映射保存成功
2025-08-15 21:40:48.407 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:786 | 🔧 [架构优化] 配置修复已持久化保存，变更数量: 1
2025-08-15 21:40:48.407 | INFO     | src.modules.format_management.field_registry:load_mappings:629 | 🏷️ [字段注册] 字段映射加载成功
2025-08-15 21:40:48.407 | INFO     | src.modules.format_management.format_renderer:__init__:83 | 🎨 [格式渲染] 格式渲染器初始化完成
2025-08-15 21:40:48.407 | INFO     | src.modules.format_management.unified_format_manager:_on_config_updated:960 | 🚀 [配置同步] 配置重新加载完成: unknown, 加载状态: True -> True
2025-08-15 21:40:48.407 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:665 | 🚀 [配置同步] 已发布配置变更事件: salary_data_2025_12_pension_employees
2025-08-15 21:40:48.407 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:669 | 完整字段映射保存成功: salary_data_2025_12_pension_employees
2025-08-15 21:40:48.407 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:594 | 为表 salary_data_2025_12_pension_employees 生成标准化字段映射: 32 个字段
2025-08-15 21:40:48.422 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:615 | Sheet 退休人员工资表 数据处理完成: 13 行
2025-08-15 21:40:48.422 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:406 | 🔧 [P0修复] 用户选择异动表路径: 异动人员表 > 2025年 > 12月 > 全部在职人员
2025-08-15 21:40:48.422 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:422 | 🔧 [P0修复] 异动表使用灵活模板: change_data
2025-08-15 21:40:48.438 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:436 | 🔧 [P0修复] 异动表名: change_data_2025_12_retired_employees
2025-08-15 21:40:48.438 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:525 | 🔧 [P0修复] 开始创建灵活异动表: change_data_2025_12_retired_employees
2025-08-15 21:40:48.438 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:526 | 🔧 [P0修复] 用户Excel列数: 29
2025-08-15 21:40:48.438 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:527 | 🔧 [P0修复] 用户Excel列名: ['序号', '人员代码', '姓名', '部门名称', '人员类别代码', '基本退休费', '津贴', '结余津贴', '离退休生活补贴', '护理费', '物业补贴', '住房补贴', '增资预付', '2016待遇调整', '2017待遇调整', '2018待遇调整', '2019待遇调整', '2020待遇调整', '2021待遇调整', '2022待遇调整', '2023待遇调整', '补发', '借支', '应发工资', '公积', '保险扣款', '备注', 'data_source', 'import_time']
2025-08-15 21:40:48.438 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 序号
2025-08-15 21:40:48.438 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 人员代码
2025-08-15 21:40:48.438 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 姓名
2025-08-15 21:40:48.438 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 部门名称
2025-08-15 21:40:48.438 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 人员类别代码
2025-08-15 21:40:48.438 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 基本退休费
2025-08-15 21:40:48.438 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 津贴
2025-08-15 21:40:48.438 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 结余津贴
2025-08-15 21:40:48.438 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 离退休生活补贴
2025-08-15 21:40:48.438 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 护理费
2025-08-15 21:40:48.438 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 物业补贴
2025-08-15 21:40:48.438 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 住房补贴
2025-08-15 21:40:48.438 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 增资预付
2025-08-15 21:40:48.438 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 2016待遇调整
2025-08-15 21:40:48.438 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 2017待遇调整
2025-08-15 21:40:48.438 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 2018待遇调整
2025-08-15 21:40:48.438 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 2019待遇调整
2025-08-15 21:40:48.438 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 2020待遇调整
2025-08-15 21:40:48.438 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 2021待遇调整
2025-08-15 21:40:48.438 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 2022待遇调整
2025-08-15 21:40:48.438 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 2023待遇调整
2025-08-15 21:40:48.438 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 补发
2025-08-15 21:40:48.438 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 借支
2025-08-15 21:40:48.438 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 应发工资
2025-08-15 21:40:48.438 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 公积
2025-08-15 21:40:48.438 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 保险扣款
2025-08-15 21:40:48.454 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 备注
2025-08-15 21:40:48.469 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: data_source
2025-08-15 21:40:48.469 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: import_time
2025-08-15 21:40:48.469 | ERROR    | src.modules.data_storage.database_manager:create_table:504 | 🔧 [P0修复] 表创建失败: 'ColumnDefinition' object has no attribute 'data_type'
2025-08-15 21:40:48.469 | ERROR    | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:580 | 🔧 [P0修复] 灵活异动表创建失败: change_data_2025_12_retired_employees
2025-08-15 21:40:48.469 | ERROR    | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:460 | 🔧 [P0修复] 灵活异动表创建失败: change_data_2025_12_retired_employees
2025-08-15 21:40:48.469 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-15 21:40:48.610 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-15 21:40:48.610 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 23 列 (原始 23 列)
2025-08-15 21:40:48.610 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:599 | 数据质量检查: 已过滤1条姓名为空的记录
2025-08-15 21:40:48.610 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:642 | 数据过滤完成: 原始1397条记录，过滤1条无效记录，有效记录1396条
2025-08-15 21:40:48.610 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:530 | 工作表 全部在职人员工资表 使用智能默认配置
2025-08-15 21:40:48.610 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:818 | 为Sheet '全部在职人员工资表' 生成智能默认配置: 2 个必需字段
2025-08-15 21:40:48.610 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:141 | 为模板 active_employees 生成了 28 个字段映射
2025-08-15 21:40:48.610 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:547 | 使用专用模板 active_employees 生成字段映射: 28 个字段
2025-08-15 21:40:48.625 | INFO     | src.modules.format_management.unified_format_manager:_on_config_updated:943 | 🚀 [配置同步] 收到配置更新事件: 表=unknown, 类型=unknown
2025-08-15 21:40:48.625 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:737 | 🔧 [自动修复] 为表 salary_data_2025_12_active_employees 自动生成字段类型配置
2025-08-15 21:40:48.625 | INFO     | src.modules.format_management.field_registry:_find_matching_default_config:921 | 🔧 [智能匹配] 表 salary_data_2025_12_active_employees 匹配成功: active_employees (得分:220, 原因:精确后缀匹配:active_employees,英文关键词:active,模式匹配:.*active.*employee.*,分词匹配:active)
2025-08-15 21:40:48.625 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:755 | 🔧 [自动修复] 使用匹配的默认配置: salary_data_2025_12_active_employees
2025-08-15 21:40:48.625 | WARNING  | src.modules.format_management.field_registry:_merge_default_mappings:778 | 🔧 [架构优化] 配置一致性警告: ["表 salary_data_2025_08_active_employees 有字段缺少类型定义: {'sequence_number'}", "表 salary_data_2025_08_active_employees 有多余的字段类型定义: {'sequence', 'row_number'}", "表 salary_data_2025_12_active_employees 有字段缺少类型定义: {'sequence_number'}", "表 salary_data_2025_12_active_employees 有多余的字段类型定义: {'sequence', 'row_number'}", '🔧 [P3修复] 发现6个字段类型不一致问题，建议检查数值字段的类型定义', '🔧 [P3修复] 示例: 表 part_time_employees 字段 total_salary (currency -> float)', '🔧 [P3修复] 示例: 表 contract_employees 字段 base_salary (currency -> float)', '🔧 [P3修复] 示例: 表 contract_employees 字段 performance_bonus (currency -> float)', '🔧 [P3修复] 2个表的existing_display_fields为空: salary_data_2025_07_active_employees, salary_data_2025_12_active_employees']
2025-08-15 21:40:48.625 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2060 | 🔧 [P1-1修复] 表 salary_data_2025_12_active_employees existing_display_fields已自动修复: ['employee_id', 'employee_name', 'department', 'employee_type', 'employee_type_code']...
2025-08-15 21:40:48.625 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2070 | 🔧 [架构优化] 应用智能修复策略，执行 1 项修复操作
2025-08-15 21:40:48.641 | INFO     | src.modules.format_management.field_registry:save_mappings:662 | 🏷️ [字段注册] 字段映射保存成功
2025-08-15 21:40:48.641 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:786 | 🔧 [架构优化] 配置修复已持久化保存，变更数量: 1
2025-08-15 21:40:48.641 | INFO     | src.modules.format_management.field_registry:load_mappings:629 | 🏷️ [字段注册] 字段映射加载成功
2025-08-15 21:40:48.641 | INFO     | src.modules.format_management.format_renderer:__init__:83 | 🎨 [格式渲染] 格式渲染器初始化完成
2025-08-15 21:40:48.641 | INFO     | src.modules.format_management.unified_format_manager:_on_config_updated:960 | 🚀 [配置同步] 配置重新加载完成: unknown, 加载状态: True -> True
2025-08-15 21:40:48.641 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:665 | 🚀 [配置同步] 已发布配置变更事件: salary_data_2025_12_active_employees
2025-08-15 21:40:48.641 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:669 | 完整字段映射保存成功: salary_data_2025_12_active_employees
2025-08-15 21:40:48.641 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:594 | 为表 salary_data_2025_12_active_employees 生成标准化字段映射: 28 个字段
2025-08-15 21:40:48.657 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:615 | Sheet 全部在职人员工资表 数据处理完成: 1396 行
2025-08-15 21:40:48.657 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:406 | 🔧 [P0修复] 用户选择异动表路径: 异动人员表 > 2025年 > 12月 > 全部在职人员
2025-08-15 21:40:48.657 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:422 | 🔧 [P0修复] 异动表使用灵活模板: change_data
2025-08-15 21:40:48.657 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:436 | 🔧 [P0修复] 异动表名: change_data_2025_12_flexible
2025-08-15 21:40:48.657 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:525 | 🔧 [P0修复] 开始创建灵活异动表: change_data_2025_12_flexible
2025-08-15 21:40:48.657 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:526 | 🔧 [P0修复] 用户Excel列数: 25
2025-08-15 21:40:48.657 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:527 | 🔧 [P0修复] 用户Excel列名: ['序号', '工号', '姓名', '部门名称', '人员类别代码', '人员类别', '2025年岗位工资', '2025年薪级工资', '津贴', '结余津贴', '2025年基础性绩效', '卫生费', '交通补贴', '物业补贴', '住房补贴', '车补', '通讯补贴', '2025年奖励性绩效预发', '补发', '借支', '应发工资', '2025公积金', '代扣代存养老保险', 'data_source', 'import_time']
2025-08-15 21:40:48.657 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 序号
2025-08-15 21:40:48.657 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 工号
2025-08-15 21:40:48.657 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 姓名
2025-08-15 21:40:48.657 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 部门名称
2025-08-15 21:40:48.657 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 人员类别代码
2025-08-15 21:40:48.657 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 人员类别
2025-08-15 21:40:48.657 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 2025年岗位工资
2025-08-15 21:40:48.657 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 2025年薪级工资
2025-08-15 21:40:48.657 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 津贴
2025-08-15 21:40:48.657 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 结余津贴
2025-08-15 21:40:48.657 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 2025年基础性绩效
2025-08-15 21:40:48.657 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 卫生费
2025-08-15 21:40:48.672 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 交通补贴
2025-08-15 21:40:48.672 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 物业补贴
2025-08-15 21:40:48.672 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 住房补贴
2025-08-15 21:40:48.688 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 车补
2025-08-15 21:40:48.688 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 通讯补贴
2025-08-15 21:40:48.688 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 2025年奖励性绩效预发
2025-08-15 21:40:48.688 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 补发
2025-08-15 21:40:48.688 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 借支
2025-08-15 21:40:48.688 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 应发工资
2025-08-15 21:40:48.688 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 2025公积金
2025-08-15 21:40:48.688 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 代扣代存养老保险
2025-08-15 21:40:48.688 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: data_source
2025-08-15 21:40:48.688 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: import_time
2025-08-15 21:40:48.688 | ERROR    | src.modules.data_storage.database_manager:create_table:504 | 🔧 [P0修复] 表创建失败: 'ColumnDefinition' object has no attribute 'data_type'
2025-08-15 21:40:48.688 | ERROR    | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:580 | 🔧 [P0修复] 灵活异动表创建失败: change_data_2025_12_flexible
2025-08-15 21:40:48.688 | ERROR    | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:460 | 🔧 [P0修复] 灵活异动表创建失败: change_data_2025_12_flexible
2025-08-15 21:40:48.688 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-15 21:40:48.813 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-15 21:40:48.813 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 21 列 (原始 21 列)
2025-08-15 21:40:48.813 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:599 | 数据质量检查: 已过滤1条姓名为空的记录
2025-08-15 21:40:48.813 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:642 | 数据过滤完成: 原始63条记录，过滤1条无效记录，有效记录62条
2025-08-15 21:40:48.813 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:530 | 工作表 A岗职工 使用智能默认配置
2025-08-15 21:40:48.813 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:818 | 为Sheet 'A岗职工' 生成智能默认配置: 2 个必需字段
2025-08-15 21:40:48.813 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:141 | 为模板 a_grade_employees 生成了 26 个字段映射
2025-08-15 21:40:48.813 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:547 | 使用专用模板 a_grade_employees 生成字段映射: 26 个字段
2025-08-15 21:40:48.829 | INFO     | src.modules.format_management.unified_format_manager:_on_config_updated:943 | 🚀 [配置同步] 收到配置更新事件: 表=unknown, 类型=unknown
2025-08-15 21:40:48.829 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:737 | 🔧 [自动修复] 为表 salary_data_2025_12_a_grade_employees 自动生成字段类型配置
2025-08-15 21:40:48.829 | INFO     | src.modules.format_management.field_registry:_find_matching_default_config:921 | 🔧 [智能匹配] 表 salary_data_2025_12_a_grade_employees 匹配成功: a_grade_employees (得分:330, 原因:精确后缀匹配:a_grade_employees,英文关键词:a_grade,英文关键词:a_grade_employees,模式匹配:.*a.*grade.*employee.*,优先级加分:100)
2025-08-15 21:40:48.829 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:755 | 🔧 [自动修复] 使用匹配的默认配置: salary_data_2025_12_a_grade_employees
2025-08-15 21:40:48.829 | WARNING  | src.modules.format_management.field_registry:_merge_default_mappings:778 | 🔧 [架构优化] 配置一致性警告: ["表 salary_data_2025_08_active_employees 有字段缺少类型定义: {'sequence_number'}", "表 salary_data_2025_08_active_employees 有多余的字段类型定义: {'sequence', 'row_number'}", "表 salary_data_2025_12_active_employees 有字段缺少类型定义: {'sequence_number'}", "表 salary_data_2025_12_active_employees 有多余的字段类型定义: {'sequence', 'row_number'}", '🔧 [P3修复] 发现6个字段类型不一致问题，建议检查数值字段的类型定义', '🔧 [P3修复] 示例: 表 part_time_employees 字段 total_salary (currency -> float)', '🔧 [P3修复] 示例: 表 contract_employees 字段 base_salary (currency -> float)', '🔧 [P3修复] 示例: 表 contract_employees 字段 performance_bonus (currency -> float)', '🔧 [P3修复] 2个表的existing_display_fields为空: salary_data_2025_07_active_employees, salary_data_2025_12_a_grade_employees']
2025-08-15 21:40:48.829 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2060 | 🔧 [P1-1修复] 表 salary_data_2025_12_a_grade_employees existing_display_fields已自动修复: ['employee_id', 'employee_name', 'department', 'employee_type', 'employee_type_code']...
2025-08-15 21:40:48.829 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2070 | 🔧 [架构优化] 应用智能修复策略，执行 1 项修复操作
2025-08-15 21:40:48.844 | INFO     | src.modules.format_management.field_registry:save_mappings:662 | 🏷️ [字段注册] 字段映射保存成功
2025-08-15 21:40:48.844 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:786 | 🔧 [架构优化] 配置修复已持久化保存，变更数量: 1
2025-08-15 21:40:48.844 | INFO     | src.modules.format_management.field_registry:load_mappings:629 | 🏷️ [字段注册] 字段映射加载成功
2025-08-15 21:40:48.844 | INFO     | src.modules.format_management.format_renderer:__init__:83 | 🎨 [格式渲染] 格式渲染器初始化完成
2025-08-15 21:40:48.844 | INFO     | src.modules.format_management.unified_format_manager:_on_config_updated:960 | 🚀 [配置同步] 配置重新加载完成: unknown, 加载状态: True -> True
2025-08-15 21:40:48.844 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:665 | 🚀 [配置同步] 已发布配置变更事件: salary_data_2025_12_a_grade_employees
2025-08-15 21:40:48.844 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:669 | 完整字段映射保存成功: salary_data_2025_12_a_grade_employees
2025-08-15 21:40:48.844 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:594 | 为表 salary_data_2025_12_a_grade_employees 生成标准化字段映射: 26 个字段
2025-08-15 21:40:48.844 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:615 | Sheet A岗职工 数据处理完成: 62 行
2025-08-15 21:40:48.844 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:406 | 🔧 [P0修复] 用户选择异动表路径: 异动人员表 > 2025年 > 12月 > 全部在职人员
2025-08-15 21:40:48.860 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:422 | 🔧 [P0修复] 异动表使用灵活模板: change_data
2025-08-15 21:40:48.876 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:436 | 🔧 [P0修复] 异动表名: change_data_2025_12_a_grade_employees
2025-08-15 21:40:48.876 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:525 | 🔧 [P0修复] 开始创建灵活异动表: change_data_2025_12_a_grade_employees
2025-08-15 21:40:48.876 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:526 | 🔧 [P0修复] 用户Excel列数: 23
2025-08-15 21:40:48.876 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:527 | 🔧 [P0修复] 用户Excel列名: ['序号', '工号', '姓名', '部门名称', '人员类别', '人员类别代码', '2025年岗位工资', '2025年校龄工资', '津贴', '结余津贴', '2025年基础性绩效', '卫生费', '2025年生活补贴', '车补', '2025年奖励性绩效预发', '补发', '借支', '应发工资', '2025公积金', '保险扣款', '代扣代存养老保险', 'data_source', 'import_time']
2025-08-15 21:40:48.876 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 序号
2025-08-15 21:40:48.876 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 工号
2025-08-15 21:40:48.876 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 姓名
2025-08-15 21:40:48.876 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 部门名称
2025-08-15 21:40:48.876 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 人员类别
2025-08-15 21:40:48.876 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 人员类别代码
2025-08-15 21:40:48.876 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 2025年岗位工资
2025-08-15 21:40:48.876 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 2025年校龄工资
2025-08-15 21:40:48.876 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 津贴
2025-08-15 21:40:48.876 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 结余津贴
2025-08-15 21:40:48.876 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 2025年基础性绩效
2025-08-15 21:40:48.876 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 卫生费
2025-08-15 21:40:48.876 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 2025年生活补贴
2025-08-15 21:40:48.876 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 车补
2025-08-15 21:40:48.876 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 2025年奖励性绩效预发
2025-08-15 21:40:48.876 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 补发
2025-08-15 21:40:48.876 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 借支
2025-08-15 21:40:48.876 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 应发工资
2025-08-15 21:40:48.876 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 2025公积金
2025-08-15 21:40:48.876 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 保险扣款
2025-08-15 21:40:48.876 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 代扣代存养老保险
2025-08-15 21:40:48.876 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: data_source
2025-08-15 21:40:48.876 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: import_time
2025-08-15 21:40:48.876 | ERROR    | src.modules.data_storage.database_manager:create_table:504 | 🔧 [P0修复] 表创建失败: 'ColumnDefinition' object has no attribute 'data_type'
2025-08-15 21:40:48.876 | ERROR    | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:580 | 🔧 [P0修复] 灵活异动表创建失败: change_data_2025_12_a_grade_employees
2025-08-15 21:40:48.876 | ERROR    | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:460 | 🔧 [P0修复] 灵活异动表创建失败: change_data_2025_12_a_grade_employees
2025-08-15 21:40:48.876 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:252 | 多Sheet导入完成: {'success': False, 'results': {}, 'total_records': 0, 'import_stats': {'total_sheets': 4, 'processed_sheets': 0, 'total_records': 0, 'failed_records': 0, 'validation_errors': 0}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']}
2025-08-15 21:40:57.862 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:178 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-08-15 21:40:58.049 | INFO     | src.modules.data_import.excel_importer:validate_file:115 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-08-15 21:40:58.065 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:216 | 开始导入多Sheet Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-08-15 21:40:58.252 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:233 | 检测到 4 个工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-08-15 21:40:58.252 | INFO     | src.modules.data_import.excel_importer:import_data:274 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-08-15 21:40:58.252 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-15 21:40:58.424 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:337 | [修复标识] Excel读取完成: 16列 (列过滤: 否)
2025-08-15 21:40:58.424 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-15 21:40:58.440 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 16 列 (原始 16 列)
2025-08-15 21:40:58.440 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:362 | 导入完成: 3行 x 16列
2025-08-15 21:40:58.440 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:599 | 数据质量检查: 已过滤1条姓名为空的记录
2025-08-15 21:40:58.440 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:642 | 数据过滤完成: 原始3条记录，过滤1条无效记录，有效记录2条
2025-08-15 21:40:58.440 | INFO     | src.modules.data_import.excel_importer:import_data:301 | [修复标识] 数据导入最终完成: 2行 × 16列
2025-08-15 21:40:58.440 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:530 | 工作表 离休人员工资表 使用智能默认配置
2025-08-15 21:40:58.440 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:818 | 为Sheet '离休人员工资表' 生成智能默认配置: 1 个必需字段
2025-08-15 21:40:58.440 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:141 | 为模板 retired_employees 生成了 21 个字段映射
2025-08-15 21:40:58.440 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:547 | 使用专用模板 retired_employees 生成字段映射: 21 个字段
2025-08-15 21:40:58.440 | INFO     | src.modules.format_management.unified_format_manager:_on_config_updated:943 | 🚀 [配置同步] 收到配置更新事件: 表=unknown, 类型=unknown
2025-08-15 21:40:58.440 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:737 | 🔧 [自动修复] 为表 salary_data_2025_12_retired_employees 自动生成字段类型配置
2025-08-15 21:40:58.440 | INFO     | src.modules.format_management.field_registry:_find_matching_default_config:921 | 🔧 [智能匹配] 表 salary_data_2025_12_retired_employees 匹配成功: retired_employees (得分:220, 原因:精确后缀匹配:retired_employees,英文关键词:retired,模式匹配:.*retired.*employee.*,分词匹配:retired)
2025-08-15 21:40:58.440 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:755 | 🔧 [自动修复] 使用匹配的默认配置: salary_data_2025_12_retired_employees
2025-08-15 21:40:58.455 | WARNING  | src.modules.format_management.field_registry:_merge_default_mappings:778 | 🔧 [架构优化] 配置一致性警告: ["表 salary_data_2025_08_active_employees 有字段缺少类型定义: {'sequence_number'}", "表 salary_data_2025_08_active_employees 有多余的字段类型定义: {'sequence', 'row_number'}", "表 salary_data_2025_12_active_employees 有字段缺少类型定义: {'sequence_number'}", "表 salary_data_2025_12_active_employees 有多余的字段类型定义: {'sequence', 'row_number'}", '🔧 [P3修复] 发现6个字段类型不一致问题，建议检查数值字段的类型定义', '🔧 [P3修复] 示例: 表 part_time_employees 字段 total_salary (currency -> float)', '🔧 [P3修复] 示例: 表 contract_employees 字段 base_salary (currency -> float)', '🔧 [P3修复] 示例: 表 contract_employees 字段 performance_bonus (currency -> float)', '🔧 [P3修复] 2个表的existing_display_fields为空: salary_data_2025_07_active_employees, salary_data_2025_12_retired_employees']
2025-08-15 21:40:58.455 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2060 | 🔧 [P1-1修复] 表 salary_data_2025_12_retired_employees existing_display_fields已自动修复: ['employee_id', 'employee_name', 'department', 'basic_retirement_salary', 'balance_allowance']...
2025-08-15 21:40:58.472 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2070 | 🔧 [架构优化] 应用智能修复策略，执行 1 项修复操作
2025-08-15 21:40:58.472 | INFO     | src.modules.format_management.field_registry:save_mappings:662 | 🏷️ [字段注册] 字段映射保存成功
2025-08-15 21:40:58.472 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:786 | 🔧 [架构优化] 配置修复已持久化保存，变更数量: 1
2025-08-15 21:40:58.472 | INFO     | src.modules.format_management.field_registry:load_mappings:629 | 🏷️ [字段注册] 字段映射加载成功
2025-08-15 21:40:58.472 | INFO     | src.modules.format_management.format_renderer:__init__:83 | 🎨 [格式渲染] 格式渲染器初始化完成
2025-08-15 21:40:58.472 | INFO     | src.core.unified_state_manager:update_global_state:350 | 全局状态已更新: StateChangeType.DATA_RELOADED
2025-08-15 21:40:58.472 | INFO     | src.modules.format_management.unified_format_manager:_on_config_updated:960 | 🚀 [配置同步] 配置重新加载完成: unknown, 加载状态: True -> True
2025-08-15 21:40:58.472 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:665 | 🚀 [配置同步] 已发布配置变更事件: salary_data_2025_12_retired_employees
2025-08-15 21:40:58.472 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:669 | 完整字段映射保存成功: salary_data_2025_12_retired_employees
2025-08-15 21:40:58.472 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:594 | 为表 salary_data_2025_12_retired_employees 生成标准化字段映射: 21 个字段
2025-08-15 21:40:58.487 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:615 | Sheet 离休人员工资表 数据处理完成: 2 行
2025-08-15 21:40:58.487 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:406 | 🔧 [P0修复] 用户选择异动表路径: 异动人员表 > 2025年 > 12月 > 全部在职人员
2025-08-15 21:40:58.487 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:422 | 🔧 [P0修复] 异动表使用灵活模板: change_data
2025-08-15 21:40:58.487 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:436 | 🔧 [P0修复] 异动表名: change_data_2025_12_retired_employees
2025-08-15 21:40:58.487 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:525 | 🔧 [P0修复] 开始创建灵活异动表: change_data_2025_12_retired_employees
2025-08-15 21:40:58.487 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:526 | 🔧 [P0修复] 用户Excel列数: 18
2025-08-15 21:40:58.487 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:527 | 🔧 [P0修复] 用户Excel列名: ['序号', '人员代码', '姓名', '部门名称', '基本\n离休费', '结余\n津贴', '生活\n补贴', '住房\n补贴', '物业\n补贴', '离休\n补贴', '护理费', '增发一次\n性生活补贴', '补发', '合计', '借支', '备注', 'data_source', 'import_time']
2025-08-15 21:40:58.487 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 序号
2025-08-15 21:40:58.487 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 人员代码
2025-08-15 21:40:58.487 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 姓名
2025-08-15 21:40:58.487 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 部门名称
2025-08-15 21:40:58.504 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 基本
离休费
2025-08-15 21:40:58.504 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 结余
津贴
2025-08-15 21:40:58.504 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 生活
补贴
2025-08-15 21:40:58.504 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 住房
补贴
2025-08-15 21:40:58.504 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 物业
补贴
2025-08-15 21:40:58.504 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 离休
补贴
2025-08-15 21:40:58.504 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 护理费
2025-08-15 21:40:58.504 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 增发一次
性生活补贴
2025-08-15 21:40:58.504 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 补发
2025-08-15 21:40:58.504 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 合计
2025-08-15 21:40:58.504 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 借支
2025-08-15 21:40:58.504 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 备注
2025-08-15 21:40:58.504 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: data_source
2025-08-15 21:40:58.504 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: import_time
2025-08-15 21:40:58.504 | ERROR    | src.modules.data_storage.database_manager:create_table:504 | 🔧 [P0修复] 表创建失败: 'ColumnDefinition' object has no attribute 'data_type'
2025-08-15 21:40:58.504 | ERROR    | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:580 | 🔧 [P0修复] 灵活异动表创建失败: change_data_2025_12_retired_employees
2025-08-15 21:40:58.504 | ERROR    | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:460 | 🔧 [P0修复] 灵活异动表创建失败: change_data_2025_12_retired_employees
2025-08-15 21:40:58.504 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-15 21:40:58.627 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-15 21:40:58.627 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 27 列 (原始 27 列)
2025-08-15 21:40:58.627 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:599 | 数据质量检查: 已过滤1条姓名为空的记录
2025-08-15 21:40:58.627 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:642 | 数据过滤完成: 原始14条记录，过滤1条无效记录，有效记录13条
2025-08-15 21:40:58.627 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:530 | 工作表 退休人员工资表 使用智能默认配置
2025-08-15 21:40:58.627 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:818 | 为Sheet '退休人员工资表' 生成智能默认配置: 1 个必需字段
2025-08-15 21:40:58.627 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:141 | 为模板 pension_employees 生成了 32 个字段映射
2025-08-15 21:40:58.627 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:547 | 使用专用模板 pension_employees 生成字段映射: 32 个字段
2025-08-15 21:40:58.643 | INFO     | src.modules.format_management.unified_format_manager:_on_config_updated:943 | 🚀 [配置同步] 收到配置更新事件: 表=unknown, 类型=unknown
2025-08-15 21:40:58.643 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:737 | 🔧 [自动修复] 为表 salary_data_2025_12_pension_employees 自动生成字段类型配置
2025-08-15 21:40:58.643 | INFO     | src.modules.format_management.field_registry:_find_matching_default_config:921 | 🔧 [智能匹配] 表 salary_data_2025_12_pension_employees 匹配成功: pension_employees (得分:300, 原因:精确后缀匹配:pension_employees,英文关键词:pension,英文关键词:pension_employees,模式匹配:.*pension.*employee.*,模式匹配:.*pension.*,分词匹配:pension)
2025-08-15 21:40:58.643 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:755 | 🔧 [自动修复] 使用匹配的默认配置: salary_data_2025_12_pension_employees
2025-08-15 21:40:58.643 | WARNING  | src.modules.format_management.field_registry:_merge_default_mappings:778 | 🔧 [架构优化] 配置一致性警告: ["表 salary_data_2025_08_active_employees 有字段缺少类型定义: {'sequence_number'}", "表 salary_data_2025_08_active_employees 有多余的字段类型定义: {'sequence', 'row_number'}", "表 salary_data_2025_12_active_employees 有字段缺少类型定义: {'sequence_number'}", "表 salary_data_2025_12_active_employees 有多余的字段类型定义: {'sequence', 'row_number'}", '🔧 [P3修复] 发现6个字段类型不一致问题，建议检查数值字段的类型定义', '🔧 [P3修复] 示例: 表 part_time_employees 字段 total_salary (currency -> float)', '🔧 [P3修复] 示例: 表 contract_employees 字段 base_salary (currency -> float)', '🔧 [P3修复] 示例: 表 contract_employees 字段 performance_bonus (currency -> float)', '🔧 [P3修复] 2个表的existing_display_fields为空: salary_data_2025_07_active_employees, salary_data_2025_12_pension_employees']
2025-08-15 21:40:58.643 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2060 | 🔧 [P1-1修复] 表 salary_data_2025_12_pension_employees existing_display_fields已自动修复: ['employee_id', 'employee_name', 'department', 'employee_type_code', 'basic_retirement_salary']...
2025-08-15 21:40:58.643 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2070 | 🔧 [架构优化] 应用智能修复策略，执行 1 项修复操作
2025-08-15 21:40:58.659 | INFO     | src.modules.format_management.field_registry:save_mappings:662 | 🏷️ [字段注册] 字段映射保存成功
2025-08-15 21:40:58.659 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:786 | 🔧 [架构优化] 配置修复已持久化保存，变更数量: 1
2025-08-15 21:40:58.659 | INFO     | src.modules.format_management.field_registry:load_mappings:629 | 🏷️ [字段注册] 字段映射加载成功
2025-08-15 21:40:58.659 | INFO     | src.modules.format_management.format_renderer:__init__:83 | 🎨 [格式渲染] 格式渲染器初始化完成
2025-08-15 21:40:58.659 | INFO     | src.modules.format_management.unified_format_manager:_on_config_updated:960 | 🚀 [配置同步] 配置重新加载完成: unknown, 加载状态: True -> True
2025-08-15 21:40:58.674 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:665 | 🚀 [配置同步] 已发布配置变更事件: salary_data_2025_12_pension_employees
2025-08-15 21:40:58.674 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:669 | 完整字段映射保存成功: salary_data_2025_12_pension_employees
2025-08-15 21:40:58.674 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:594 | 为表 salary_data_2025_12_pension_employees 生成标准化字段映射: 32 个字段
2025-08-15 21:40:58.674 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:615 | Sheet 退休人员工资表 数据处理完成: 13 行
2025-08-15 21:40:58.674 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:406 | 🔧 [P0修复] 用户选择异动表路径: 异动人员表 > 2025年 > 12月 > 全部在职人员
2025-08-15 21:40:58.674 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:422 | 🔧 [P0修复] 异动表使用灵活模板: change_data
2025-08-15 21:40:58.674 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:436 | 🔧 [P0修复] 异动表名: change_data_2025_12_retired_employees
2025-08-15 21:40:58.674 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:525 | 🔧 [P0修复] 开始创建灵活异动表: change_data_2025_12_retired_employees
2025-08-15 21:40:58.674 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:526 | 🔧 [P0修复] 用户Excel列数: 29
2025-08-15 21:40:58.674 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:527 | 🔧 [P0修复] 用户Excel列名: ['序号', '人员代码', '姓名', '部门名称', '人员类别代码', '基本退休费', '津贴', '结余津贴', '离退休生活补贴', '护理费', '物业补贴', '住房补贴', '增资预付', '2016待遇调整', '2017待遇调整', '2018待遇调整', '2019待遇调整', '2020待遇调整', '2021待遇调整', '2022待遇调整', '2023待遇调整', '补发', '借支', '应发工资', '公积', '保险扣款', '备注', 'data_source', 'import_time']
2025-08-15 21:40:58.674 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 序号
2025-08-15 21:40:58.674 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 人员代码
2025-08-15 21:40:58.674 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 姓名
2025-08-15 21:40:58.674 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 部门名称
2025-08-15 21:40:58.674 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 人员类别代码
2025-08-15 21:40:58.674 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 基本退休费
2025-08-15 21:40:58.674 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 津贴
2025-08-15 21:40:58.674 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 结余津贴
2025-08-15 21:40:58.674 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 离退休生活补贴
2025-08-15 21:40:58.690 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 护理费
2025-08-15 21:40:58.706 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 物业补贴
2025-08-15 21:40:58.706 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 住房补贴
2025-08-15 21:40:58.706 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 增资预付
2025-08-15 21:40:58.706 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 2016待遇调整
2025-08-15 21:40:58.706 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 2017待遇调整
2025-08-15 21:40:58.706 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 2018待遇调整
2025-08-15 21:40:58.706 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 2019待遇调整
2025-08-15 21:40:58.706 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 2020待遇调整
2025-08-15 21:40:58.706 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 2021待遇调整
2025-08-15 21:40:58.706 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 2022待遇调整
2025-08-15 21:40:58.706 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 2023待遇调整
2025-08-15 21:40:58.706 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 补发
2025-08-15 21:40:58.706 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 借支
2025-08-15 21:40:58.706 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 应发工资
2025-08-15 21:40:58.706 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 公积
2025-08-15 21:40:58.706 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 保险扣款
2025-08-15 21:40:58.706 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 备注
2025-08-15 21:40:58.706 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: data_source
2025-08-15 21:40:58.706 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: import_time
2025-08-15 21:40:58.706 | ERROR    | src.modules.data_storage.database_manager:create_table:504 | 🔧 [P0修复] 表创建失败: 'ColumnDefinition' object has no attribute 'data_type'
2025-08-15 21:40:58.706 | ERROR    | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:580 | 🔧 [P0修复] 灵活异动表创建失败: change_data_2025_12_retired_employees
2025-08-15 21:40:58.706 | ERROR    | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:460 | 🔧 [P0修复] 灵活异动表创建失败: change_data_2025_12_retired_employees
2025-08-15 21:40:58.706 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-15 21:40:58.846 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-15 21:40:58.846 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 23 列 (原始 23 列)
2025-08-15 21:40:58.846 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:599 | 数据质量检查: 已过滤1条姓名为空的记录
2025-08-15 21:40:58.846 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:642 | 数据过滤完成: 原始1397条记录，过滤1条无效记录，有效记录1396条
2025-08-15 21:40:58.846 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:530 | 工作表 全部在职人员工资表 使用智能默认配置
2025-08-15 21:40:58.846 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:818 | 为Sheet '全部在职人员工资表' 生成智能默认配置: 2 个必需字段
2025-08-15 21:40:58.846 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:141 | 为模板 active_employees 生成了 28 个字段映射
2025-08-15 21:40:58.846 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:547 | 使用专用模板 active_employees 生成字段映射: 28 个字段
2025-08-15 21:40:58.862 | INFO     | src.modules.format_management.unified_format_manager:_on_config_updated:943 | 🚀 [配置同步] 收到配置更新事件: 表=unknown, 类型=unknown
2025-08-15 21:40:58.862 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:737 | 🔧 [自动修复] 为表 salary_data_2025_12_active_employees 自动生成字段类型配置
2025-08-15 21:40:58.862 | INFO     | src.modules.format_management.field_registry:_find_matching_default_config:921 | 🔧 [智能匹配] 表 salary_data_2025_12_active_employees 匹配成功: active_employees (得分:220, 原因:精确后缀匹配:active_employees,英文关键词:active,模式匹配:.*active.*employee.*,分词匹配:active)
2025-08-15 21:40:58.862 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:755 | 🔧 [自动修复] 使用匹配的默认配置: salary_data_2025_12_active_employees
2025-08-15 21:40:58.862 | WARNING  | src.modules.format_management.field_registry:_merge_default_mappings:778 | 🔧 [架构优化] 配置一致性警告: ["表 salary_data_2025_08_active_employees 有字段缺少类型定义: {'sequence_number'}", "表 salary_data_2025_08_active_employees 有多余的字段类型定义: {'sequence', 'row_number'}", "表 salary_data_2025_12_active_employees 有字段缺少类型定义: {'sequence_number'}", "表 salary_data_2025_12_active_employees 有多余的字段类型定义: {'sequence', 'row_number'}", '🔧 [P3修复] 发现6个字段类型不一致问题，建议检查数值字段的类型定义', '🔧 [P3修复] 示例: 表 part_time_employees 字段 total_salary (currency -> float)', '🔧 [P3修复] 示例: 表 contract_employees 字段 base_salary (currency -> float)', '🔧 [P3修复] 示例: 表 contract_employees 字段 performance_bonus (currency -> float)', '🔧 [P3修复] 2个表的existing_display_fields为空: salary_data_2025_07_active_employees, salary_data_2025_12_active_employees']
2025-08-15 21:40:58.862 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2060 | 🔧 [P1-1修复] 表 salary_data_2025_12_active_employees existing_display_fields已自动修复: ['employee_id', 'employee_name', 'department', 'employee_type', 'employee_type_code']...
2025-08-15 21:40:58.862 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2070 | 🔧 [架构优化] 应用智能修复策略，执行 1 项修复操作
2025-08-15 21:40:58.877 | INFO     | src.modules.format_management.field_registry:save_mappings:662 | 🏷️ [字段注册] 字段映射保存成功
2025-08-15 21:40:58.877 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:786 | 🔧 [架构优化] 配置修复已持久化保存，变更数量: 1
2025-08-15 21:40:58.877 | INFO     | src.modules.format_management.field_registry:load_mappings:629 | 🏷️ [字段注册] 字段映射加载成功
2025-08-15 21:40:58.877 | INFO     | src.modules.format_management.format_renderer:__init__:83 | 🎨 [格式渲染] 格式渲染器初始化完成
2025-08-15 21:40:58.877 | INFO     | src.modules.format_management.unified_format_manager:_on_config_updated:960 | 🚀 [配置同步] 配置重新加载完成: unknown, 加载状态: True -> True
2025-08-15 21:40:58.877 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:665 | 🚀 [配置同步] 已发布配置变更事件: salary_data_2025_12_active_employees
2025-08-15 21:40:58.877 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:669 | 完整字段映射保存成功: salary_data_2025_12_active_employees
2025-08-15 21:40:58.877 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:594 | 为表 salary_data_2025_12_active_employees 生成标准化字段映射: 28 个字段
2025-08-15 21:40:58.893 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:615 | Sheet 全部在职人员工资表 数据处理完成: 1396 行
2025-08-15 21:40:58.893 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:406 | 🔧 [P0修复] 用户选择异动表路径: 异动人员表 > 2025年 > 12月 > 全部在职人员
2025-08-15 21:40:58.893 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:422 | 🔧 [P0修复] 异动表使用灵活模板: change_data
2025-08-15 21:40:58.893 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:436 | 🔧 [P0修复] 异动表名: change_data_2025_12_flexible
2025-08-15 21:40:58.893 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:525 | 🔧 [P0修复] 开始创建灵活异动表: change_data_2025_12_flexible
2025-08-15 21:40:58.893 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:526 | 🔧 [P0修复] 用户Excel列数: 25
2025-08-15 21:40:58.893 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:527 | 🔧 [P0修复] 用户Excel列名: ['序号', '工号', '姓名', '部门名称', '人员类别代码', '人员类别', '2025年岗位工资', '2025年薪级工资', '津贴', '结余津贴', '2025年基础性绩效', '卫生费', '交通补贴', '物业补贴', '住房补贴', '车补', '通讯补贴', '2025年奖励性绩效预发', '补发', '借支', '应发工资', '2025公积金', '代扣代存养老保险', 'data_source', 'import_time']
2025-08-15 21:40:58.893 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 序号
2025-08-15 21:40:58.893 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 工号
2025-08-15 21:40:58.893 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 姓名
2025-08-15 21:40:58.893 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 部门名称
2025-08-15 21:40:58.893 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 人员类别代码
2025-08-15 21:40:58.893 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 人员类别
2025-08-15 21:40:58.893 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 2025年岗位工资
2025-08-15 21:40:58.893 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 2025年薪级工资
2025-08-15 21:40:58.893 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 津贴
2025-08-15 21:40:58.893 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 结余津贴
2025-08-15 21:40:58.893 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 2025年基础性绩效
2025-08-15 21:40:58.893 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 卫生费
2025-08-15 21:40:58.893 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 交通补贴
2025-08-15 21:40:58.893 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 物业补贴
2025-08-15 21:40:58.893 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 住房补贴
2025-08-15 21:40:58.893 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 车补
2025-08-15 21:40:58.893 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 通讯补贴
2025-08-15 21:40:58.893 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 2025年奖励性绩效预发
2025-08-15 21:40:58.893 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 补发
2025-08-15 21:40:58.909 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 借支
2025-08-15 21:40:58.909 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 应发工资
2025-08-15 21:40:58.909 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 2025公积金
2025-08-15 21:40:58.924 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 代扣代存养老保险
2025-08-15 21:40:58.924 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: data_source
2025-08-15 21:40:58.924 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: import_time
2025-08-15 21:40:58.924 | ERROR    | src.modules.data_storage.database_manager:create_table:504 | 🔧 [P0修复] 表创建失败: 'ColumnDefinition' object has no attribute 'data_type'
2025-08-15 21:40:58.924 | ERROR    | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:580 | 🔧 [P0修复] 灵活异动表创建失败: change_data_2025_12_flexible
2025-08-15 21:40:58.924 | ERROR    | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:460 | 🔧 [P0修复] 灵活异动表创建失败: change_data_2025_12_flexible
2025-08-15 21:40:58.924 | INFO     | src.utils.log_config:log_file_operation:336 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-08-15 21:40:59.035 | INFO     | src.modules.data_import.excel_importer:_clean_data:459 | [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-08-15 21:40:59.035 | INFO     | src.modules.data_import.excel_importer:_clean_data:470 | 数据清理完成: 保留所有 21 列 (原始 21 列)
2025-08-15 21:40:59.035 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:599 | 数据质量检查: 已过滤1条姓名为空的记录
2025-08-15 21:40:59.049 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:642 | 数据过滤完成: 原始63条记录，过滤1条无效记录，有效记录62条
2025-08-15 21:40:59.049 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:530 | 工作表 A岗职工 使用智能默认配置
2025-08-15 21:40:59.049 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:818 | 为Sheet 'A岗职工' 生成智能默认配置: 2 个必需字段
2025-08-15 21:40:59.049 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:141 | 为模板 a_grade_employees 生成了 26 个字段映射
2025-08-15 21:40:59.049 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:547 | 使用专用模板 a_grade_employees 生成字段映射: 26 个字段
2025-08-15 21:40:59.049 | INFO     | src.modules.format_management.unified_format_manager:_on_config_updated:943 | 🚀 [配置同步] 收到配置更新事件: 表=unknown, 类型=unknown
2025-08-15 21:40:59.049 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:737 | 🔧 [自动修复] 为表 salary_data_2025_12_a_grade_employees 自动生成字段类型配置
2025-08-15 21:40:59.049 | INFO     | src.modules.format_management.field_registry:_find_matching_default_config:921 | 🔧 [智能匹配] 表 salary_data_2025_12_a_grade_employees 匹配成功: a_grade_employees (得分:330, 原因:精确后缀匹配:a_grade_employees,英文关键词:a_grade,英文关键词:a_grade_employees,模式匹配:.*a.*grade.*employee.*,优先级加分:100)
2025-08-15 21:40:59.049 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:755 | 🔧 [自动修复] 使用匹配的默认配置: salary_data_2025_12_a_grade_employees
2025-08-15 21:40:59.049 | WARNING  | src.modules.format_management.field_registry:_merge_default_mappings:778 | 🔧 [架构优化] 配置一致性警告: ["表 salary_data_2025_08_active_employees 有字段缺少类型定义: {'sequence_number'}", "表 salary_data_2025_08_active_employees 有多余的字段类型定义: {'sequence', 'row_number'}", "表 salary_data_2025_12_active_employees 有字段缺少类型定义: {'sequence_number'}", "表 salary_data_2025_12_active_employees 有多余的字段类型定义: {'sequence', 'row_number'}", '🔧 [P3修复] 发现6个字段类型不一致问题，建议检查数值字段的类型定义', '🔧 [P3修复] 示例: 表 part_time_employees 字段 total_salary (currency -> float)', '🔧 [P3修复] 示例: 表 contract_employees 字段 base_salary (currency -> float)', '🔧 [P3修复] 示例: 表 contract_employees 字段 performance_bonus (currency -> float)', '🔧 [P3修复] 2个表的existing_display_fields为空: salary_data_2025_07_active_employees, salary_data_2025_12_a_grade_employees']
2025-08-15 21:40:59.065 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2060 | 🔧 [P1-1修复] 表 salary_data_2025_12_a_grade_employees existing_display_fields已自动修复: ['employee_id', 'employee_name', 'department', 'employee_type', 'employee_type_code']...
2025-08-15 21:40:59.065 | INFO     | src.modules.format_management.field_registry:_apply_smart_config_repair_strategy:2070 | 🔧 [架构优化] 应用智能修复策略，执行 1 项修复操作
2025-08-15 21:40:59.081 | INFO     | src.modules.format_management.field_registry:save_mappings:662 | 🏷️ [字段注册] 字段映射保存成功
2025-08-15 21:40:59.081 | INFO     | src.modules.format_management.field_registry:_merge_default_mappings:786 | 🔧 [架构优化] 配置修复已持久化保存，变更数量: 1
2025-08-15 21:40:59.081 | INFO     | src.modules.format_management.field_registry:load_mappings:629 | 🏷️ [字段注册] 字段映射加载成功
2025-08-15 21:40:59.081 | INFO     | src.modules.format_management.format_renderer:__init__:83 | 🎨 [格式渲染] 格式渲染器初始化完成
2025-08-15 21:40:59.081 | INFO     | src.modules.format_management.unified_format_manager:_on_config_updated:960 | 🚀 [配置同步] 配置重新加载完成: unknown, 加载状态: True -> True
2025-08-15 21:40:59.081 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:665 | 🚀 [配置同步] 已发布配置变更事件: salary_data_2025_12_a_grade_employees
2025-08-15 21:40:59.081 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:669 | 完整字段映射保存成功: salary_data_2025_12_a_grade_employees
2025-08-15 21:40:59.081 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:594 | 为表 salary_data_2025_12_a_grade_employees 生成标准化字段映射: 26 个字段
2025-08-15 21:40:59.081 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:615 | Sheet A岗职工 数据处理完成: 62 行
2025-08-15 21:40:59.081 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:406 | 🔧 [P0修复] 用户选择异动表路径: 异动人员表 > 2025年 > 12月 > 全部在职人员
2025-08-15 21:40:59.081 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:422 | 🔧 [P0修复] 异动表使用灵活模板: change_data
2025-08-15 21:40:59.081 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:436 | 🔧 [P0修复] 异动表名: change_data_2025_12_a_grade_employees
2025-08-15 21:40:59.081 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:525 | 🔧 [P0修复] 开始创建灵活异动表: change_data_2025_12_a_grade_employees
2025-08-15 21:40:59.081 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:526 | 🔧 [P0修复] 用户Excel列数: 23
2025-08-15 21:40:59.081 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:527 | 🔧 [P0修复] 用户Excel列名: ['序号', '工号', '姓名', '部门名称', '人员类别', '人员类别代码', '2025年岗位工资', '2025年校龄工资', '津贴', '结余津贴', '2025年基础性绩效', '卫生费', '2025年生活补贴', '车补', '2025年奖励性绩效预发', '补发', '借支', '应发工资', '2025公积金', '保险扣款', '代扣代存养老保险', 'data_source', 'import_time']
2025-08-15 21:40:59.081 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 序号
2025-08-15 21:40:59.081 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 工号
2025-08-15 21:40:59.081 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 姓名
2025-08-15 21:40:59.081 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 部门名称
2025-08-15 21:40:59.081 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 人员类别
2025-08-15 21:40:59.081 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 人员类别代码
2025-08-15 21:40:59.081 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 2025年岗位工资
2025-08-15 21:40:59.081 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 2025年校龄工资
2025-08-15 21:40:59.096 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 津贴
2025-08-15 21:40:59.112 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 结余津贴
2025-08-15 21:40:59.112 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 2025年基础性绩效
2025-08-15 21:40:59.112 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 卫生费
2025-08-15 21:40:59.112 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 2025年生活补贴
2025-08-15 21:40:59.112 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 车补
2025-08-15 21:40:59.112 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 2025年奖励性绩效预发
2025-08-15 21:40:59.112 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 补发
2025-08-15 21:40:59.112 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 借支
2025-08-15 21:40:59.112 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 应发工资
2025-08-15 21:40:59.112 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 2025公积金
2025-08-15 21:40:59.112 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 保险扣款
2025-08-15 21:40:59.112 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 代扣代存养老保险
2025-08-15 21:40:59.112 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: data_source
2025-08-15 21:40:59.112 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: import_time
2025-08-15 21:40:59.112 | ERROR    | src.modules.data_storage.database_manager:create_table:504 | 🔧 [P0修复] 表创建失败: 'ColumnDefinition' object has no attribute 'data_type'
2025-08-15 21:40:59.112 | ERROR    | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:580 | 🔧 [P0修复] 灵活异动表创建失败: change_data_2025_12_a_grade_employees
2025-08-15 21:40:59.112 | ERROR    | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:460 | 🔧 [P0修复] 灵活异动表创建失败: change_data_2025_12_a_grade_employees
2025-08-15 21:40:59.112 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:252 | 多Sheet导入完成: {'success': False, 'results': {}, 'total_records': 0, 'import_stats': {'total_sheets': 4, 'processed_sheets': 0, 'total_records': 0, 'failed_records': 0, 'validation_errors': 0}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']}
2025-08-15 21:41:05.272 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2095 | MainWorkspaceArea 响应式适配: sm
2025-08-15 21:41:06.975 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 08月 > 全部在职人员', '工资表 > 2025年', '工资表', '工资表 > 2025年 > 05月 > 全部在职人员', '工资表 > 2025年 > 08月']
2025-08-15 21:41:06.975 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7370 | 导航变化: 工资表 > 2025年 > 08月 > 退休人员
2025-08-15 21:41:06.975 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10489 | 🚫 [用户要求] 表格切换不显示加载条: change_data_2025_08_active_employees -> None
2025-08-15 21:41:06.975 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10505 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-08-15 21:41:06.975 | INFO     | src.gui.prototype.prototype_main_window:clear_field_processing_cache:7245 | 🔧 [缓存清理] 清理了所有 0 个字段处理缓存条目
2025-08-15 21:41:06.975 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8297 | 🔧 [表名生成] 从完整路径生成表名: ['工资表', '2025年', '08月', '退休人员'] -> salary_data_2025_08_pension_employees
2025-08-15 21:41:06.975 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-15 21:41:06.975 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:362 | 已清理表 salary_data_2025_08_pension_employees 的缓存
2025-08-15 21:41:06.975 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:11175 | 已注册 4 个表格到表头管理器
2025-08-15 21:41:06.975 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1188 | 🔧 [P1-3] 开始智能表头重影检测，共 4 个表格
2025-08-15 21:41:06.975 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1261 | 🔧 [P1-3] 智能表头重影检测完成，耗时 0.00ms
2025-08-15 21:41:06.975 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1262 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-08-15 21:41:06.975 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7586 | 🆕 使用新架构加载数据: salary_data_2025_08_pension_employees（通过事件系统）
2025-08-15 21:41:06.975 | INFO     | src.services.table_data_service:load_table_data:366 | 加载表格数据: 页码: 1
2025-08-15 21:41:06.975 | INFO     | src.core.unified_data_request_manager:request_table_data:212 | 开始处理数据请求
2025-08-15 21:41:06.975 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:805 | 正在从表 salary_data_2025_08_pension_employees 分页获取数据（支持排序）: 第1页, 每页50条, 排序=0列
2025-08-15 21:41:06.991 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:921 | 成功从表 salary_data_2025_08_pension_employees 获取第1页数据（含排序）: 13 行，总计13行
2025-08-15 21:41:06.991 | INFO     | src.core.unified_data_request_manager:request_table_data:262 | 数据请求处理完成: 字段=32, 行数=13, 耗时=15.7ms
2025-08-15 21:41:06.991 | INFO     | src.services.table_data_service:load_table_data:462 | [新架构] 使用统一格式管理器（单例优化）
2025-08-15 21:41:06.991 | INFO     | src.modules.format_management.format_renderer:render_dataframe:107 | 🎯 [格式渲染] 隐藏字段配置: ['id', 'updated_at', 'created_at', 'sequence_number']
2025-08-15 21:41:06.991 | INFO     | src.modules.format_management.format_renderer:render_dataframe:115 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-08-15 21:41:07.006 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-08-15 21:41:07.006 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-08-15 21:41:07.006 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 备注 -> 英文名: remarks -> 类型: string
2025-08-15 21:41:07.006 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 月份 -> 英文名: month -> 类型: month_string_extract_last_two
2025-08-15 21:41:07.006 | INFO     | src.modules.format_management.field_registry:get_display_fields:1554 | 🔧 [P0-2修复] display_order转换完成: 28个字段，原始字段数: 28
2025-08-15 21:41:07.006 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | 🔧 [DEBUG] table_type=pension_employees, display_fields=28个字段
2025-08-15 21:41:07.006 | INFO     | src.modules.format_management.format_renderer:render_dataframe:211 | 🔧 [P0修复] 配置字段匹配完成: 成功匹配 28/28 个字段
2025-08-15 21:41:07.006 | INFO     | src.modules.format_management.format_renderer:render_dataframe:219 | 🔧 [P0-关键修复] 保留未配置的业务字段: 4个
2025-08-15 21:41:07.006 | INFO     | src.modules.format_management.format_renderer:render_dataframe:224 | 🔧 [P0修复] 字段重排完成: 原始32个 -> 最终32个字段
2025-08-15 21:41:07.006 | INFO     | src.modules.format_management.format_renderer:render_dataframe:240 | 🎨 [格式渲染] DataFrame格式化完成: pension_employees, 行数: 13, 列数: 32
2025-08-15 21:41:07.006 | INFO     | src.modules.format_management.unified_format_manager:format_data:367 | 🎯 [统一格式管理] 数据格式化完成: pension_employees, 行数: 13, 列数: 32
2025-08-15 21:41:07.006 | INFO     | src.core.unified_state_manager:update_table_state:233 | 表状态已更新: salary_data_2025_08_pension_employees, 变更类型: None
2025-08-15 21:41:07.006 | INFO     | src.services.table_data_service:load_table_data:512 | [修复数据发布] 数据加载成功，发布更新事件: 13行
2025-08-15 21:41:07.006 | INFO     | src.services.table_data_service:load_table_data:516 | 📨[data_event] publish | table=salary_data_2025_08_pension_employees | rows=13 | page=1 | size=50 | total=13 | req_type=RequestType.INITIAL_LOAD | request_id=SV-1755265267006-72c5ffdc
2025-08-15 21:41:07.006 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3785 | 📥[data_event] received | table=salary_data_2025_08_pension_employees | request_id=SV-1755265267006-72c5ffdc
2025-08-15 21:41:07.006 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3817 | 数据内容: 13行 x 32列
2025-08-15 21:41:07.006 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3843 | 🔧 [数据更新] 接收到initial_load操作的数据更新事件: salary_data_2025_08_pension_employees, 13行
2025-08-15 21:41:07.022 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7327 | 表 salary_data_2025_08_pension_employees 没有用户偏好设置，显示所有可见字段
2025-08-15 21:41:07.039 | INFO     | src.gui.prototype.prototype_main_window:set_data:873 | 通过公共接口设置新的表格数据(DataFrame): 13 行
2025-08-15 21:41:07.039 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 13行，表名: salary_data_2025_08_pension_employees
2025-08-15 21:41:07.039 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=19709165, 薪资=N/A
2025-08-15 21:41:07.039 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=19981259, 薪资=N/A
2025-08-15 21:41:07.039 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:5271 | 最大可见行数已更新: 10 -> 13
2025-08-15 21:41:07.039 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5324 | 根据数据量(13)自动调整最大可见行数为: 13
2025-08-15 21:41:07.039 | INFO     | src.modules.format_management.format_renderer:render_dataframe:107 | 🎯 [格式渲染] 隐藏字段配置: ['id', 'updated_at', 'created_at', 'sequence_number']
2025-08-15 21:41:07.039 | INFO     | src.modules.format_management.format_renderer:render_dataframe:115 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-08-15 21:41:07.053 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-08-15 21:41:07.053 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-08-15 21:41:07.053 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 备注 -> 英文名: remarks -> 类型: string
2025-08-15 21:41:07.053 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 月份 -> 英文名: month -> 类型: month_string_extract_last_two
2025-08-15 21:41:07.053 | INFO     | src.modules.format_management.field_registry:get_display_fields:1554 | 🔧 [P0-2修复] display_order转换完成: 28个字段，原始字段数: 28
2025-08-15 21:41:07.053 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | 🔧 [DEBUG] table_type=pension_employees, display_fields=28个字段
2025-08-15 21:41:07.053 | INFO     | src.modules.format_management.format_renderer:render_dataframe:211 | 🔧 [P0修复] 配置字段匹配完成: 成功匹配 28/28 个字段
2025-08-15 21:41:07.053 | INFO     | src.modules.format_management.format_renderer:render_dataframe:224 | 🔧 [P0修复] 字段重排完成: 原始28个 -> 最终28个字段
2025-08-15 21:41:07.053 | INFO     | src.modules.format_management.format_renderer:render_dataframe:240 | 🎨 [格式渲染] DataFrame格式化完成: pension_employees, 行数: 13, 列数: 28
2025-08-15 21:41:07.053 | INFO     | src.modules.format_management.unified_format_manager:format_data:367 | 🎯 [统一格式管理] 数据格式化完成: pension_employees, 行数: 13, 列数: 28
2025-08-15 21:41:07.069 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 13行数据
2025-08-15 21:41:07.069 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:52 | [数据流追踪] 开始小数据集渲染: 13行 x 28列
2025-08-15 21:41:07.085 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:112 | [数据流追踪] 小数据集渲染完成: 耗时15.7ms, 平均每行1.21ms
2025-08-15 21:41:07.085 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=13, 渲染时间=15.7ms, 策略=small_dataset
2025-08-15 21:41:07.085 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:279 | [数据流追踪] 选择小数据集渲染策略
2025-08-15 21:41:07.085 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19709165
2025-08-15 21:41:07.085 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 19981259
2025-08-15 21:41:07.085 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第2行数据工号: 19721294
2025-08-15 21:41:07.085 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第3行数据工号: 19841258
2025-08-15 21:41:07.085 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第4行数据工号: 19499098
2025-08-15 21:41:07.085 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 13
2025-08-15 21:41:07.085 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[0]: 工号=19709165, 薪资=N/A
2025-08-15 21:41:07.085 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[1]: 工号=19981259, 薪资=N/A
2025-08-15 21:41:07.085 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[2]: 工号=19721294, 薪资=N/A
2025-08-15 21:41:07.085 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[3]: 工号=19841258, 薪资=N/A
2025-08-15 21:41:07.085 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[4]: 工号=19499098, 薪资=N/A
2025-08-15 21:41:07.085 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:479 | 可见行数据顺序: ['19709165', '19981259', '19721294', '19841258', '19499098']
2025-08-15 21:41:07.085 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:442 | 表格数据已设置: 13 行, 28 列
2025-08-15 21:41:07.097 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:417 | 🔧 [新架构] 排序管理器切换到表格: salary_data_2025_08_pension_employees
2025-08-15 21:41:07.115 | INFO     | src.gui.prototype.widgets.column_sort_manager:_reload_field_mapping_for_table:448 | 🔧 [新架构] 为表格 salary_data_2025_08_pension_employees 重新加载 32 个字段映射
2025-08-15 21:41:07.116 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 13 行, 耗时: 57.9ms
2025-08-15 21:41:07.116 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7926 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-15 21:41:07.117 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7939 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-15 21:41:07.118 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1723 | 🆕 [列宽保存] 未找到表格 salary_data_2025_08_pension_employees 的列宽配置
2025-08-15 21:41:07.118 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-15 21:41:07.120 | INFO     | src.gui.prototype.prototype_main_window:set_data:997 | 📊[pagination-write] 跳过写入当前页数据量(total=13)，保持 total=0，等待数据事件纠正
2025-08-15 21:41:07.120 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3887 | 🔧 [P1-1修复] 数据已成功设置到UI，使用新映射表头: 13行, 28列
2025-08-15 21:41:07.121 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 28 个, 行号起始 1, 共 13 行
2025-08-15 21:41:07.122 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3903 | 🔧 [P0-新5修复] 表头强制刷新完成: salary_data_2025_08_pension_employees, 传递参数: 28个表头
2025-08-15 21:41:07.129 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3907 | 🔧 [统一数据设置] 数据已成功设置到UI: 13行, 28列
2025-08-15 21:41:07.131 | INFO     | src.gui.widgets.pagination_widget:batch_update:472 | 📊[pagination-write] batch_update | src=_on_new_data_updated/initial_load | rid=SV-1755265267006-72c5ffdc | total: 0->13, page: 1->1, size: 50->50, pages: 1->1
2025-08-15 21:41:07.132 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3941 | ✅[pagination-state] now | page=1 / pages=1 / total=13 | rid=SV-1755265267006-72c5ffdc
2025-08-15 21:41:07.132 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3954 | 🔍 [FINAL修复] 最终调用set_pagination_state: 第1页, 记录1-13 | rid=SV-1755265267006-72c5ffdc
2025-08-15 21:41:07.133 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4166 | 🔍 [表格调试] ================== set_pagination_state 开始 ==================
2025-08-15 21:41:07.133 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4167 | 🔍 [表格调试] 收到分页状态参数: {'current_page': 1, 'page_size': 50, 'total_records': 13, 'start_record': 1, 'end_record': 13}
2025-08-15 21:41:07.134 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4168 | 🔍 [表格调试] 当前表格行数: 13
2025-08-15 21:41:07.142 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4169 | 🔍 [表格调试] 当前表格列数: 28
2025-08-15 21:41:07.144 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4171 | 🔧 [分页行号修复] 接收分页状态: 第1页, 记录1-13
2025-08-15 21:41:07.145 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1723 | 🆕 [列宽保存] 未找到表格 salary_data_2025_08_pension_employees 的列宽配置
2025-08-15 21:41:07.146 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4198 | 🔧 [P0-3修复] 分页时已恢复列宽设置: salary_data_2025_08_pension_employees
2025-08-15 21:41:07.146 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4206 | 🔧 [分页行号修复] 表格状态: 当前行数=13, 期望行数=13
2025-08-15 21:41:07.147 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 28 个, 行号起始 1, 共 13 行
2025-08-15 21:41:07.147 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_force_update_pagination_row_numbers:4286 | 🔧 [分页行号修复] 强制更新完成: 起始记录1, 共13行
2025-08-15 21:41:07.148 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3958 | 🔍 [FINAL修复] set_pagination_state最终调用完成
2025-08-15 21:41:07.212 | INFO     | src.gui.table_header_manager:cleanup_callback:162 | 🔧 [P1-1] 表格 table_3_1580491932304 已自动清理（弱引用回调）
2025-08-15 21:41:07.213 | INFO     | src.gui.table_header_manager:cleanup_callback:162 | 🔧 [P1-1] 表格 table_2_1580491932736 已自动清理（弱引用回调）
2025-08-15 21:41:07.235 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:379 | 已清理所有缓存
2025-08-15 21:41:07.235 | INFO     | src.gui.prototype.prototype_main_window:_validate_component_state_consistency:10983 | ✅ [新架构] 组件状态一致性验证通过
2025-08-15 21:41:07.238 | INFO     | src.gui.prototype.prototype_main_window:complete_navigation_transition:10946 | 🆕 [新架构] 导航迁移完成
2025-08-15 21:41:07.238 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3972 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-08-15 21:41:07.239 | INFO     | src.services.table_data_service:load_table_data:539 | 📨[data_event] published
2025-08-15 21:41:07.239 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7591 | 🆕 新架构数据加载请求已发送，等待事件系统处理
2025-08-15 21:41:07.241 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7961 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: salary_data_2025_08_pension_employees
2025-08-15 21:41:08.040 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:216 | 预测展开路径: ['工资表 > 2025年 > 08月 > 全部在职人员', '工资表 > 2025年', '工资表', '工资表 > 2025年 > 05月 > 全部在职人员', '工资表 > 2025年 > 08月 > 离休人员']
2025-08-15 21:41:08.040 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:7370 | 导航变化: 工资表 > 2025年 > 08月 > 离休人员
2025-08-15 21:41:08.040 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10489 | 🚫 [用户要求] 表格切换不显示加载条: salary_data_2025_08_pension_employees -> None
2025-08-15 21:41:08.040 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:10505 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-08-15 21:41:08.040 | INFO     | src.gui.prototype.prototype_main_window:clear_field_processing_cache:7245 | 🔧 [缓存清理] 清理了所有 0 个字段处理缓存条目
2025-08-15 21:41:08.040 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:8297 | 🔧 [表名生成] 从完整路径生成表名: ['工资表', '2025年', '08月', '离休人员'] -> salary_data_2025_08_retired_employees
2025-08-15 21:41:08.040 | INFO     | src.gui.widgets.pagination_widget:reset:584 | 分页状态已重置
2025-08-15 21:41:08.040 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:362 | 已清理表 salary_data_2025_08_retired_employees 的缓存
2025-08-15 21:41:08.040 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:11175 | 已注册 2 个表格到表头管理器
2025-08-15 21:41:08.040 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7586 | 🆕 使用新架构加载数据: salary_data_2025_08_retired_employees（通过事件系统）
2025-08-15 21:41:08.040 | INFO     | src.services.table_data_service:load_table_data:366 | 加载表格数据: 页码: 1
2025-08-15 21:41:08.040 | INFO     | src.services.table_data_service:load_table_data:390 | [缓存命中] 使用缓存数据: 第1页
2025-08-15 21:41:08.056 | INFO     | src.services.table_data_service:load_table_data:401 | 📨[data_event] publish(cache) | table=salary_data_2025_08_retired_employees | rows=2 | page=1 | size=50 | total=2 | request_id=SV-1755265268056-ee466d4f-C
2025-08-15 21:41:08.056 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3785 | 📥[data_event] received | table=salary_data_2025_08_retired_employees | request_id=SV-1755265268056-ee466d4f-C
2025-08-15 21:41:08.056 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3817 | 数据内容: 2行 x 21列
2025-08-15 21:41:08.056 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3843 | 🔧 [数据更新] 接收到cached_load操作的数据更新事件: salary_data_2025_08_retired_employees, 2行
2025-08-15 21:41:08.056 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7327 | 表 salary_data_2025_08_retired_employees 没有用户偏好设置，显示所有可见字段
2025-08-15 21:41:08.056 | INFO     | src.gui.prototype.prototype_main_window:set_data:873 | 通过公共接口设置新的表格数据(DataFrame): 2 行
2025-08-15 21:41:08.056 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7327 | 表 salary_data_2025_08_retired_employees 没有用户偏好设置，显示所有可见字段
2025-08-15 21:41:08.056 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 2行，表名: salary_data_2025_08_retired_employees
2025-08-15 21:41:08.056 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=19289006, 薪资=N/A
2025-08-15 21:41:08.056 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=19339009, 薪资=N/A
2025-08-15 21:41:08.072 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:5271 | 最大可见行数已更新: 13 -> 10
2025-08-15 21:41:08.088 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5324 | 根据数据量(2)自动调整最大可见行数为: 10
2025-08-15 21:41:08.088 | INFO     | src.modules.format_management.format_renderer:render_dataframe:107 | 🎯 [格式渲染] 隐藏字段配置: ['id', 'updated_at', 'created_at', 'sequence_number']
2025-08-15 21:41:08.088 | INFO     | src.modules.format_management.format_renderer:render_dataframe:115 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-08-15 21:41:08.088 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 增发一次性生活补贴 -> 英文名: one_time_living_allowance -> 类型: float
2025-08-15 21:41:08.088 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-08-15 21:41:08.088 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-08-15 21:41:08.088 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 备注 -> 英文名: remarks -> 类型: string
2025-08-15 21:41:08.088 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 月份 -> 英文名: month -> 类型: month_string_extract_last_two
2025-08-15 21:41:08.088 | INFO     | src.modules.format_management.field_registry:get_display_fields:1554 | 🔧 [P0-2修复] display_order转换完成: 17个字段，原始字段数: 17
2025-08-15 21:41:08.088 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | 🔧 [DEBUG] table_type=retired_employees, display_fields=17个字段
2025-08-15 21:41:08.088 | INFO     | src.modules.format_management.format_renderer:render_dataframe:211 | 🔧 [P0修复] 配置字段匹配完成: 成功匹配 17/17 个字段
2025-08-15 21:41:08.103 | INFO     | src.modules.format_management.format_renderer:render_dataframe:224 | 🔧 [P0修复] 字段重排完成: 原始17个 -> 最终17个字段
2025-08-15 21:41:08.103 | INFO     | src.modules.format_management.format_renderer:render_dataframe:240 | 🎨 [格式渲染] DataFrame格式化完成: retired_employees, 行数: 2, 列数: 17
2025-08-15 21:41:08.103 | INFO     | src.modules.format_management.unified_format_manager:format_data:367 | 🎯 [统一格式管理] 数据格式化完成: retired_employees, 行数: 2, 列数: 17
2025-08-15 21:41:08.103 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 2行数据
2025-08-15 21:41:08.103 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:52 | [数据流追踪] 开始小数据集渲染: 2行 x 17列
2025-08-15 21:41:08.103 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:112 | [数据流追踪] 小数据集渲染完成: 耗时0.0ms, 平均每行0.00ms
2025-08-15 21:41:08.103 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=2, 渲染时间=0.0ms, 策略=small_dataset
2025-08-15 21:41:08.103 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:279 | [数据流追踪] 选择小数据集渲染策略
2025-08-15 21:41:08.103 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19289006
2025-08-15 21:41:08.103 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 19339009
2025-08-15 21:41:08.103 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 2
2025-08-15 21:41:08.119 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[0]: 工号=19289006, 薪资=N/A
2025-08-15 21:41:08.119 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[1]: 工号=19339009, 薪资=N/A
2025-08-15 21:41:08.119 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:479 | 可见行数据顺序: ['19289006', '19339009']
2025-08-15 21:41:08.119 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:442 | 表格数据已设置: 2 行, 17 列
2025-08-15 21:41:08.132 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:417 | 🔧 [新架构] 排序管理器切换到表格: salary_data_2025_08_retired_employees
2025-08-15 21:41:08.132 | INFO     | src.gui.prototype.widgets.column_sort_manager:_reload_field_mapping_for_table:448 | 🔧 [新架构] 为表格 salary_data_2025_08_retired_employees 重新加载 21 个字段映射
2025-08-15 21:41:08.135 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 2 行, 耗时: 75.9ms
2025-08-15 21:41:08.135 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7926 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-15 21:41:08.135 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7939 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-08-15 21:41:08.143 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1723 | 🆕 [列宽保存] 未找到表格 salary_data_2025_08_retired_employees 的列宽配置
2025-08-15 21:41:08.143 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-15 21:41:08.144 | INFO     | src.gui.prototype.prototype_main_window:set_data:997 | 📊[pagination-write] 跳过写入当前页数据量(total=2)，保持 total=0，等待数据事件纠正
2025-08-15 21:41:08.145 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3887 | 🔧 [P1-1修复] 数据已成功设置到UI，使用新映射表头: 2行, 17列
2025-08-15 21:41:08.146 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 17 个, 行号起始 1, 共 2 行
2025-08-15 21:41:08.147 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3903 | 🔧 [P0-新5修复] 表头强制刷新完成: salary_data_2025_08_retired_employees, 传递参数: 17个表头
2025-08-15 21:41:08.147 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3907 | 🔧 [统一数据设置] 数据已成功设置到UI: 2行, 17列
2025-08-15 21:41:08.148 | INFO     | src.gui.widgets.pagination_widget:batch_update:472 | 📊[pagination-write] batch_update | src=_on_new_data_updated/cached_load | rid=SV-1755265268056-ee466d4f-C | total: 0->2, page: 1->1, size: 50->50, pages: 1->1
2025-08-15 21:41:08.149 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3941 | ✅[pagination-state] now | page=1 / pages=1 / total=2 | rid=SV-1755265268056-ee466d4f-C
2025-08-15 21:41:08.151 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3954 | 🔍 [FINAL修复] 最终调用set_pagination_state: 第1页, 记录1-2 | rid=SV-1755265268056-ee466d4f-C
2025-08-15 21:41:08.160 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4166 | 🔍 [表格调试] ================== set_pagination_state 开始 ==================
2025-08-15 21:41:08.161 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4167 | 🔍 [表格调试] 收到分页状态参数: {'current_page': 1, 'page_size': 50, 'total_records': 2, 'start_record': 1, 'end_record': 2}
2025-08-15 21:41:08.162 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4168 | 🔍 [表格调试] 当前表格行数: 2
2025-08-15 21:41:08.163 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4169 | 🔍 [表格调试] 当前表格列数: 17
2025-08-15 21:41:08.163 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4171 | 🔧 [分页行号修复] 接收分页状态: 第1页, 记录1-2
2025-08-15 21:41:08.164 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1723 | 🆕 [列宽保存] 未找到表格 salary_data_2025_08_retired_employees 的列宽配置
2025-08-15 21:41:08.164 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4198 | 🔧 [P0-3修复] 分页时已恢复列宽设置: salary_data_2025_08_retired_employees
2025-08-15 21:41:08.165 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4206 | 🔧 [分页行号修复] 表格状态: 当前行数=2, 期望行数=2
2025-08-15 21:41:08.166 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 17 个, 行号起始 1, 共 2 行
2025-08-15 21:41:08.166 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_force_update_pagination_row_numbers:4286 | 🔧 [分页行号修复] 强制更新完成: 起始记录1, 共2行
2025-08-15 21:41:08.167 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3958 | 🔍 [FINAL修复] set_pagination_state最终调用完成
2025-08-15 21:41:08.223 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:379 | 已清理所有缓存
2025-08-15 21:41:08.224 | INFO     | src.gui.prototype.prototype_main_window:_validate_component_state_consistency:10983 | ✅ [新架构] 组件状态一致性验证通过
2025-08-15 21:41:08.227 | INFO     | src.gui.prototype.prototype_main_window:complete_navigation_transition:10946 | 🆕 [新架构] 导航迁移完成
2025-08-15 21:41:08.227 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3972 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-08-15 21:41:08.227 | INFO     | src.services.table_data_service:load_table_data:426 | 📨[data_event] published(cache)
2025-08-15 21:41:08.228 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:7591 | 🆕 新架构数据加载请求已发送，等待事件系统处理
2025-08-15 21:41:08.258 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7961 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: salary_data_2025_08_retired_employees
2025-08-15 21:41:10.313 | INFO     | src.gui.prototype.widgets.column_sort_manager:handle_header_click:138 | 🆕 [多列排序] 新增列3(基本离休费)排序: 升序
2025-08-15 21:41:10.313 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_state_changed:7425 | 🆕 [新架构多列排序] 排序状态变化: 1 列
2025-08-15 21:41:10.328 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_state_changed:7433 | 🆕 [新架构多列排序] 当前排序: 基本离休费: 升序
2025-08-15 21:41:10.328 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_sort_request:7448 | 🆕 [新架构多列排序] 排序请求: salary_data_2025_08_retired_employees.basic_retirement_salary -> ascending
2025-08-15 21:41:10.328 | INFO     | src.gui.prototype.prototype_main_window:_handle_sort_applied_impl:4527 | 🔧 [P0-CRITICAL] 简化排序处理: 列3, basic_retirement_salary, ascending
2025-08-15 21:41:10.328 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:4202 | 🔧 [排序调试] 准备发布排序请求: salary_data_2025_08_retired_employees, [{'column_name': 'basic_retirement_salary', 'order': 'ascending', 'priority': 0}]
2025-08-15 21:41:10.328 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:4239 | [数据流追踪] 开始处理排序请求: 表=salary_data_2025_08_retired_employees, 排序列数=1, 页码=1
2025-08-15 21:41:10.328 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:4285 | 🔧 [排序] 使用实际页码: 1 (传入页码: 1)
2025-08-15 21:41:10.328 | INFO     | src.services.table_data_service:_handle_sort_request:154 | [排序调试] 接收到排序请求
2025-08-15 21:41:10.328 | INFO     | src.core.unified_state_manager:update_table_state:233 | 表状态已更新: salary_data_2025_08_retired_employees, 变更类型: StateChangeType.SORT_CHANGED
2025-08-15 21:41:10.328 | INFO     | src.core.unified_data_request_manager:request_table_data:212 | 开始处理数据请求
2025-08-15 21:41:10.328 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:994 | [FIX] [排序修复] 列 'basic_retirement_salary' 存在于表 'salary_data_2025_08_retired_employees' 中（类型: REAL）
2025-08-15 21:41:10.328 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:994 | [FIX] [排序修复] 列 'basic_retirement_salary' 存在于表 'salary_data_2025_08_retired_employees' 中（类型: REAL）
2025-08-15 21:41:10.328 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:805 | 正在从表 salary_data_2025_08_retired_employees 分页获取数据（支持排序）: 第1页, 每页50条, 排序=1列
2025-08-15 21:41:10.328 | INFO     | src.modules.data_storage.dynamic_table_manager:_column_exists_in_table:994 | [FIX] [排序修复] 列 'basic_retirement_salary' 存在于表 'salary_data_2025_08_retired_employees' 中（类型: REAL）
2025-08-15 21:41:10.328 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:921 | 成功从表 salary_data_2025_08_retired_employees 获取第1页数据（含排序）: 2 行，总计2行
2025-08-15 21:41:10.328 | INFO     | src.core.unified_data_request_manager:request_table_data:262 | 数据请求处理完成: 字段=21, 行数=2, 耗时=0.0ms
2025-08-15 21:41:10.328 | INFO     | src.services.table_data_service:_handle_sort_request:205 | [修复排序] 排序操作成功，发布数据更新事件: salary_data_2025_08_retired_employees, 2行
2025-08-15 21:41:10.328 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3785 | 📥[data_event] received | table=salary_data_2025_08_retired_employees | request_id=None
2025-08-15 21:41:10.328 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3817 | 数据内容: 2行 x 21列
2025-08-15 21:41:10.328 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3840 | 🔧 [关键修复] 接收到sort_change操作的数据更新事件，开始UI更新: salary_data_2025_08_retired_employees, 2行
2025-08-15 21:41:10.328 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7327 | 表 salary_data_2025_08_retired_employees 没有用户偏好设置，显示所有可见字段
2025-08-15 21:41:10.344 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3869 | 🚀 [排序优化] 检测到排序操作，标记为高优先级同步更新
2025-08-15 21:41:10.344 | INFO     | src.gui.prototype.prototype_main_window:set_data:873 | 通过公共接口设置新的表格数据(DataFrame): 2 行
2025-08-15 21:41:10.361 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:7327 | 表 salary_data_2025_08_retired_employees 没有用户偏好设置，显示所有可见字段
2025-08-15 21:41:10.361 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2714 | 🔧 [修复监控] 开始设置数据: 2行，表名: salary_data_2025_08_retired_employees
2025-08-15 21:41:10.361 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=19339009.0, 薪资=N/A
2025-08-15 21:41:10.361 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:2812 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=19289006.0, 薪资=N/A
2025-08-15 21:41:10.361 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:5271 | 最大可见行数已更新: 10 -> 10
2025-08-15 21:41:10.361 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:5324 | 根据数据量(2)自动调整最大可见行数为: 10
2025-08-15 21:41:10.361 | INFO     | src.modules.format_management.format_renderer:render_dataframe:107 | 🎯 [格式渲染] 隐藏字段配置: ['id', 'updated_at', 'created_at', 'sequence_number']
2025-08-15 21:41:10.361 | INFO     | src.modules.format_management.format_renderer:render_dataframe:115 | 🎯 [格式渲染] 无需隐藏字段（未在当前数据中找到）
2025-08-15 21:41:10.361 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 增发一次性生活补贴 -> 英文名: one_time_living_allowance -> 类型: float
2025-08-15 21:41:10.361 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 补发 -> 英文名: supplement -> 类型: float
2025-08-15 21:41:10.361 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 借支 -> 英文名: advance -> 类型: float
2025-08-15 21:41:10.361 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 备注 -> 英文名: remarks -> 类型: string
2025-08-15 21:41:10.361 | INFO     | src.modules.format_management.format_renderer:render_dataframe:133 | [关键修复] 字段: 月份 -> 英文名: month -> 类型: month_string_extract_last_two
2025-08-15 21:41:10.375 | INFO     | src.modules.format_management.field_registry:get_display_fields:1554 | 🔧 [P0-2修复] display_order转换完成: 17个字段，原始字段数: 17
2025-08-15 21:41:10.375 | INFO     | src.modules.format_management.format_renderer:render_dataframe:153 | 🔧 [DEBUG] table_type=retired_employees, display_fields=17个字段
2025-08-15 21:41:10.375 | INFO     | src.modules.format_management.format_renderer:render_dataframe:211 | 🔧 [P0修复] 配置字段匹配完成: 成功匹配 17/17 个字段
2025-08-15 21:41:10.375 | INFO     | src.modules.format_management.format_renderer:render_dataframe:224 | 🔧 [P0修复] 字段重排完成: 原始17个 -> 最终17个字段
2025-08-15 21:41:10.375 | INFO     | src.modules.format_management.format_renderer:render_dataframe:240 | 🎨 [格式渲染] DataFrame格式化完成: retired_employees, 行数: 2, 列数: 17
2025-08-15 21:41:10.375 | INFO     | src.modules.format_management.unified_format_manager:format_data:367 | 🎯 [统一格式管理] 数据格式化完成: retired_employees, 行数: 2, 列数: 17
2025-08-15 21:41:10.375 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:252 | [数据流追踪] 自动渲染策略选择: 2行数据
2025-08-15 21:41:10.375 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:52 | [数据流追踪] 开始小数据集渲染: 2行 x 17列
2025-08-15 21:41:10.375 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:112 | [数据流追踪] 小数据集渲染完成: 耗时0.0ms, 平均每行0.00ms
2025-08-15 21:41:10.375 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=2, 渲染时间=0.0ms, 策略=small_dataset
2025-08-15 21:41:10.375 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:279 | [数据流追踪] 选择小数据集渲染策略
2025-08-15 21:41:10.375 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第0行数据工号: 19339009
2025-08-15 21:41:10.375 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 第1行数据工号: 19289006
2025-08-15 21:41:10.375 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:465 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 2
2025-08-15 21:41:10.375 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[0]: 工号=19339009, 薪资=N/A
2025-08-15 21:41:10.375 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:478 | 🚨 [UI数据修复] row_data[1]: 工号=19289006, 薪资=N/A
2025-08-15 21:41:10.375 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:479 | 可见行数据顺序: ['19339009', '19289006']
2025-08-15 21:41:10.375 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:442 | 表格数据已设置: 2 行, 17 列
2025-08-15 21:41:10.387 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3088 | 表格数据设置完成: 2 行, 耗时: 25.8ms
2025-08-15 21:41:10.402 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7926 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-08-15 21:41:10.403 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7936 | 🔧 [P0-排序修复] 恢复排序指示器成功: 1 列排序
2025-08-15 21:41:10.404 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7937 | 🔧 [P0-排序修复] 排序描述: 基本离休费: 升序
2025-08-15 21:41:10.405 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1723 | 🆕 [列宽保存] 未找到表格 salary_data_2025_08_retired_employees 的列宽配置
2025-08-15 21:41:10.405 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_set_data_impl:3167 | 🔧 [修复监控] UI强制刷新完成，数据可见
2025-08-15 21:41:10.406 | INFO     | src.gui.prototype.prototype_main_window:set_data:997 | 📊[pagination-write] 跳过写入当前页数据量(total=2)，保持 total=2，等待数据事件纠正
2025-08-15 21:41:10.407 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3887 | 🔧 [P1-1修复] 数据已成功设置到UI，使用新映射表头: 2行, 17列
2025-08-15 21:41:10.407 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 17 个, 行号起始 1, 共 2 行
2025-08-15 21:41:10.408 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3903 | 🔧 [P0-新5修复] 表头强制刷新完成: salary_data_2025_08_retired_employees, 传递参数: 17个表头
2025-08-15 21:41:10.408 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3907 | 🔧 [统一数据设置] 数据已成功设置到UI: 2行, 17列
2025-08-15 21:41:10.409 | INFO     | src.gui.widgets.pagination_widget:batch_update:472 | 📊[pagination-write] batch_update | src=_on_new_data_updated/sort_change | rid=None | total: 2->2, page: 1->1, size: 50->50, pages: 1->1
2025-08-15 21:41:10.410 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3941 | ✅[pagination-state] now | page=1 / pages=1 / total=2 | rid=None
2025-08-15 21:41:10.411 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3954 | 🔍 [FINAL修复] 最终调用set_pagination_state: 第1页, 记录1-2 | rid=None
2025-08-15 21:41:10.424 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4166 | 🔍 [表格调试] ================== set_pagination_state 开始 ==================
2025-08-15 21:41:10.425 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4167 | 🔍 [表格调试] 收到分页状态参数: {'current_page': 1, 'page_size': 50, 'total_records': 2, 'start_record': 1, 'end_record': 2}
2025-08-15 21:41:10.426 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4168 | 🔍 [表格调试] 当前表格行数: 2
2025-08-15 21:41:10.426 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4169 | 🔍 [表格调试] 当前表格列数: 17
2025-08-15 21:41:10.427 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4171 | 🔧 [分页行号修复] 接收分页状态: 第1页, 记录1-2
2025-08-15 21:41:10.428 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1723 | 🆕 [列宽保存] 未找到表格 salary_data_2025_08_retired_employees 的列宽配置
2025-08-15 21:41:10.428 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4198 | 🔧 [P0-3修复] 分页时已恢复列宽设置: salary_data_2025_08_retired_employees
2025-08-15 21:41:10.429 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:4206 | 🔧 [分页行号修复] 表格状态: 当前行数=2, 期望行数=2
2025-08-15 21:41:10.429 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 17 个, 行号起始 1, 共 2 行
2025-08-15 21:41:10.430 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_force_update_pagination_row_numbers:4286 | 🔧 [分页行号修复] 强制更新完成: 起始记录1, 共2行
2025-08-15 21:41:10.430 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3958 | 🔍 [FINAL修复] set_pagination_state最终调用完成
2025-08-15 21:41:10.431 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3972 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-08-15 21:41:10.431 | INFO     | src.services.table_data_service:_handle_sort_request:225 | [修复排序] 数据更新事件已发布
2025-08-15 21:41:10.432 | INFO     | src.gui.prototype.prototype_main_window:_publish_sort_request_event:4324 | [数据流追踪] 排序事件已发布: 表=salary_data_2025_08_retired_employees, 排序列数=1, 页码=1
2025-08-15 21:41:10.440 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_on_header_clicked_impl:8175 | 🆕 [新架构多列排序] 成功处理列3(基本离休费)点击
2025-08-15 21:41:10.506 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_ui_state_after_data_set:7961 | 🔧 [P0-2修复] 数据设置后UI状态恢复完成: salary_data_2025_08_retired_employees
2025-08-15 21:41:14.801 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:2095 | MainWorkspaceArea 响应式适配: sm
2025-08-15 21:41:22.441 | INFO     | __main__:main:523 | 应用程序正常退出
2025-08-15 21:41:22.462 | INFO     | src.gui.table_header_manager:cleanup_callback:162 | 🔧 [P1-1] 表格 table_0_1580354104224 已自动清理（弱引用回调）
2025-08-15 21:58:27.184 | INFO     | src.utils.log_config:_log_initialization_info:289 | 日志系统初始化完成
2025-08-15 21:58:27.185 | INFO     | src.utils.log_config:_log_initialization_info:290 | 日志级别: INFO
2025-08-15 21:58:27.186 | INFO     | src.utils.log_config:_log_initialization_info:291 | 控制台输出: True
2025-08-15 21:58:27.186 | INFO     | src.utils.log_config:_log_initialization_info:292 | 文件输出: True
2025-08-15 21:58:27.189 | INFO     | src.utils.log_config:_log_initialization_info:298 | 日志文件路径: logs/salary_system.log
2025-08-15 21:58:27.190 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-08-15 21:59:44.830 | INFO     | src.utils.log_config:_log_initialization_info:289 | 日志系统初始化完成
2025-08-15 21:59:44.831 | INFO     | src.utils.log_config:_log_initialization_info:290 | 日志级别: INFO
2025-08-15 21:59:44.832 | INFO     | src.utils.log_config:_log_initialization_info:291 | 控制台输出: True
2025-08-15 21:59:44.833 | INFO     | src.utils.log_config:_log_initialization_info:292 | 文件输出: True
2025-08-15 21:59:44.833 | INFO     | src.utils.log_config:_log_initialization_info:298 | 日志文件路径: logs/salary_system.log
2025-08-15 21:59:44.834 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-08-15 21:59:45.601 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-15 21:59:45.601 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-15 21:59:45.602 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-08-15 21:59:45.603 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-08-15 21:59:45.604 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-08-15 21:59:45.604 | INFO     | src.modules.data_storage.database_manager:_initialize_database:102 | 开始初始化数据库...
2025-08-15 21:59:45.607 | INFO     | src.modules.data_storage.database_manager:_initialize_database:157 | 数据库初始化完成
2025-08-15 21:59:45.607 | INFO     | src.modules.data_storage.database_manager:__init__:98 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-08-15 21:59:45.615 | WARNING  | src.modules.data_storage.dynamic_table_manager:_perform_startup_database_validation:116 | 🔧 [P1修复] 启动时数据库就绪检查失败
2025-08-15 21:59:45.616 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:107 | 动态表管理器初始化完成
2025-08-15 21:59:45.618 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:525 | 🔧 [P0修复] 开始创建灵活异动表: change_data_2025_12_test_employees
2025-08-15 21:59:45.618 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:526 | 🔧 [P0修复] 用户Excel列数: 9
2025-08-15 21:59:45.619 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:527 | 🔧 [P0修复] 用户Excel列名: ['工号', '姓名', '部门名称', '2025年岗位工资', '2025年薪级工资', '津贴', '应发工资', 'data_source', 'import_time']
2025-08-15 21:59:45.619 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 工号
2025-08-15 21:59:45.620 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 姓名
2025-08-15 21:59:45.620 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 部门名称
2025-08-15 21:59:45.620 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 2025年岗位工资
2025-08-15 21:59:45.621 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 2025年薪级工资
2025-08-15 21:59:45.622 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 津贴
2025-08-15 21:59:45.623 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 应发工资
2025-08-15 21:59:45.624 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: data_source
2025-08-15 21:59:45.625 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: import_time
2025-08-15 21:59:45.672 | ERROR    | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:584 | 🔧 [P0修复] 创建灵活异动表异常: 'ConfigManager' object has no attribute 'create_table'
2025-08-15 21:59:45.677 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-15 21:59:45.678 | WARNING  | src.modules.data_storage.dynamic_table_manager:_perform_startup_database_validation:116 | 🔧 [P1修复] 启动时数据库就绪检查失败
2025-08-15 21:59:45.679 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:107 | 动态表管理器初始化完成
2025-08-15 21:59:45.683 | ERROR    | src.modules.data_storage.dynamic_table_manager:get_all_table_names:2654 | 🔧 [P0修复] 获取所有表名失败: 'ConfigManager' object has no attribute 'get_all_table_names'
2025-08-15 21:59:45.683 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-15 21:59:45.683 | WARNING  | src.modules.data_storage.dynamic_table_manager:_perform_startup_database_validation:116 | 🔧 [P1修复] 启动时数据库就绪检查失败
2025-08-15 21:59:45.683 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:107 | 动态表管理器初始化完成
2025-08-15 21:59:47.227 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_table_list:1225 | 🔧 [深度修复] 数据库就绪检查失败，使用降级查询
2025-08-15 21:59:47.227 | ERROR    | src.modules.data_storage.dynamic_table_manager:_query_table_metadata_by_type:1328 | 🔧 [深度修复] 查询表元数据失败: 'ConfigManager' object has no attribute 'execute_query'
2025-08-15 21:59:47.229 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1246 | 🔧 [深度修复] 找到 0 个匹配类型 'change_data' 的表 (尝试 5/5)
2025-08-15 22:02:53.603 | INFO     | src.utils.log_config:_log_initialization_info:289 | 日志系统初始化完成
2025-08-15 22:02:53.603 | INFO     | src.utils.log_config:_log_initialization_info:290 | 日志级别: INFO
2025-08-15 22:02:53.603 | INFO     | src.utils.log_config:_log_initialization_info:291 | 控制台输出: True
2025-08-15 22:02:53.604 | INFO     | src.utils.log_config:_log_initialization_info:292 | 文件输出: True
2025-08-15 22:02:53.605 | INFO     | src.utils.log_config:_log_initialization_info:298 | 日志文件路径: logs/salary_system.log
2025-08-15 22:02:53.607 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-08-15 22:02:54.438 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-15 22:02:54.438 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-15 22:02:54.438 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-08-15 22:02:54.438 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-08-15 22:02:54.438 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-08-15 22:02:54.438 | INFO     | src.modules.data_storage.database_manager:_initialize_database:102 | 开始初始化数据库...
2025-08-15 22:02:54.450 | INFO     | src.modules.data_storage.database_manager:_initialize_database:157 | 数据库初始化完成
2025-08-15 22:02:54.450 | INFO     | src.modules.data_storage.database_manager:__init__:98 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-08-15 22:02:54.462 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:107 | 动态表管理器初始化完成
2025-08-15 22:02:54.463 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:525 | 🔧 [P0修复] 开始创建灵活异动表: change_data_2025_12_test_employees
2025-08-15 22:02:54.463 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:526 | 🔧 [P0修复] 用户Excel列数: 9
2025-08-15 22:02:54.464 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:527 | 🔧 [P0修复] 用户Excel列名: ['工号', '姓名', '部门名称', '2025年岗位工资', '2025年薪级工资', '津贴', '应发工资', 'data_source', 'import_time']
2025-08-15 22:02:54.464 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 工号
2025-08-15 22:02:54.465 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 姓名
2025-08-15 22:02:54.465 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 部门名称
2025-08-15 22:02:54.466 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 2025年岗位工资
2025-08-15 22:02:54.467 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 2025年薪级工资
2025-08-15 22:02:54.468 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 津贴
2025-08-15 22:02:54.468 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 应发工资
2025-08-15 22:02:54.469 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: data_source
2025-08-15 22:02:54.470 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: import_time
2025-08-15 22:02:54.519 | INFO     | src.modules.data_storage.dynamic_table_manager:_create_table_from_schema:1052 | 成功创建表: change_data_2025_12_test_employees
2025-08-15 22:02:54.522 | INFO     | src.modules.data_storage.dynamic_table_manager:_create_index:1089 | 成功创建索引: idx_change_data_2025_12_test_employees_created on change_data_2025_12_test_employees
2025-08-15 22:02:54.523 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:562 | 🔧 [P0修复] 灵活异动表创建成功: change_data_2025_12_test_employees
2025-08-15 22:02:54.525 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:563 | 🔧 [P0修复] 总字段数: 12 (系统字段: 3, 用户字段: 9)
2025-08-15 22:02:54.526 | ERROR    | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:584 | 🔧 [P0修复] 创建灵活异动表异常: DynamicTableManager._record_table_metadata() takes 2 positional arguments but 4 were given
2025-08-15 22:02:54.528 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-15 22:02:54.583 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:107 | 动态表管理器初始化完成
2025-08-15 22:02:54.583 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-15 22:02:54.583 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:107 | 动态表管理器初始化完成
2025-08-15 22:02:54.596 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_table_list:1236 | 🔧 [深度修复] 检测到 change_data 类型的实际表但元数据缺失，执行强制同步后重试
2025-08-15 22:02:54.901 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_table_list:1236 | 🔧 [深度修复] 检测到 change_data 类型的实际表但元数据缺失，执行强制同步后重试
2025-08-15 22:02:55.205 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_table_list:1236 | 🔧 [深度修复] 检测到 change_data 类型的实际表但元数据缺失，执行强制同步后重试
2025-08-15 22:02:55.511 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_table_list:1236 | 🔧 [深度修复] 检测到 change_data 类型的实际表但元数据缺失，执行强制同步后重试
2025-08-15 22:02:55.882 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1246 | 🔧 [深度修复] 找到 0 个匹配类型 'change_data' 的表 (尝试 5/5)
2025-08-15 22:05:31.361 | INFO     | src.utils.log_config:_log_initialization_info:289 | 日志系统初始化完成
2025-08-15 22:05:31.361 | INFO     | src.utils.log_config:_log_initialization_info:290 | 日志级别: INFO
2025-08-15 22:05:31.361 | INFO     | src.utils.log_config:_log_initialization_info:291 | 控制台输出: True
2025-08-15 22:05:31.361 | INFO     | src.utils.log_config:_log_initialization_info:292 | 文件输出: True
2025-08-15 22:05:31.361 | INFO     | src.utils.log_config:_log_initialization_info:298 | 日志文件路径: logs/salary_system.log
2025-08-15 22:05:31.361 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-08-15 22:05:32.528 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-15 22:05:32.529 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-15 22:05:32.530 | INFO     | src.modules.system_config.config_manager:load_config:361 | 正在加载配置文件: config.json
2025-08-15 22:05:32.531 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:273 | 配置文件验证通过
2025-08-15 22:05:32.531 | INFO     | src.modules.system_config.config_manager:load_config:373 | 配置文件加载成功
2025-08-15 22:05:32.531 | INFO     | src.modules.data_storage.database_manager:_initialize_database:102 | 开始初始化数据库...
2025-08-15 22:05:32.554 | INFO     | src.modules.data_storage.database_manager:_initialize_database:157 | 数据库初始化完成
2025-08-15 22:05:32.555 | INFO     | src.modules.data_storage.database_manager:__init__:98 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-08-15 22:05:32.561 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:107 | 动态表管理器初始化完成
2025-08-15 22:05:32.570 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:525 | 🔧 [P0修复] 开始创建灵活异动表: change_data_2025_12_test_employees
2025-08-15 22:05:32.574 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:526 | 🔧 [P0修复] 用户Excel列数: 9
2025-08-15 22:05:32.577 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:527 | 🔧 [P0修复] 用户Excel列名: ['工号', '姓名', '部门名称', '2025年岗位工资', '2025年薪级工资', '津贴', '应发工资', 'data_source', 'import_time']
2025-08-15 22:05:32.579 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 工号
2025-08-15 22:05:32.580 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 姓名
2025-08-15 22:05:32.645 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 部门名称
2025-08-15 22:05:32.650 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 2025年岗位工资
2025-08-15 22:05:32.651 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 2025年薪级工资
2025-08-15 22:05:32.652 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 津贴
2025-08-15 22:05:32.652 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: 应发工资
2025-08-15 22:05:32.652 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: data_source
2025-08-15 22:05:32.669 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:545 | 🔧 [P0修复] 添加用户字段: import_time
2025-08-15 22:05:32.691 | INFO     | src.modules.data_storage.dynamic_table_manager:_create_table_from_schema:1043 | 成功创建表: change_data_2025_12_test_employees
2025-08-15 22:05:32.816 | INFO     | src.modules.data_storage.dynamic_table_manager:_create_index:1080 | 成功创建索引: idx_change_data_2025_12_test_employees_created on change_data_2025_12_test_employees
2025-08-15 22:05:32.850 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:562 | 🔧 [P0修复] 灵活异动表创建成功: change_data_2025_12_test_employees
2025-08-15 22:05:32.851 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:563 | 🔧 [P0修复] 总字段数: 12 (系统字段: 3, 用户字段: 9)
2025-08-15 22:05:32.859 | INFO     | src.modules.data_storage.dynamic_table_manager:create_flexible_change_data_table:567 | 🔧 [P0修复] 异动表元数据已记录: change_data_2025_12_test_employees
2025-08-15 22:05:32.928 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-15 22:05:32.942 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:107 | 动态表管理器初始化完成
2025-08-15 22:05:33.062 | INFO     | src.modules.system_config.config_manager:__init__:334 | 配置管理器初始化完成，配置文件: config.json
2025-08-15 22:05:33.074 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:107 | 动态表管理器初始化完成
2025-08-15 22:05:33.130 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:1243 | 🔧 [深度修复] 找到 1 个匹配类型 'change_data' 的表 (尝试 1/5)
