## 数据导航区域问题梳理与优化建议（2025-08-14）

### 背景
- 本文梳理本次对话与代码/日志分析，聚焦“数据导航区域”的样式与功能问题，以及改进建议。
- 参考文件与路径：
  - 日志：`logs/salary_system.log`
  - 截图：`logs/navigate1.png`（导入前）、`logs/navigate2.png`（导入后）
  - 主入口：`main.py`
  - 关键组件：
    - 导航面板：`src/gui/prototype/widgets/enhanced_navigation_panel.py`
    - 标签容器：`src/gui/prototype/widgets/tabbed_navigation_panel.py`
    - 主窗口：`src/gui/prototype/prototype_main_window.py`
    - 分页控件：`src/gui/widgets/pagination_widget.py`
    - 表管理：`src/modules/data_storage/dynamic_table_manager.py`

---

### 核心结论（TL;DR）
- **自动选择最新数据失败**：导航树月份标签为“m月”，而“最新期”路径使用“0Mm月”，路径不一致导致无法选中、无法联动数据加载。
- **导航仅构建单一年份**：硬编码“2025年”，导入其他年份后导航不显示。
- **搜索过滤不可见链路**：父节点被隐藏导致命中的子节点不可见。
- **样式不统一**：Emoji 图标在 Win10 下对齐不齐、选中态加粗引起抖动、左右留白偏大。
- **异动表为静态**：未与数据库元数据联动，导入后仍为样例结构。
- **空库体验欠佳**：重试与兜底日志过多，无明确导入引导。

---

### 问题清单

- 功能问题
  - 统一路径与标签格式：
    - 最新路径生成为“`工资表 > YYYY年 > 0Mm月 > 全部在职人员`”（两位数月份）。
    - 导航树构建月份为“`m月`”（一位数月份）。
    - 结果：`select_path(latest_path)` 找不到节点，不触发 `navigation_changed`，数据区不刷新。
    - 位置：
      - `src/modules/data_storage/dynamic_table_manager.py`（`get_latest_salary_data_path`）
      - `src/gui/prototype/widgets/enhanced_navigation_panel.py`（`_execute_salary_data_load`、`_load_fallback_salary_data`）
  - 多年份未构建：仅针对传入的“2025年”构建；导入“2024/2026年”不会出现在导航。
  - 搜索过滤隐藏父链：`filter_items` 逐项隐藏，无“命中子项时显式父链”的逻辑。
  - “异动表”静态：无从元数据动态加载，无法自动选中“最新期”。
  - 空库重试噪声：首次启动或未导入数据时，多次重试 + 兜底日志频发，用户无明确导入提示。

- 样式问题
  - Emoji 图标对齐差，选中态加粗导致文本宽度变化产生抖动。
  - 左侧内边距偏大，窄屏下与外层卡片叠加影响右侧表格空间。
  - 统计条 `stats_label` 字号过小、灰底灰字对比度不足。

- 其他问题
  - 状态持久化路径相对 `state/user/...`，若工作目录变化，文件位置不稳定；应基于 `APP_ROOT` 绝对化。
  - 多处中文文案硬编码，不利后续国际化与统一提示样式。

---

### 关键证据与定位

- 日志（导入前多次重试/兜底）：
```text
WARNING | ... get_latest_salary_data_path:1436 | 未找到任何工资数据表
INFO    | ... _get_latest_path_with_retry:1366 | 检查表状态失败，1.0s后重试...
WARNING | ... auto_select_latest_data:1181     | 多次重试后仍未找到最新工资数据路径
```

- 最新路径使用两位数月份：
```text
src/modules/data_storage/dynamic_table_manager.py
path = f"工资表 > {latest_year}年 > {latest_month:02d}月 > {display_name}"
```

- 导航树月份为一位数：
```text
src/gui/prototype/widgets/enhanced_navigation_panel.py
month_path = self.tree_widget.add_navigation_item(year_path, f"{month}月", "📊")
```

- 兜底数据同样是一位数月份：
```text
src/gui/prototype/widgets/enhanced_navigation_panel.py
month_05_path = self.tree_widget.add_navigation_item(year_path, "5月", "📊")
```

- 仅构建单一年份（代码注释明确）：
```text
src/gui/prototype/widgets/enhanced_navigation_panel.py
# 注意：目前只处理一个年份节点，如果未来需要支持多年份，这里的逻辑需要调整
```

---

### 流程与故障点（可视化）

- 自动选择最新数据失败原因
```mermaid
flowchart TD
A[启动后延迟自动选择] --> B[查询最新表路径 get_latest_salary_data_path]
B -->|返回 '工资表 > YYYY年 > 0Mm月 > 全部在职人员'| C[EnhancedNavigationPanel 构建树]
C -->|月份标签 'm月'| D{路径能匹配?}
D -- 否(05月 vs 5月) --> E[无法 select_path]
E --> F[未触发 navigation_changed]
F --> G[右侧数据未加载/高亮缺失]
D -- 是 --> H[select_path 成功并触发加载]
```

---
- 导入后未自动选中新数据的时序
```mermaid
sequenceDiagram
participant UI as EnhancedNavigationPanel
participant DTM as DynamicTableManager
UI->>DTM: get_latest_salary_data_path()
DTM-->>UI: 工资表 > 2025年 > 05月 > 全部在职人员
UI->>UI: 构建月份节点 '5月'
UI->>UI: select_path('... > 05月 > ...')
UI-->>UI: 未找到对应节点 → 不触发 navigation_changed
note right of UI: 界面无高亮，表格不刷新
```

---

### 改进建议（按优先级）

1) 必修复
- 统一月份格式（两种方案二选一）：
  - 将导航树月份改为两位数：`f"{month:02d}月"`，与 `get_latest_salary_data_path` 对齐；
  - 或将 `get_latest_salary_data_path` 的月份改为不补零，保持与现有树一致。
- 多年份构建：遍历 `get_navigation_tree_data()` 全年份，移除硬编码“2025年/2024年”。
- 搜索父链可见性：命中子项时展开并显示其父链；清空搜索时恢复显示。

2) 体验与样式
- 用 `QIcon`（本地 PNG/SVG）替换 Emoji，统一尺寸与基线；
- 选中态去掉加粗，仅用背景色/前景色/左侧强调条表示，防抖动；
- 调小左侧内边距，窄屏启用抽屉模式并确保遮罩层 `_overlay` 正常显示；
- 空库显式引导：在导航顶部/状态栏提示“请先导入数据”，减少重试频次与日志噪声。

3) 架构一致性
- “异动表”导航从元数据动态加载，与“工资表”一致支持“最新期自动选择”；
- 状态文件路径基于 `APP_ROOT` 绝对化，避免工作目录变更导致的保存位置漂移；
- 中文文案统一管理，便于后续国际化/主题化。

---

### 影响范围（文件与功能）
- `src/modules/data_storage/dynamic_table_manager.py`：最新路径格式、元数据读取。
- `src/gui/prototype/widgets/enhanced_navigation_panel.py`：月份标签构建、多年份遍历、搜索过滤、状态恢复。
- `src/gui/prototype/widgets/tabbed_navigation_panel.py`：根过滤、抽屉模式、Tab 首次激活逻辑。
- `src/gui/widgets/pagination_widget.py`：样式与交互一致性（次要）。
- `main.py`：整体启动样式与异常处理（旁系）。

---

### TODO（执行清单）
1. 统一月份格式（树或路径一侧改动，选其一，优先改树为两位数）。
2. 遍历所有年份构建导航，删除“2025/2024”硬编码片段。
3. 改造搜索：匹配时显式父链、自动展开；清空时恢复。
4. 替换 Emoji 为本地图标，调整选中态样式与内边距。
5. 空库时减少重试频次，在 UI 显示导入引导。
6. “异动表”改为动态数据源，打通“最新期自动选择”。
7. 导航状态文件路径绝对化；统一中文文案管理。

---

### 备注
- 开发与测试优先级：月份一致性、多年份构建、搜索父链 → 首批；样式与引导 → 第二批；异动表动态化与文案/路径治理 → 第三批。
- Windows10 + PowerShell 环境下测试 Emoji 与字体呈现差异明显，建议统一为 `QIcon` 方案。


