# 日志样例-分页端到端追踪

```
📍[page-change] 入口 | table=在职人员 | target_page=6 | request_id=PG-1723456789012-ab12cd34
📨[data_event] publish | table=在职人员 | rows=50 | page=6 | size=50 | total=1396 | request_id=PG-1723456789012-ab12cd34
📥[data_event] received | table=在职人员 | request_id=PG-1723456789012-ab12cd34
📊[pagination-write] batch_update | src=_on_new_data_updated/page_change | rid=PG-1723456789012-ab12cd34 | total: 1396->1396, page: 5->6, size: 50->50, pages: 28->28
✅[pagination-state] now | page=6 / pages=28 / total=1396 | rid=PG-1723456789012-ab12cd34
```

并发丢弃旧请求：
```
📍[page-change] 入口 | table=在职人员 | target_page=5 | request_id=PG-...-X
📍[page-change] 入口 | table=在职人员 | target_page=6 | request_id=PG-...-Y
📨[data_event] publish | ... | request_id=PG-...-X
📥[data_event] received | ... | request_id=PG-...-X
🧰[refresh-token] discard | table=在职人员 | rid=PG-...-X
📨[data_event] publish | ... | request_id=PG-...-Y
📥[data_event] received | ... | request_id=PG-...-Y
📊[pagination-write] batch_update | src=... | rid=PG-...-Y | ...
✅[pagination-state] now | page=6 / ... | rid=PG-...-Y
```

