#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
P0级修复验证测试 - 用户选择异动表处理逻辑

测试用户选择异动表时系统的处理逻辑是否正确
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.modules.data_storage.database_manager import DatabaseManager
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.modules.data_import.multi_sheet_importer import MultiSheetImporter
from src.modules.system_config.config_manager import ConfigManager
from src.core.unified_data_request_manager import UnifiedDataRequestManager
from src.core.data_request import DataRequest

def test_user_selected_change_table():
    """测试用户选择异动表的处理逻辑"""
    print("=" * 80)
    print("🔧 [P0修复] 测试用户选择异动表的处理逻辑")
    print("=" * 80)
    
    try:
        # 1. 初始化组件
        config_manager = ConfigManager()
        db_manager = DatabaseManager(config_manager)
        table_manager = DynamicTableManager(db_manager, config_manager)
        
        # 2. 测试灵活异动表创建
        print("\n1. 测试灵活异动表创建...")
        test_columns = ["工号", "姓名", "部门名称", "2025年岗位工资", "2025年薪级工资", "津贴", "应发工资"]
        table_name = "change_data_2025_12_test_flexible"
        
        success = table_manager.create_flexible_change_data_table(
            table_name=table_name,
            columns=test_columns,
            user_selected=True
        )
        
        if success:
            print(f"✅ 灵活异动表创建成功: {table_name}")
            print(f"   用户字段数: {len(test_columns)}")
            print(f"   用户字段: {test_columns}")
        else:
            print(f"❌ 灵活异动表创建失败: {table_name}")
            
        # 3. 测试表名映射逻辑
        print("\n2. 测试表名映射逻辑...")
        request_manager = UnifiedDataRequestManager(db_manager, table_manager)
        
        # 测试异动表请求
        change_request = DataRequest(
            table_name="change_data_2025_12_nonexistent",
            page=1,
            page_size=50
        )
        
        print(f"   原始请求表名: {change_request.table_name}")
        
        # 验证请求（这会触发表名映射逻辑）
        validation_result = request_manager._validate_request(change_request)
        
        if validation_result["valid"]:
            print(f"✅ 表名验证成功")
            print(f"   最终表名: {change_request.table_name}")
            print(f"   表类型: {request_manager._get_table_type_from_name(change_request.table_name)}")
        else:
            print(f"❌ 表名验证失败: {validation_result['error']}")
            
        # 4. 测试多Sheet导入器的用户选择逻辑
        print("\n3. 测试多Sheet导入器的用户选择逻辑...")
        importer = MultiSheetImporter(table_manager)
        
        # 模拟用户选择异动记录表
        importer.sheet_configs["target_table_template"] = "salary_changes"
        
        # 测试员工类型推断
        test_headers = ["工号", "姓名", "部门名称", "2025年岗位工资", "2025年薪级工资"]
        inferred_type = importer._infer_employee_type_from_headers(test_headers)
        
        print(f"   测试表头: {test_headers}")
        print(f"   推断员工类型: {inferred_type}")
        
        if inferred_type:
            print(f"✅ 员工类型推断成功: {inferred_type}")
        else:
            print(f"⚠️  无法推断员工类型，将使用flexible后缀")
            
        print("\n" + "=" * 80)
        print("🔧 [P0修复] 测试完成")
        print("=" * 80)
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_table_type_detection():
    """测试表类型检测逻辑"""
    print("\n🔧 [P0修复] 测试表类型检测逻辑")
    print("-" * 50)
    
    try:
        config_manager = ConfigManager()
        db_manager = DatabaseManager(config_manager)
        table_manager = DynamicTableManager(db_manager, config_manager)
        request_manager = UnifiedDataRequestManager(db_manager, table_manager)
        
        test_cases = [
            "change_data_2025_12_active_employees",
            "salary_data_2025_12_active_employees", 
            "change_data_2025_12_flexible",
            "salary_data_2025_08_retired_employees"
        ]
        
        for table_name in test_cases:
            table_type = request_manager._get_table_type_from_name(table_name)
            print(f"   表名: {table_name}")
            print(f"   类型: {table_type}")
            print()
            
        return True
        
    except Exception as e:
        print(f"❌ 表类型检测测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🔧 [P0修复] 开始验证修复效果...")
    
    success1 = test_user_selected_change_table()
    success2 = test_table_type_detection()
    
    if success1 and success2:
        print("\n✅ 所有P0修复验证测试通过！")
    else:
        print("\n❌ 部分P0修复验证测试失败，需要进一步检查")
