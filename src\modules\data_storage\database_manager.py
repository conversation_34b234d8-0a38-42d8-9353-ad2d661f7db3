"""
月度工资异动处理系统 - 数据库管理模块

本模块负责SQLite数据库的连接和基础管理，包括：
- 数据库连接池管理
- 事务管理
- 基础CRUD操作
- 数据库结构管理
- 连接状态监控

特性：
- 线程安全的连接管理
- 自动重连机制
- 连接池优化
- 事务回滚支持

创建时间: 2025-06-15 (CREATIVE阶段 Day 1)
更新时间: 2025-06-15 (集成系统配置模块)
"""

import sqlite3
import threading
import time
from contextlib import contextmanager
from pathlib import Path
from typing import Optional, Dict, Any, List, Tuple, Union
from dataclasses import dataclass

# 导入项目内部模块
from src.utils.log_config import setup_logger
from src.utils.logging_utils import log_throttle, redact
from src.modules.system_config import ConfigManager

# 模块logger
logger = setup_logger("data_storage.database_manager")

@dataclass
class ConnectionInfo:
    """数据库连接信息"""
    db_path: Path
    created_at: float
    last_accessed: float
    thread_id: int
    is_active: bool = True

class DatabaseManager:
    """
    数据库管理器
    
    负责SQLite数据库的连接管理、事务处理和基础操作
    """
    
    def __init__(self, db_path: Optional[Union[str, Path]] = None, config_manager: Optional[ConfigManager] = None):
        """
        初始化数据库管理器
        
        Args:
            db_path: 数据库文件路径，如果为None则从配置获取
            config_manager: 配置管理器实例
        """
        self.logger = setup_logger("data_storage.database_manager")
        
        # 获取配置管理器
        self.config_manager = config_manager or ConfigManager()
        
        # 确定数据库路径
        if db_path is None:
            db_name = self.config_manager.get_config_value("database.db_name", "salary_system.db")
            db_path_config = self.config_manager.get_config_value("database.db_path", "data/db")
            
            # 获取项目根目录
            current_file = Path(__file__)
            project_root = current_file.parent.parent.parent.parent
            
            # 如果配置中指定了db_path，使用配置的路径，否则使用默认的data/db
            if db_path_config:
                self.db_path = project_root / db_path_config / db_name
            else:
                self.db_path = project_root / "data" / "db" / db_name
        else:
            self.db_path = Path(db_path)
        
        # 确保数据库目录存在
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 连接池
        self._connections: Dict[int, sqlite3.Connection] = {}
        self._connection_info: Dict[int, ConnectionInfo] = {}
        self._lock = threading.Lock()
        
        # 配置参数
        self.connection_timeout = self.config_manager.get_config_value("database.connection_timeout", 30)
        self.backup_enabled = self.config_manager.get_config_value("database.backup_enabled", True)
        
        # 初始化数据库
        self._initialize_database()
        
        self.logger.info(f"数据库管理器初始化完成，数据库路径: {self.db_path}")
    
    def _initialize_database(self) -> None:
        """初始化数据库，创建基础表结构"""
        self.logger.info("开始初始化数据库...")
        
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # 创建系统配置表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS system_config (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        key TEXT UNIQUE NOT NULL,
                        value TEXT NOT NULL,
                        description TEXT,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # 创建数据表元信息表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS table_metadata (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        table_name TEXT UNIQUE NOT NULL,
                        table_type TEXT NOT NULL,  -- salary_data, change_log, template_info等
                        schema_version INTEGER DEFAULT 1,
                        description TEXT,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        is_active BOOLEAN DEFAULT 1
                    )
                """)
                
                # 创建数据备份记录表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS backup_records (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        backup_type TEXT NOT NULL,  -- full, incremental
                        backup_path TEXT NOT NULL,
                        file_size INTEGER,
                        records_count INTEGER,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        status TEXT DEFAULT 'completed'  -- completed, failed, in_progress
                    )
                """)
                
                # 插入初始配置
                cursor.execute("""
                    INSERT OR IGNORE INTO system_config (key, value, description)
                    VALUES 
                    ('db_version', '1.0', '数据库版本'),
                    ('created_by', '月度工资异动处理系统', '数据库创建者'),
                    ('initialized_at', datetime('now'), '初始化时间')
                """)
                
                conn.commit()
                self.logger.info("数据库初始化完成")
                
        except Exception as e:
            self.logger.error(f"数据库初始化失败: {str(e)}")
            raise
    
    @contextmanager
    def get_connection(self, timeout: Optional[int] = None):
        """
        获取数据库连接（上下文管理器）
        
        Args:
            timeout: 连接超时时间（秒）
            
        Yields:
            sqlite3.Connection: 数据库连接对象
        """
        thread_id = threading.get_ident()
        
        if timeout is None:
            timeout = self.connection_timeout
        
        connection = None
        try:
            connection = self._get_or_create_connection(thread_id)
            yield connection
            
        except Exception as e:
            if connection:
                connection.rollback()
            self.logger.error(f"数据库连接使用失败: {str(e)}")
            raise
        finally:
            self._update_last_accessed(thread_id)
    
    def _get_or_create_connection(self, thread_id: int) -> sqlite3.Connection:
        """获取或创建数据库连接"""
        with self._lock:
            if thread_id in self._connections:
                # 检查连接是否仍然有效
                conn = self._connections[thread_id]
                try:
                    conn.execute("SELECT 1")
                    return conn
                except sqlite3.Error:
                    # 连接已失效，需要重新创建
                    del self._connections[thread_id]
                    del self._connection_info[thread_id]
            
            # 创建新连接
            conn = sqlite3.connect(
                str(self.db_path),
                timeout=self.connection_timeout,
                check_same_thread=False
            )
            
            # 设置行工厂，返回字典形式的结果
            conn.row_factory = sqlite3.Row
            
            # 启用外键约束
            conn.execute("PRAGMA foreign_keys = ON")
            
            # 设置WAL模式（写前日志）提高并发性能
            conn.execute("PRAGMA journal_mode = WAL")
            
            # 存储连接信息
            self._connections[thread_id] = conn
            self._connection_info[thread_id] = ConnectionInfo(
                db_path=self.db_path,
                created_at=time.time(),
                last_accessed=time.time(),
                thread_id=thread_id
            )
            
            self.logger.debug(f"为线程 {thread_id} 创建新数据库连接")
            return conn
    
    def _update_last_accessed(self, thread_id: int) -> None:
        """更新连接最后访问时间"""
        if thread_id in self._connection_info:
            self._connection_info[thread_id].last_accessed = time.time()
    
    def close_connection(self, thread_id: Optional[int] = None) -> None:
        """
        关闭指定线程的数据库连接
        
        Args:
            thread_id: 线程ID，如果为None则关闭当前线程的连接
        """
        if thread_id is None:
            thread_id = threading.get_ident()
        
        with self._lock:
            if thread_id in self._connections:
                try:
                    self._connections[thread_id].close()
                    del self._connections[thread_id]
                    del self._connection_info[thread_id]
                    self.logger.debug(f"已关闭线程 {thread_id} 的数据库连接")
                except Exception as e:
                    self.logger.error(f"关闭数据库连接失败: {str(e)}")
    
    def close_all_connections(self) -> None:
        """关闭所有数据库连接"""
        with self._lock:
            for thread_id, conn in list(self._connections.items()):
                try:
                    conn.close()
                    del self._connections[thread_id]
                    del self._connection_info[thread_id]
                except Exception as e:
                    self.logger.error(f"关闭连接失败 (线程 {thread_id}): {str(e)}")
            
            self.logger.info("所有数据库连接已关闭")
    
    def execute_query(self, query: str, params: Optional[tuple] = None) -> List[Dict[str, Any]]:
        """
        执行查询操作
        
        Args:
            query: SQL查询语句
            params: 查询参数
            
        Returns:
            List[Dict[str, Any]]: 查询结果列表
        """
        try:
            import os as _os, time as _t
            t0 = _t.time()
            with self.get_connection() as conn:
                cursor = conn.cursor()
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)

                # 将Row对象转换为字典
                results = []
                for row in cursor.fetchall():
                    results.append(dict(row))

                elapsed_ms = int((_t.time() - t0) * 1000)
                slow_ms = int(_os.getenv('LOG_SLOW_SQL_MS', '300'))
                if elapsed_ms >= slow_ms:
                    # 慢SQL留痕（简要）
                    self.logger.info(f"[PERF][SLOW_SQL] query_time={elapsed_ms}ms, rows={len(results)}")

                # 详日志受策略控制
                # 详细SQL仅在开关开启时输出，且节流
                if _os.getenv('LOG_SQL_DETAIL', '0') == '1' and log_throttle('db-sql-detail', 2.0):
                    try:
                        if params is not None:
                            redacted_params = tuple(redact(str(v)) for v in params)
                            self.logger.debug(f"SQL(detail): {query} | params={redacted_params} | {elapsed_ms}ms")
                        else:
                            self.logger.debug(f"SQL(detail): {query} | {elapsed_ms}ms")
                    except Exception:
                        pass

                if log_throttle('db-query-success', 1.0):
                    self.logger.debug(f"查询执行成功，返回 {len(results)} 条记录")
                return results

        except Exception as e:
            self.logger.error(f"查询执行失败: {str(e)}")
            raise
    
    def execute_update(self, query: str, params: Optional[tuple] = None) -> int:
        """
        执行更新操作（INSERT, UPDATE, DELETE）
        
        Args:
            query: SQL语句
            params: 参数
            
        Returns:
            int: 受影响的行数
        """
        try:
            import os as _os, time as _t
            t0 = _t.time()
            with self.get_connection() as conn:
                cursor = conn.cursor()
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)

                conn.commit()
                affected_rows = cursor.rowcount

                elapsed_ms = int((_t.time() - t0) * 1000)
                slow_ms = int(_os.getenv('LOG_SLOW_SQL_MS', '300'))
                if elapsed_ms >= slow_ms:
                    self.logger.info(f"[PERF][SLOW_SQL] update_time={elapsed_ms}ms, affected={affected_rows}")

                # 详细SQL仅在开关开启时输出，且节流
                if _os.getenv('LOG_SQL_DETAIL', '0') == '1' and log_throttle('db-sql-detail', 2.0):
                    try:
                        if params is not None:
                            redacted_params = tuple(redact(str(v)) for v in params)
                            self.logger.debug(f"SQL(detail): {query} | params={redacted_params} | {elapsed_ms}ms")
                        else:
                            self.logger.debug(f"SQL(detail): {query} | {elapsed_ms}ms")
                    except Exception:
                        pass

                if log_throttle('db-update-success', 1.0):
                    self.logger.debug(f"更新执行成功，影响 {affected_rows} 行")
                return affected_rows

        except Exception as e:
            self.logger.error(f"更新执行失败: {str(e)}")
            raise
    
    def execute_batch(self, query: str, params_list: List[tuple]) -> int:
        """
        批量执行SQL语句
        
        Args:
            query: SQL语句
            params_list: 参数列表
            
        Returns:
            int: 总共影响的行数
        """
        try:
            import os as _os, time as _t
            t0 = _t.time()
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.executemany(query, params_list)
                conn.commit()

                affected_rows = cursor.rowcount

                elapsed_ms = int((_t.time() - t0) * 1000)
                slow_ms = int(_os.getenv('LOG_SLOW_SQL_MS', '300'))
                if elapsed_ms >= slow_ms:
                    self.logger.info(f"[PERF][SLOW_SQL] batch_time={elapsed_ms}ms, affected={affected_rows}, batches={len(params_list)}")

                # 详细SQL仅在开关开启时输出，且节流
                if _os.getenv('LOG_SQL_DETAIL', '0') == '1' and log_throttle('db-sql-detail', 2.0):
                    try:
                        # 仅采样前几条参数以避免日志膨胀
                        sample = params_list[:3] if params_list else []
                        redacted = [tuple(redact(str(v)) for v in p) for p in sample]
                        self.logger.debug(f"SQL(detail): {query} | sample_params={redacted} | {elapsed_ms}ms")
                    except Exception:
                        pass

                if log_throttle('db-batch-success', 1.0):
                    self.logger.debug(f"批量执行成功，影响 {affected_rows} 行")
                return affected_rows

        except Exception as e:
            self.logger.error(f"批量执行失败: {str(e)}")
            raise
    
    def table_exists(self, table_name: str) -> bool:
        """
        检查表是否存在
        
        Args:
            table_name: 表名
            
        Returns:
            bool: 表是否存在
        """
        query = """
            SELECT COUNT(*) as count 
            FROM sqlite_master 
            WHERE type='table' AND name=?
        """
        result = self.execute_query(query, (table_name,))
        return result[0]['count'] > 0

    def get_all_table_names(self) -> List[str]:
        """
        🔧 [P0修复] 获取数据库中所有表名

        Returns:
            List[str]: 表名列表
        """
        try:
            query = """
            SELECT name FROM sqlite_master
            WHERE type='table' AND name NOT LIKE 'sqlite_%'
            ORDER BY name
            """
            result = self.execute_query(query)
            return [row['name'] for row in result]

        except Exception as e:
            self.logger.error(f"🔧 [P0修复] 获取所有表名失败: {e}")
            return []

    def create_table(self, table_schema) -> bool:
        """
        🔧 [P0修复] 根据表结构定义创建表

        Args:
            table_schema: 表结构定义对象

        Returns:
            bool: 创建成功返回True
        """
        try:
            # 🔧 [P0修复] 构建CREATE TABLE语句 - 修复属性名不匹配问题
            columns_sql = []
            for column in table_schema.columns:
                # 🔧 [P0修复] 使用正确的属性名：column.type 而不是 column.data_type
                column_sql = f"{column.name} {column.type}"
                if not column.nullable:
                    column_sql += " NOT NULL"
                # 🔧 [P0修复] 使用正确的属性名：column.default 而不是 column.default_value
                if column.default is not None:
                    column_sql += f" DEFAULT {column.default}"
                # 🔧 [P0修复] ColumnDefinition没有auto_increment属性，需要特殊处理
                if hasattr(column, 'auto_increment') and column.auto_increment:
                    column_sql += " AUTOINCREMENT"
                elif column.name == 'id' and column.type == 'INTEGER':
                    # 对于id字段，默认添加AUTOINCREMENT
                    column_sql += " AUTOINCREMENT"
                columns_sql.append(column_sql)

            # 添加主键约束
            if table_schema.primary_key:
                pk_columns = ", ".join(table_schema.primary_key)
                columns_sql.append(f"PRIMARY KEY ({pk_columns})")

            create_sql = f"""
            CREATE TABLE IF NOT EXISTS {table_schema.table_name} (
                {", ".join(columns_sql)}
            )
            """

            self.execute_query(create_sql)

            # 创建索引
            for index in table_schema.indexes:
                index_columns = ", ".join(index["columns"])
                unique_clause = "UNIQUE" if index.get("unique", False) else ""
                index_sql = f"""
                CREATE {unique_clause} INDEX IF NOT EXISTS {index["name"]}
                ON {table_schema.table_name} ({index_columns})
                """
                self.execute_query(index_sql)

            self.logger.info(f"🔧 [P0修复] 表创建成功: {table_schema.table_name}")
            return True

        except Exception as e:
            self.logger.error(f"🔧 [P0修复] 表创建失败: {e}")
            return False

    def get_table_info(self, table_name: str) -> List[Dict[str, Any]]:
        """
        获取表结构信息
        
        Args:
            table_name: 表名
            
        Returns:
            List[Dict[str, Any]]: 表结构信息
        """
        query = f"PRAGMA table_info({table_name})"
        return self.execute_query(query)
    
    def get_database_size(self) -> int:
        """
        获取数据库文件大小（字节）
        
        Returns:
            int: 数据库文件大小
        """
        if self.db_path.exists():
            return self.db_path.stat().st_size
        return 0
    
    def vacuum_database(self) -> None:
        """执行数据库vacuum操作，压缩数据库"""
        try:
            with self.get_connection() as conn:
                conn.execute("VACUUM")
                conn.commit()
            self.logger.info("数据库vacuum操作完成")
        except Exception as e:
            self.logger.error(f"数据库vacuum操作失败: {str(e)}")
            raise
    
    def get_connection_status(self) -> Dict[str, Any]:
        """
        获取连接池状态信息
        
        Returns:
            Dict[str, Any]: 连接状态信息
        """
        with self._lock:
            active_connections = len(self._connections)
            connection_details = []
            
            for thread_id, info in self._connection_info.items():
                connection_details.append({
                    "thread_id": thread_id,
                    "created_at": info.created_at,
                    "last_accessed": info.last_accessed,
                    "age_seconds": time.time() - info.created_at,
                    "idle_seconds": time.time() - info.last_accessed
                })
            
            return {
                "database_path": str(self.db_path),
                "database_size_bytes": self.get_database_size(),
                "active_connections": active_connections,
                "connection_details": connection_details,
                "configuration": {
                    "connection_timeout": self.connection_timeout,
                    "backup_enabled": self.backup_enabled
                }
            }


# 全局数据库管理器实例
_database_manager: Optional[DatabaseManager] = None
_manager_lock = threading.Lock()


def get_database_manager() -> DatabaseManager:
    """
    获取全局数据库管理器实例（单例模式）
    
    Returns:
        DatabaseManager: 数据库管理器实例
    """
    global _database_manager
    
    if _database_manager is None:
        with _manager_lock:
            if _database_manager is None:
                _database_manager = DatabaseManager()
    
    return _database_manager

# 模块导出
__all__ = [
    "DatabaseManager",
    "ConnectionInfo",
    "get_database_manager"
]

if __name__ == "__main__":
    # 测试代码
    print("测试数据库管理模块...")
    
    # 创建数据库管理器
    db_mgr = DatabaseManager("test_database.db")
    
    # 测试连接
    with db_mgr.get_connection() as conn:
        cursor = conn.cursor()
        cursor.execute("SELECT sqlite_version()")
        version = cursor.fetchone()
        print(f"SQLite版本: {version[0]}")
    
    # 测试查询
    result = db_mgr.execute_query("SELECT * FROM system_config")
    print(f"系统配置记录数: {len(result)}")
    
    # 测试表检查
    exists = db_mgr.table_exists("system_config")
    print(f"system_config表存在: {exists}")
    
    # 获取连接状态
    status = db_mgr.get_connection_status()
    print(f"连接状态: {status}")
    
    # 关闭连接
    db_mgr.close_all_connections()
    
    print("数据库管理模块测试完成！") 