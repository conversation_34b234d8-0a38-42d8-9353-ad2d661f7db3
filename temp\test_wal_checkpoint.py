#!/usr/bin/env python3
"""
测试WAL checkpoint机制
"""
import sys
import os
import time
sys.path.insert(0, os.path.abspath('.'))

from src.modules.data_storage.database_manager import DatabaseManager
from src.modules.system_config.config_manager import Config<PERSON><PERSON><PERSON>

def test_wal_checkpoint():
    print("=== WAL Checkpoint 测试 ===")
    
    # 初始化管理器
    config_manager = ConfigManager()
    config_manager.load_config()
    db_manager = DatabaseManager(config_manager=config_manager)
    
    print("\n1. 检查当前journal模式:")
    try:
        result = db_manager.execute_query("PRAGMA journal_mode")
        if result:
            mode = result[0].get('journal_mode', 'unknown')
            print(f"   Journal模式: {mode}")
        else:
            print("   无法获取journal模式")
    except Exception as e:
        print(f"   错误: {e}")
    
    print("\n2. 检查WAL文件状态:")
    try:
        # 检查WAL文件信息
        result = db_manager.execute_query("PRAGMA wal_checkpoint(PASSIVE)")
        if result:
            print(f"   PASSIVE checkpoint结果: {result}")
        else:
            print("   PASSIVE checkpoint返回空")
    except Exception as e:
        print(f"   错误: {e}")
    
    print("\n3. 执行FULL checkpoint:")
    try:
        result = db_manager.execute_query("PRAGMA wal_checkpoint(FULL)")
        if result:
            print(f"   FULL checkpoint结果: {result}")
        else:
            print("   FULL checkpoint返回空")
    except Exception as e:
        print(f"   错误: {e}")
    
    print("\n4. 检查数据库文件状态:")
    try:
        db_path = db_manager.db_path
        print(f"   数据库文件: {db_path}")
        print(f"   文件存在: {db_path.exists()}")
        if db_path.exists():
            print(f"   文件大小: {db_path.stat().st_size / 1024:.2f} KB")
        
        # 检查WAL和SHM文件
        wal_path = db_path.with_suffix('.db-wal')
        shm_path = db_path.with_suffix('.db-shm')
        print(f"   WAL文件存在: {wal_path.exists()}")
        if wal_path.exists():
            print(f"   WAL文件大小: {wal_path.stat().st_size} bytes")
        print(f"   SHM文件存在: {shm_path.exists()}")
        if shm_path.exists():
            print(f"   SHM文件大小: {shm_path.stat().st_size} bytes")
    except Exception as e:
        print(f"   错误: {e}")
    
    print("\n5. 测试表查询一致性:")
    try:
        # 第一次查询
        query = "SELECT name FROM sqlite_master WHERE type='table' AND name LIKE 'salary_data_%'"
        result1 = db_manager.execute_query(query)
        print(f"   第一次查询: {len(result1)} 个表")
        
        # 执行checkpoint后再查询
        db_manager.execute_query("PRAGMA wal_checkpoint(FULL)")
        result2 = db_manager.execute_query(query)
        print(f"   checkpoint后查询: {len(result2)} 个表")
        
        # 检查结果一致性
        if len(result1) == len(result2):
            print("   ✅ 查询结果一致")
        else:
            print("   ❌ 查询结果不一致！")
            print(f"   第一次: {[r['name'] for r in result1]}")
            print(f"   第二次: {[r['name'] for r in result2]}")
    except Exception as e:
        print(f"   错误: {e}")
    
    print("\n6. 测试多连接一致性:")
    try:
        # 创建新的数据库管理器实例
        db_manager2 = DatabaseManager(config_manager=config_manager)
        
        # 同时查询
        result_conn1 = db_manager.execute_query(query)
        result_conn2 = db_manager2.execute_query(query)
        
        print(f"   连接1查询: {len(result_conn1)} 个表")
        print(f"   连接2查询: {len(result_conn2)} 个表")
        
        if len(result_conn1) == len(result_conn2):
            print("   ✅ 多连接结果一致")
        else:
            print("   ❌ 多连接结果不一致！")
    except Exception as e:
        print(f"   错误: {e}")

if __name__ == "__main__":
    test_wal_checkpoint()
