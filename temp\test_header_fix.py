#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试P0级问题修复：表头显示问题
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from src.gui.prototype.widgets.virtualized_expandable_table import VirtualizedExpandableTable

def test_empty_table_headers():
    """测试空表时中文表头显示"""
    print("=" * 60)
    print("测试P0修复：空表时中文表头显示")
    print("=" * 60)
    
    # 创建应用
    app = QApplication(sys.argv)
    
    # 创建主窗口
    window = QMainWindow()
    window.setWindowTitle("表头显示测试")
    window.resize(1200, 600)
    
    # 创建中心部件
    central_widget = QWidget()
    layout = QVBoxLayout()
    central_widget.setLayout(layout)
    window.setCentralWidget(central_widget)
    
    # 创建表格组件
    table = VirtualizedExpandableTable()
    layout.addWidget(table)
    
    # 测试场景1：设置中文表头的空表格
    print("\n测试场景1：设置中文表头的空表格")
    standard_headers = [
        "工号", "姓名", "部门名称", "人员类别代码", "人员类别",
        "2025年岗位工资", "2025年薪级工资", "津贴", "结余津贴",
        "2025年基础性绩效", "卫生费", "交通补贴", "物业补贴",
        "住房补贴", "车补", "通讯补贴", "2025年奖励性绩效预发",
        "补发", "借支", "应发工资", "2025公积金", "代扣代存养老保险"
    ]
    empty_data = []
    
    # 调用set_data方法
    table.set_data(empty_data, standard_headers, current_table_name="default_active_employees")
    
    # 验证表头设置
    actual_headers = []
    for col in range(table.horizontalHeader().count()):
        header_item = table.horizontalHeaderItem(col)
        if header_item:
            actual_headers.append(header_item.text())
    
    print(f"期望表头数量: {len(standard_headers)}")
    print(f"实际表头数量: {len(actual_headers)}")
    print(f"前5个表头: {actual_headers[:5] if actual_headers else '无'}")
    
    # 检查是否为中文表头
    if actual_headers:
        is_chinese = any('工号' in actual_headers[:1]) or any('姓名' in actual_headers[:2])
        if is_chinese:
            print("✅ 修复成功：表头显示为中文！")
        else:
            print("❌ 修复失败：表头未显示为中文")
            print(f"   实际显示: {actual_headers[:5]}")
    else:
        print("❌ 错误：未获取到表头")
    
    # 测试场景2：测试英文表头（对比测试）
    print("\n测试场景2：设置英文表头的空表格（对比测试）")
    english_headers = [
        "employee_id", "name", "department", "type_code", "type",
        "position_salary", "grade_salary", "allowance", "balance_allowance"
    ]
    
    table.set_data(empty_data, english_headers, current_table_name="test_table")
    
    # 验证英文表头
    actual_headers_2 = []
    for col in range(min(9, table.horizontalHeader().count())):
        header_item = table.horizontalHeaderItem(col)
        if header_item:
            actual_headers_2.append(header_item.text())
    
    print(f"英文表头测试: {actual_headers_2[:5]}")
    
    print("\n" + "=" * 60)
    print("测试总结：")
    if actual_headers and any('工号' in actual_headers[:1]) or any('姓名' in actual_headers[:2]):
        print("✅ P0问题修复成功：空表时正确显示中文表头")
    else:
        print("❌ P0问题修复失败：需要进一步调试")
    print("=" * 60)
    
    # 显示窗口
    window.show()
    
    # 运行应用（短时间后自动退出）
    from PyQt5.QtCore import QTimer
    QTimer.singleShot(2000, app.quit)
    app.exec_()

if __name__ == '__main__':
    test_empty_table_headers()