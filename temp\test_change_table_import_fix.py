#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""测试异动表导入修复效果"""

import sys
import os
import pandas as pd
from pathlib import Path

# 添加项目根目录到系统路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.modules.data_import.multi_sheet_importer import MultiSheetImporter
from src.modules.data_storage.database_manager import DatabaseManager
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager

def test_change_table_import():
    """测试异动表导入功能"""
    print("=" * 80)
    print("测试异动表导入功能修复效果")
    print("=" * 80)
    
    # 初始化管理器
    db_manager = DatabaseManager()
    table_manager = DynamicTableManager(db_manager)
    importer = MultiSheetImporter(table_manager)  # 只需要table_manager参数
    
    # Excel文件路径
    excel_file = r"E:\project\case\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls"
    
    print(f"\n1. 导入Excel文件: {excel_file}")
    print("   目标: 异动人员表")
    print("   年份: 2025, 月份: 12")
    
    # 执行导入 - 指定target_path参数
    result = importer.import_excel_file(
        file_path=excel_file,
        month="12",
        year=2025,
        target_path="异动人员表"  # 指定导入到异动表
    )
    
    print(f"\n2. 导入结果:")
    print(f"   成功: {result['success']}")
    print(f"   总记录数: {result.get('total_records', result.get('import_stats', {}).get('total_records', 0))}")
    
    if result['results']:
        print("\n3. 各Sheet导入详情:")
        for sheet_name, sheet_result in result['results'].items():
            print(f"\n   Sheet: {sheet_name}")
            print(f"   - 成功: {sheet_result['success']}")
            print(f"   - 表名: {sheet_result.get('table_name', 'N/A')}")
            print(f"   - 记录数: {sheet_result.get('records', 0)}")
            if not sheet_result['success']:
                print(f"   - 错误: {sheet_result.get('message', 'Unknown error')}")
    
    # 验证数据是否成功写入
    print("\n4. 验证数据库中的异动表:")
    try:
        # 查询所有异动表
        query = """
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name LIKE 'change_data_%'
            ORDER BY name
        """
        tables = db_manager.execute_query(query)
        
        if tables:
            print(f"   找到 {len(tables)} 个异动表:")
            for table in tables:
                table_name = table[0]
                # 查询表中的记录数
                count_query = f"SELECT COUNT(*) FROM {table_name}"
                count_result = db_manager.execute_query(count_query)
                count = count_result[0][0] if count_result else 0
                print(f"   - {table_name}: {count} 条记录")
                
                # 显示前3条记录的部分字段
                if count > 0:
                    sample_query = f"SELECT * FROM {table_name} LIMIT 3"
                    samples = db_manager.execute_query(sample_query)
                    if samples:
                        # 获取列名
                        cursor = db_manager.get_connection().cursor()
                        cursor.execute(f"PRAGMA table_info({table_name})")
                        columns = [col[1] for col in cursor.fetchall()]
                        
                        print(f"     表结构包含 {len(columns)} 个字段")
                        # 显示前10个字段名
                        print(f"     字段示例: {', '.join(columns[:10])}")
        else:
            print("   未找到任何异动表")
            
    except Exception as e:
        print(f"   验证失败: {e}")
    
    print("\n" + "=" * 80)
    print("测试完成!")
    print("=" * 80)
    
    return result['success']

if __name__ == "__main__":
    success = test_change_table_import()
    sys.exit(0 if success else 1)