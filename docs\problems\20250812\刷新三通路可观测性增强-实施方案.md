# 刷新三通路可观测性增强-实施方案（开发阶段日志优先版）

> 原则：开发阶段禁止日志降噪/节流；入口必打、关键状态必打、结果必打；严格按时间线复盘可还原完整链路。

## 目标
- 明确区分三类刷新入口（分页刷新/表格刷新/全局刷新），在日志中“一眼识别、可精确定位时刻与上下文”。
- 将分页状态写入（total_records/total_pages/current_page）纳入统一日志规范，携带 request_id 贯穿全链路。
- 为后续行为修复（原子更新/刷新令牌）提供必要可观测性基础。

## 必加埋点（INFO 级，绝不节流）

### 1) 入口埋点（统一携带 request_id）
- 分页刷新入口（已存在前缀，补充上下文字段）：
  - 日志示例：
    - 🔄[scope=page] 分页刷新被触发 | table=..., page=..., page_size=..., total_before=..., request_id=...
- 表格刷新入口（新增）：
  - 日志示例：
    - 🔁[scope=table] 表格刷新被触发 | table=..., path=..., page=..., page_size=..., total_before=..., request_id=...
- 全局刷新入口（已有，补充 request_id）：
  - 日志示例：
    - 🔧[scope=global] 全局刷新被触发 | request_id=..., reason=manual_click

生成 request_id：入口处生成（时间戳/UUID），贯穿该次刷新产生的每条日志。

### 2) 分页状态写入点（统一格式）
- 所有 set_total_records / set_current_page / set_page_size / set_pagination_state / batch_update（后续会有）调用处，输出：
  - 写入来源（source=function/path）
  - 旧值→新值（old_total→new_total / old_page→new_page / old_size→new_size）
  - 推导字段（expected_total_pages/new_total_pages）
  - request_id
- 日志示例：
  - 📊[pagination-write] set_total_records | source=set_data(non_pagination) | old=1396 → new=50 | request_id=...
  - 📊[pagination-write] batch_update | page=1, size=50, total=1396 | pages=28 | request_id=...

### 3) 数据事件/服务层关键点
- table_data_service 发布 data_updated 前：
  - 📨[data_event] publish | table=..., rows=..., current_page=..., page_size=..., total=..., request_id=...
- UI 侧 _on_new_data_updated/_on_pagination_data_loaded 接收后：
  - 📥[data_event] received | table=..., rows=..., current_page=..., page_size=..., total=..., request_id=...

### 4) 刷新令牌（后续实现）相关日志
- 入口生成 refresh_token：
  - 🔑[refresh-token] generated | request_id=..., token=...
- 写入前校验 token：
  - 🧰[refresh-token] apply | token=... | valid=True/False | action=discard/apply

## 状态校验与一致性日志
- 每次写入后立刻打印：
  - ✅[pagination-state] now | page=X / pages=Y / total=Z | request_id=...
- 若计算页数不一致/页码溢出：
  - ⚠️[pagination-verify] mismatch | expected_pages=..., actual_pages=... | action=recalc | request_id=...

## 验证用例（按时间线复盘）
- 用例 A：启动后点“分页刷新”（无表场景）
  - 期望：入口日志出现 → 拒绝执行 + 原因 → 无任何 set_total_records 发生。
- 用例 B：导入后点“表格刷新”
  - 期望：入口日志出现 → 产生请求 request_id → 随后的每一步写入都带同一个 request_id
- 用例 C：连续点“全局刷新”
  - 期望：多个 request_id 并列；后续实现令牌后，旧请求写入被明确“discard”。

## 后续与依赖
- 该文档仅为日志实施方案；需配合《分页状态一致性修复-原子更新与刷新令牌-实施方案》完成行为修复。

## Mermaid 流程图
```mermaid
flowchart TD
    A[用户点击刷新] --> B{哪种刷新?}
    B -->|分页刷新| C[生成request_id + 打入口日志]
    B -->|表格刷新| D[生成request_id + 打入口日志]
    B -->|全局刷新| E[生成request_id + 打入口日志]
    C --> F[后续每次写入打印 pagination-write + request_id]
    D --> F
    E --> F
    F --> G[接收数据事件打印 data_event 日志]
    G --> H[校验并打印 pagination-state/verify]
```

## Mermaid 时序图
```mermaid
sequenceDiagram
    participant U as 用户
    participant UI as UI层
    participant TDS as 数据服务

    U->>UI: 点击任一刷新
    UI->>UI: 生成request_id并记录入口日志
    UI->>TDS: 拉取数据(如需)
    TDS-->>UI: 返回数据+total（可选）
    UI->>UI: 每次写入分页状态都记录 pagination-write(+request_id)
    UI->>UI: 写入后记录 pagination-state / verify
```

