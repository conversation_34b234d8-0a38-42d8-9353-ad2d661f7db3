#!/usr/bin/env python3
"""
调试数据库表查询问题
"""
import sys
import os
sys.path.insert(0, os.path.abspath('.'))

from src.modules.data_storage.database_manager import DatabaseManager
from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
from src.modules.system_config.config_manager import ConfigManager

def debug_table_query():
    print("=== 数据库表查询调试 ===")

    # 初始化管理器
    config_manager = ConfigManager()
    config_manager.load_config()
    db_manager = DatabaseManager(config_manager=config_manager)
    table_manager = DynamicTableManager(db_manager, config_manager)
    
    print("\n1. 直接查询sqlite_master:")
    try:
        query = "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%' ORDER BY name"
        tables = db_manager.execute_query(query)
        print(f"   找到 {len(tables)} 个表:")
        for table in tables:
            print(f"     - {table['name']}")
    except Exception as e:
        print(f"   错误: {e}")
    
    print("\n2. 查询table_metadata:")
    try:
        query = "SELECT table_name, table_type FROM table_metadata WHERE table_type='salary_data'"
        metadata = db_manager.execute_query(query)
        print(f"   找到 {len(metadata)} 个salary_data类型的表:")
        for meta in metadata:
            print(f"     - {meta['table_name']} ({meta['table_type']})")
    except Exception as e:
        print(f"   错误: {e}")
    
    print("\n3. 使用DynamicTableManager.get_table_list():")
    try:
        tables = table_manager.get_table_list(table_type='salary_data')
        print(f"   找到 {len(tables)} 个salary_data类型的表:")
        for table in tables:
            print(f"     - {table}")
    except Exception as e:
        print(f"   错误: {e}")
    
    print("\n4. 检查数据库就绪状态:")
    try:
        ready = table_manager._ensure_database_ready()
        print(f"   数据库就绪: {ready}")
    except Exception as e:
        print(f"   错误: {e}")
    
    print("\n5. 检查数据库连接:")
    try:
        conn_ok = table_manager._check_database_connection()
        print(f"   数据库连接: {conn_ok}")
    except Exception as e:
        print(f"   错误: {e}")
    
    print("\n6. 检查WAL模式:")
    try:
        wal_ok = table_manager._check_wal_mode_status()
        print(f"   WAL模式: {wal_ok}")
    except Exception as e:
        print(f"   错误: {e}")
    
    print("\n7. 检查表元数据存在:")
    try:
        meta_exists = table_manager._check_table_metadata_exists('salary_data')
        print(f"   表元数据存在: {meta_exists}")
    except Exception as e:
        print(f"   错误: {e}")
    
    print("\n8. 执行表查询降级:")
    try:
        tables = table_manager._execute_table_query_with_fallback()
        print(f"   降级查询找到 {len(tables)} 个表:")
        for table in tables[:5]:  # 只显示前5个
            print(f"     - {table}")
        if len(tables) > 5:
            print(f"     ... 还有 {len(tables) - 5} 个表")
    except Exception as e:
        print(f"   错误: {e}")

if __name__ == "__main__":
    debug_table_query()
