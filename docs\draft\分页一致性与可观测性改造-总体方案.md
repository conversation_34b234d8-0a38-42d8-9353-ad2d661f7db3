# 分页一致性与可观测性改造-总体方案

## 目标
- 端到端 request_id 贯通：入口→服务层→事件→UI写入
- 写入原子化：统一使用 batch_update，杜绝中间态
- 风险隔离：refresh_token 丢弃旧请求写入
- 全局刷新阶段 start/complete 完整打点

## 实施要点
- UI 入口（分页/页大小/表格/全局）生成 request_id，并打印入口日志
- TableDataService.load_table_data 增参 request_id 并用于 DataUpdatedEvent.metadata.request_id
- cached_load 事件也携带 request_id（若无则生成 SV-*-C）
- _on_new_data_updated/_on_pagination_data_loaded 使用 pagination_widget.batch_update 原子写入
- set_data 非分页分支禁止写 total=len(current_page)，无真实 total 时跳过写入
- refresh_token：维护最新 request_id，旧请求写入直接丢弃
- 全局刷新 _execute_global_state_refresh 增加 precheck_start/precheck_complete/global_refresh_complete

## 日志口径
- 入口：📍[scope] | table=... | page/size/... | request_id=...
- 发布：📨[data_event] publish | table | rows | page | size | total | req_type | request_id
- 接收：📥[data_event] received | table | request_id
- 写入：📊[pagination-write] batch_update | src | rid | total/page/size/pages old->new
- 最终：✅[pagination-state] now | page/pages/total | rid
- 丢弃：🧰[refresh-token] discard | table | rid(old)

## 验收标准
- 任一 request_id 链可复盘：入口→发布→接收→写入→最终
- 刷新/分页/排序后 UI 无中间态或回退态
- _verify_refresh_results 判断成功；日志包含 global_refresh_complete

