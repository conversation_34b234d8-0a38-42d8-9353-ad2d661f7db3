#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试P1修复：验证导入后表名是否正确更新
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_table_name_update():
    """测试表名更新功能"""
    print("=" * 80)
    print("P1修复测试：验证导入后表名更新")
    print("=" * 80)
    
    # 模拟主窗口的表名更新逻辑
    class MockMainWindow:
        def __init__(self):
            from loguru import logger
            self.logger = logger
            self.current_table_name = "default_table"  # 初始默认值
            self.current_nav_path = []
            
        def _generate_table_name_from_path(self, path_list):
            """从路径生成表名"""
            if len(path_list) >= 4:
                # 例如: ['工资表', '2025年', '08月', 'A岗职工']
                year = path_list[1].replace('年', '')
                month = path_list[2].replace('月', '').zfill(2)
                category = path_list[3]
                
                # 映射类别到英文
                category_map = {
                    '全部在职人员': 'active_employees',
                    'A岗职工': 'a_grade_employees',
                    '非A岗职工': 'non_a_grade_employees',
                    '退休人员': 'retired_employees',
                    '离休人员': 'pension_employees'
                }
                
                category_eng = category_map.get(category, category)
                table_name = f"salary_data_{year}_{month}_{category_eng}"
                return table_name
            return "unknown_table"
            
        def simulate_import_success(self, target_path_list):
            """模拟导入成功后的处理"""
            print(f"\n模拟导入到路径: {' > '.join(target_path_list)}")
            
            # 生成表名
            table_name = self._generate_table_name_from_path(target_path_list)
            print(f"生成的表名: {table_name}")
            
            # 原始逻辑（未修复）
            old_table_name = self.current_table_name
            print(f"修复前的current_table_name: {old_table_name}")
            
            # P1修复：导入成功后更新表名
            self.current_table_name = table_name
            self.logger.info(f"[P1修复] 导入成功后更新表名: {table_name}")
            
            print(f"修复后的current_table_name: {self.current_table_name}")
            
            return old_table_name, self.current_table_name
    
    # 执行测试
    mock_window = MockMainWindow()
    
    # 测试场景1：导入A岗职工数据
    print("\n" + "=" * 80)
    print("测试场景1：导入A岗职工数据")
    print("=" * 80)
    
    path1 = ['工资表', '2025年', '08月', 'A岗职工']
    old1, new1 = mock_window.simulate_import_success(path1)
    
    if old1 == "default_table" and new1 == "salary_data_2025_08_a_grade_employees":
        print("[PASS] 表名从default_table正确更新为具体表名")
    else:
        print(f"[FAIL] 表名更新不正确: {old1} -> {new1}")
    
    # 测试场景2：导入全部在职人员数据
    print("\n" + "=" * 80)
    print("测试场景2：导入全部在职人员数据")
    print("=" * 80)
    
    path2 = ['工资表', '2025年', '09月', '全部在职人员']
    old2, new2 = mock_window.simulate_import_success(path2)
    
    if old2 == "salary_data_2025_08_a_grade_employees" and new2 == "salary_data_2025_09_active_employees":
        print("[PASS] 表名从旧表名正确更新为新导入的表名")
    else:
        print(f"[FAIL] 表名更新不正确: {old2} -> {new2}")
    
    # 测试场景3：刷新数据时更新表名
    print("\n" + "=" * 80)
    print("测试场景3：刷新数据时更新表名")
    print("=" * 80)
    
    def _refresh_current_data_display(self, table_name):
        """模拟刷新数据显示"""
        print(f"刷新数据: {table_name}")
        
        # P1修复：刷新数据时更新当前表名
        if table_name and table_name != "default_table":
            self.current_table_name = table_name
            self.logger.info(f"[P1修复] 刷新数据时更新表名: {table_name}")
    
    # 绑定方法到mock对象
    import types
    mock_window._refresh_current_data_display = types.MethodType(_refresh_current_data_display, mock_window)
    
    # 重置为default_table
    mock_window.current_table_name = "default_table"
    mock_window._refresh_current_data_display("salary_data_2025_10_retired_employees")
    
    if mock_window.current_table_name == "salary_data_2025_10_retired_employees":
        print("[PASS] 刷新数据时表名正确更新")
    else:
        print(f"[FAIL] 刷新数据时表名未更新: {mock_window.current_table_name}")
    
    # 最终验证
    print("\n" + "=" * 80)
    all_pass = (
        new1 == "salary_data_2025_08_a_grade_employees" and
        new2 == "salary_data_2025_09_active_employees" and
        mock_window.current_table_name == "salary_data_2025_10_retired_employees"
    )
    
    if all_pass:
        print("[SUCCESS] P1修复验证成功！导入后表名正确更新")
    else:
        print("[ERROR] P1修复仍有问题，需要进一步调试")
    print("=" * 80)
    
    return all_pass

if __name__ == "__main__":
    success = test_table_name_update()
    sys.exit(0 if success else 1)