"""
测试导航面板修复效果
验证以下修复：
1. Tab内容不再重叠
2. 导航卡片底部对齐
3. 内容自适应功能
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PyQt5.QtWidgets import QApplication
from src.gui.prototype.prototype_main_window import PrototypeMainWindow

def test_navigation_fixes():
    """测试导航面板修复"""
    app = QApplication(sys.argv)
    
    # 创建主窗口
    window = PrototypeMainWindow()
    window.show()
    
    print("✅ 修复已应用，请检查以下内容：")
    print("1. Tab切换时内容不再重叠")
    print("2. 左侧导航卡片底部与右侧对齐")
    print("3. 窗口缩放时内容自适应")
    print("\n测试步骤：")
    print("- 切换'工资表'和'异动表'Tab，检查是否还有重叠")
    print("- 查看左侧导航底部是否与右侧分页区对齐")
    print("- 调整窗口大小，查看导航内容是否自适应")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    test_navigation_fixes()